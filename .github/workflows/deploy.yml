# yaml-language-server: $schema=https://json.schemastore.org/github-workflow.json

name: Deploy

on:
  workflow_dispatch:
    inputs:
      build-ios:
        description: 'Build iOS'
        required: false
        type: boolean
        default: true
      build-android:
        description: 'Build Android'
        required: false
        type: boolean
        default: true
      version-number:
        description: 'Version number to use, leave blank to automatically set.'
        required: false
        type: string
        default: ''
jobs:
  deploy:
    name: Deploy
    runs-on: ubuntu-latest
    timeout-minutes: 15

    steps:
      - name: Detect next version
        if: ${{ inputs.version-number == '' }}
        uses: release-drafter/release-drafter@v6
        id: version
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Start iOS Deploy
        if: ${{ inputs.build-ios }}
        uses: codemagic-ci-cd/trigger-codemagic-workflow-action@v2.0.0
        with:
          app-id: '${{ vars.CODEMAGIC_APP_ID }}'
          workflow-id: deploy_ios
          token: '${{ secrets.CODEMAGIC_API_TOKEN }}'
        env:
          CM_NEXT_VERSION: "${{ inputs.version-number != '' && inputs.version-number || steps.version.outputs.resolved_version }}"

      - name: Start Android Deploy
        if: ${{ inputs.build-android }}
        uses: codemagic-ci-cd/trigger-codemagic-workflow-action@v2.0.0
        with:
          app-id: '${{ vars.CODEMAGIC_APP_ID }}'
          workflow-id: deploy_android
          token: '${{ secrets.CODEMAGIC_API_TOKEN }}'
        env:
          CM_NEXT_VERSION: "${{ inputs.version-number != '' && inputs.version-number || steps.version.outputs.resolved_version }}"
