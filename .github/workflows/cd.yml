name: CD

on:
  push:
    branches: [main]
  release:
    types: [published]

env:
  MELOS_SDK_PATH: auto

jobs:
  update-release-draft:
    name: Update Release Draft
    runs-on: ubuntu-latest
    steps:
      - uses: release-drafter/release-drafter@v6
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  ci:
    name: CI
    uses: ./.github/workflows/ci.yml

  deploy-dev:
    name: Deploy to Dev
    if: startsWith(github.ref, 'refs/tags/v') != true
    needs: [ci]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: kuhnroyal/flutter-fvm-config-action@v3.1
        id: fvm-config-action
      - uses: subosito/flutter-action@v2.19.0
        with:
          flutter-version: ${{ steps.fvm-config-action.outputs.FLUTTER_VERSION }}
          channel: ${{ steps.fvm-config-action.outputs.FLUTTER_CHANNEL }}

      - name: <PERSON><PERSON>tra<PERSON>
        run: |
          dart pub global activate melos
          melos bootstrap --enforce-lockfile

      - name: Build Done app on web for development
        run: melos run build:done_dev:web

      # Deploy to dev config
      - uses: FirebaseExtended/action-hosting-deploy@v0
        with:
          repoToken: '${{ secrets.GITHUB_TOKEN }}'
          firebaseServiceAccount: '${{ secrets.FIREBASE_SERVICE_ACCOUNT_DONE_DEV_F0434 }}'
          projectId: dev
          channelId: live

  deploy-prod:
    name: Deploy to Production
    if: startsWith(github.ref, 'refs/tags/v')
    needs: [ci]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Get the version
        id: get_version
        run: echo ::set-output name=VERSION::${GITHUB_REF#refs/tags/v}

      - uses: kuhnroyal/flutter-fvm-config-action@v3.1
        id: fvm-config-action

      - uses: subosito/flutter-action@v2.19.0
        with:
          flutter-version: ${{ steps.fvm-config-action.outputs.FLUTTER_VERSION }}
          channel: ${{ steps.fvm-config-action.outputs.FLUTTER_CHANNEL }}

      - name: Melos Bootstrap
        run: |
          dart pub global activate melos
          melos bootstrap --enforce-lockfile

      - name: Build Done app on web for production
        run: melos run build:done:web

      # Deploy to prod config
      - uses: FirebaseExtended/action-hosting-deploy@v0
        with:
          repoToken: '${{ secrets.GITHUB_TOKEN }}'
          firebaseServiceAccount: '${{ secrets.FIREBASE_SERVICE_ACCOUNT_DONE_50549 }}'
          projectId: prod
          channelId: live

      # Setup release and upload sourcemaps
      - uses: getsentry/action-release@v3.1.1
        env:
          SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
          SENTRY_ORG: ${{ secrets.SENTRY_ORG }}
        with:
          environment: "production"
          sourcemaps: "apps/done_flutter/build/web/main.dart.js.map"
          ignore_missing: true
          ignore_empty: true
          version: "${{ steps.get_version.outputs.VERSION }}"
          projects: "done-flutter"
          
