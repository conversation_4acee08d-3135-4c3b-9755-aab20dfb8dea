name: CI

on:
  pull_request:
    branches: [main]
  workflow_call:

env:
  MELOS_SDK_PATH: auto

jobs:
  build:
    name: Build
    runs-on: ubuntu-latest
    timeout-minutes: 15

    steps:
      - uses: actions/checkout@v4
      - uses: kuhnroyal/flutter-fvm-config-action@v3.1
        id: fvm-config-action
      - uses: subosito/flutter-action@v2.19.0
        with:
          flutter-version: ${{ steps.fvm-config-action.outputs.FLUTTER_VERSION }}
          channel: ${{ steps.fvm-config-action.outputs.FLUTTER_CHANNEL }}

      - name: Cache Dependencies
        uses: actions/cache@v4
        with:
          path: ${{ env.PUB_CACHE }}
          key: ${{ runner.os }}-${{ hashFiles('.fvmrc') }}-${{ hashFiles('apps/done_flutter/pubspec.lock') }}

      - name: Melos Bootstrap
        run: |
          dart pub global activate melos
          melos bootstrap --enforce-lockfile

      - name: Check formatting
        run: melos run check-format

      - name: Analyze
        run: melos run analyze

      - name: Test
        run: melos run test
  preview:
    name: Preview
    if: github.event_name == 'pull_request' && github.actor != 'dependabot[bot]'
    runs-on: ubuntu-latest
    timeout-minutes: 15
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Get Flutter version
        uses: kuhnroyal/flutter-fvm-config-action@v3.1
        id: fvm-config-action

      - name: Setup Flutter
        uses: subosito/flutter-action@v2.19.0
        with:
          flutter-version: ${{ steps.fvm-config-action.outputs.FLUTTER_VERSION }}
          channel: ${{ steps.fvm-config-action.outputs.FLUTTER_CHANNEL }}

      - name: Cache Dependencies
        uses: actions/cache@v4
        with:
          path: ${{ env.PUB_CACHE }}
          key: ${{ runner.os }}-${{ hashFiles('.fvmrc') }}-${{ hashFiles('apps/done_flutter/pubspec.lock') }}

      - name: Melos Bootstrap
        run: |
          dart pub global activate melos
          melos bootstrap --enforce-lockfile

      - name: Build Done app on web for development
        run: melos run build:done_dev:web

      - name: Publish
        uses: FirebaseExtended/action-hosting-deploy@v0
        with:
          repoToken: '${{ secrets.GITHUB_TOKEN }}'
          firebaseServiceAccount: '${{ secrets.FIREBASE_SERVICE_ACCOUNT_DONE_DEV_F0434 }}'
          projectId: dev
