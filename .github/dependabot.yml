version: 2

updates:
  # Top level packages like Melos
  - package-ecosystem: "pub"
    directory: "/"
    schedule:
      interval: "weekly"

  - package-ecosystem: 'github-actions'
    directory: '/'
    schedule:
      interval: 'weekly'
    reviewers:
      - 'fwal'

  # # Done app
  # - package-ecosystem: "pub"
  #   directory: "/apps/done_flutter"
  #   schedule:
  #     interval: "weekly"
  #   groups:
  #     firebase:
  #       patterns:
  #       - "firebase_*"
  #       - "cloud_*"

  # # Auth feature
  # - package-ecosystem: "pub"
  #   directory: "/features/done_auth"
  #   schedule:
  #     interval: "weekly"
  #   groups:
  #     firebase:
  #       patterns:
  #       - "firebase_*"
  #       - "cloud_*"

  # # Booking feature
  # - package-ecosystem: "pub"
  #   directory: "/features/done_booking"
  #   schedule:
  #     interval: "weekly"
  #   groups:
  #     firebase:
  #       patterns:
  #       - "firebase_*"
  #       - "cloud_*"

  # # Deductions feature
  # - package-ecosystem: "pub"
  #   directory: "/features/done_deductions"
  #   schedule:
  #     interval: "weekly"
  #   groups:
  #     firebase:
  #       patterns:
  #       - "firebase_*"
  #       - "cloud_*"

  # # Game feature
  # - package-ecosystem: "pub"
  #   directory: "/features/done_game"
  #   schedule:
  #     interval: "weekly"
  #   groups:
  #     firebase:
  #       patterns:
  #       - "firebase_*"
  #       - "cloud_*"

  # # Analytics package
  # - package-ecosystem: "pub"
  #   directory: "/packages/done_analytics"
  #   schedule:
  #     interval: "weekly"
  #   groups:
  #     firebase:
  #       patterns:
  #       - "firebase_*"
  #       - "cloud_*"

  # # Auth package
  # - package-ecosystem: "pub"
  #   directory: "/packages/done_auth"
  #   schedule:
  #     interval: "weekly"
  #   groups:
  #     firebase:
  #       patterns:
  #       - "firebase_*"
  #       - "cloud_*"

  # # Booking package
  # - package-ecosystem: "pub"
  #   directory: "/packages/done_booking"
  #   schedule:
  #     interval: "weekly"
  #   groups:
  #     firebase:
  #       patterns:
  #       - "firebase_*"
  #       - "cloud_*"

  # # Calls package
  # - package-ecosystem: "pub"
  #   directory: "/packages/done_calls"
  #   schedule:
  #     interval: "weekly"
  #   groups:
  #     firebase:
  #       patterns:
  #       - "firebase_*"
  #       - "cloud_*"

  # # Database package
  # - package-ecosystem: "pub"
  #   directory: "/packages/done_database"
  #   schedule:
  #     interval: "weekly"
  #   groups:
  #     firebase:
  #       patterns:
  #       - "firebase_*"
  #       - "cloud_*"

  # # Game package
  # - package-ecosystem: "pub"
  #   directory: "/packages/done_game"
  #   schedule:
  #     interval: "weekly"
  #   groups:
  #     firebase:
  #       patterns:
  #       - "firebase_*"
  #       - "cloud_*"

  # # Image package
  # - package-ecosystem: "pub"
  #   directory: "/packages/done_image"
  #   schedule:
  #     interval: "weekly"
  #   groups:
  #     firebase:
  #       patterns:
  #       - "firebase_*"
  #       - "cloud_*"

  # # Lints package
  # - package-ecosystem: "pub"
  #   directory: "/packages/done_lints"
  #   schedule:
  #     interval: "weekly"
  #   groups:
  #     firebase:
  #       patterns:
  #       - "firebase_*"
  #       - "cloud_*"

  # # Localisation package
  # - package-ecosystem: "pub"
  #   directory: "/packages/done_localizations"
  #   schedule:
  #     interval: "weekly"
  #   groups:
  #     firebase:
  #       patterns:
  #       - "firebase_*"
  #       - "cloud_*"

  # # Maps package
  # - package-ecosystem: "pub"
  #   directory: "/packages/done_maps"
  #   schedule:
  #     interval: "weekly"
  #   groups:
  #     firebase:
  #       patterns:
  #       - "firebase_*"
  #       - "cloud_*"

  # # Models package
  # - package-ecosystem: "pub"
  #   directory: "/packages/done_models"
  #   schedule:
  #     interval: "weekly"
  #   groups:
  #     firebase:
  #       patterns:
  #       - "firebase_*"
  #       - "cloud_*"

  # # Router package
  # - package-ecosystem: "pub"
  #   directory: "/packages/done_router"
  #   schedule:
  #     interval: "weekly"
  #   groups:
  #     firebase:
  #       patterns:
  #       - "firebase_*"
  #       - "cloud_*"
