{"version": "0.2.1", "configurations": [{"name": "Done Local", "type": "dart", "request": "launch", "program": "apps/done_flutter/lib/main_local.dart", "args": ["--flavor", "local"]}, {"name": "<PERSON>", "type": "dart", "request": "launch", "program": "apps/done_flutter/lib/main_dev.dart", "args": ["--flavor", "dev"]}, {"name": "Done Dev Release", "type": "dart", "request": "launch", "program": "apps/done_flutter/lib/main_dev.dart", "args": ["--flavor", "dev", "--release"]}, {"name": "Done Prod", "request": "launch", "type": "dart", "program": "apps/done_flutter/lib/main.dart", "args": ["--flavor", "prod"]}, {"name": "Done Prod Release", "request": "launch", "type": "dart", "program": "apps/done_flutter/lib/main.dart", "args": ["--flavor", "prod", "--release"]}, {"name": "Done Prod Profile", "request": "launch", "type": "dart", "flutterMode": "profile", "program": "apps/done_flutter/lib/main.dart", "args": ["--flavor", "prod"]}, {"name": "Done Ops", "request": "launch", "type": "dart", "program": "apps/done_ops/lib/main.dart"}, {"name": "Widgetbook", "request": "launch", "type": "dart", "program": "widgetbook/lib/main.dart"}]}