// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options_dev.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DevFirebaseOptions {
  static FirebaseOptions? get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return null;
      case TargetPlatform.iOS:
        return null;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBjtVUJtmu5ChdHnqeoq-n-tSR1xSDmUug',
    appId: '1:704360355803:web:46c20c372f0d2dc0ebb9fe',
    messagingSenderId: '704360355803',
    projectId: 'done-dev-f0434',
    authDomain: 'done-dev-f0434.firebaseapp.com',
    databaseURL: 'https://done-dev-f0434.firebaseio.com',
    storageBucket: 'done-dev-f0434.appspot.com',
    measurementId: 'G-YGT7ZR72EQ',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBsax8oVFpr6UHN8-cyzClygHYx0JpS97s',
    appId: '1:704360355803:android:25bf6c8612273f69',
    messagingSenderId: '704360355803',
    projectId: 'done-dev-f0434',
    databaseURL: 'https://done-dev-f0434.firebaseio.com',
    storageBucket: 'done-dev-f0434.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBRLQ0XQ8zJhVjhFW5M_0CZiyBmMjS9K0g',
    appId: '1:704360355803:ios:4c263c7adf85219a',
    messagingSenderId: '704360355803',
    projectId: 'done-dev-f0434',
    databaseURL: 'https://done-dev-f0434.firebaseio.com',
    storageBucket: 'done-dev-f0434.appspot.com',
    androidClientId:
        '704360355803-7obv10gkk72sa17l6vp0gfmve182o68d.apps.googleusercontent.com',
    iosClientId:
        '704360355803-n2bloi5eb4i8cb5gr2blvaa5svkrrj1o.apps.googleusercontent.com',
    iosBundleId: 'co.doneservices.app.dev',
  );
}
