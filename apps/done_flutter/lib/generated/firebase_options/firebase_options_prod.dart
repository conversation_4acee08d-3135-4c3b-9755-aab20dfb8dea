// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options_prod.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class ProdFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyCrxQidn5-tQYDtWQQcSFU0gFuf_JzuEl0',
    appId: '1:865533511519:web:31426346b150e266e8d8ea',
    messagingSenderId: '865533511519',
    projectId: 'done-50549',
    authDomain: 'done-50549.firebaseapp.com',
    databaseURL: 'https://done-50549.firebaseio.com',
    storageBucket: 'done-50549.appspot.com',
    measurementId: "G-XCWXN0NHQ9",
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyB1PNq7nmA0J8cupYmstael3q5CSGHBkGM',
    appId: '1:865533511519:android:2a9c67327e976253',
    messagingSenderId: '865533511519',
    projectId: 'done-50549',
    databaseURL: 'https://done-50549.firebaseio.com',
    storageBucket: 'done-50549.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCkz1xuK2-Wf1FM0tT97t8laXpXQbAne8s',
    appId: '1:865533511519:ios:d2572ba5b43dd184',
    messagingSenderId: '865533511519',
    projectId: 'done-50549',
    databaseURL: 'https://done-50549.firebaseio.com',
    storageBucket: 'done-50549.appspot.com',
    androidClientId:
        '865533511519-2jrq5o7me9crg41546ejsjpgpc8e6r4v.apps.googleusercontent.com',
    iosClientId:
        '865533511519-6elpfdhrsaaag2q1sikvmdu68dmce5pl.apps.googleusercontent.com',
    iosBundleId: 'co.doneservices.app',
  );
}
