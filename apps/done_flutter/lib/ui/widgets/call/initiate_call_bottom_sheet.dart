import 'package:done_analytics/done_analytics.dart';
import 'package:done_calls/done_calls.dart';
import 'package:done_auth/done_auth.dart';
import 'package:done_image/done_image.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/cupertino.dart';
import 'package:done_localizations/done_localizations.dart';

class InititateCallBottomSheet extends StatelessWidget {
  const InititateCallBottomSheet({
    super.key,
    required this.job,
    required this.callee,
    required this.source,
  });

  final Job? job;
  final Callee callee;
  final String source;

  @override
  Widget build(BuildContext context) {
    final isCustomer =
        context.authState.isUserCustomer() && !context.authState.isSuperUser();
    return DoneBottomSheet(
      title: Text(
        '${S.of(context).callAction} ${callee.name} ${isCustomer ? '${S.of(context).at} ${job?.companyName}' : ''}',
      ),
      subtitle: Text(S.of(context).initiateCallPrompt),
      content: [
        DoneButton(
          centered: false,
          image: Image.asset(
            color: context.doneColors.uiOnPrimary,
            ImageAssets.phone,
            height: 20,
            width: 20,
          ),
          style: DoneButtonStyle.neutral,
          title: Text(S.of(context).audioCall),
          onPressed: () {
            CallManagerProvider.of(context).initiateCall(
              toUser: callee.reference,
              withVideo: false,
              jobRef: job?.documentReference,
            );
            Navigator.of(context).pop();
            EventLogger.instance.logEvent('initiating_call', {
              'source': source,
              'withVideo': 0,
            });
          },
        ),
        const VerticalMargin.medium(),
        DoneButton(
          centered: false,
          image: Image.asset(
            color: context.doneColors.uiOnPrimary,
            ImageAssets.videocall,
            height: 20,
            width: 20,
          ),
          style: DoneButtonStyle.neutral,
          title: Text(S.of(context).videoCall),
          onPressed: () {
            CallManagerProvider.of(context).initiateCall(
              toUser: callee.reference,
              withVideo: true,
              jobRef: job?.documentReference,
            );
            Navigator.of(context).pop();
            EventLogger.instance.logEvent('initiating_call', {
              'source': source,
              'withVideo': 1,
            });
          },
        ),
        const VerticalMargin.medium(),
        DoneButton(
          style: DoneButtonStyle.secondary,
          title: Text(S.of(context).cancel),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
      ],
    );
  }
}
