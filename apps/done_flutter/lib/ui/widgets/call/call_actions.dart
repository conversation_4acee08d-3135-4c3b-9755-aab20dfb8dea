import 'package:done_analytics/done_analytics.dart';
import 'package:done_calls/done_calls.dart';
import 'package:done_auth/done_auth.dart';
import 'package:done_flutter/core/services/chat_service.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_flutter/ui/pages/call/video.dart'
    show OnMinimize, OpacityNotifier;
import 'package:done_flutter/ui/widgets/buttons/call_action_button.dart';
import 'package:done_image/done_image.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

class CallActionsSheet extends StatefulWidget {
  const CallActionsSheet({
    super.key,
    required this.call,
    this.onMinimize,
    this.isOutgoing = false,
    this.animationOpacity,
  });

  final VideoCall call;
  final OnMinimize? onMinimize;
  final bool isOutgoing;
  final OpacityNotifier? animationOpacity;

  @override
  _CallActionsSheetState createState() => _CallActionsSheetState();
}

class _CallActionsSheetState extends State<CallActionsSheet> {
  final double _bottomSheetContainerHeight = 140;
  double _animatedPositionedTop = 0;

  @override
  Widget build(BuildContext context) {
    final trueBottomSheetHeight =
        _bottomSheetContainerHeight + MediaQuery.of(context).padding.bottom;
    final upperPaddingWithSafeArea = 20 + MediaQuery.of(context).padding.top;

    return StreamBuilder<bool>(
      stream: widget.call.isFullScreen,
      initialData: widget.call.isFullScreen.value,
      builder: (context, isFullScreen) {
        _animatedPositionedTop =
            (isFullScreen.data ?? false) ? (trueBottomSheetHeight * 2) : 0;
        return _buildButtonsStack(
          trueBottomSheetHeight,
          upperPaddingWithSafeArea,
          isFullScreen.data,
        );
      },
    );
  }

  Widget _buildButtonsStack(
    double trueBottomSheetHeight,
    double upperPaddingWithSafeArea,
    bool? isFullScreen,
  ) {
    return Stack(
      children: <Widget>[
        _buildMinimizeButton(upperPaddingWithSafeArea, isFullScreen),
        AnimatedPositioned(
          width: MediaQuery.of(context).size.width,
          height: MediaQuery.of(context).size.height,
          duration: const Duration(milliseconds: 300),
          top: _animatedPositionedTop,
          child: Stack(
            children: <Widget>[
              if (!kIsWeb) _buildSnapshotButton(trueBottomSheetHeight),
              if (!kIsWeb) _buildTorchButton(trueBottomSheetHeight),
              _buildHangupButton(context, trueBottomSheetHeight),
              _buildBottomSheetContainer(context, trueBottomSheetHeight),
              _buildBottomActions(trueBottomSheetHeight),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildMinimizeButton(
    double upperPaddingWithSafeArea,
    bool? isFullScreen,
  ) {
    final hidden = (widget.onMinimize == null || (isFullScreen ?? false));
    return Positioned(
      left: 20,
      top: upperPaddingWithSafeArea,
      child: AnimatedOpacity(
        opacity: hidden ? 0 : 1,
        duration: const Duration(milliseconds: 200),
        child: Container(
          padding: const EdgeInsets.all(2),
          decoration: BoxDecoration(
            color: context.doneColors.uiPrimary.withValues(alpha: 0.6),
            shape: BoxShape.circle,
          ),
          child: IconButton(
            icon: Image.asset(
              ImageAssets.minimize,
              color: context.doneColors.uiBlack,
              width: 20,
              height: 20,
            ),
            onPressed: widget.onMinimize,
          ),
        ),
      ),
    );
  }

  Widget _buildSnapshotButton(double trueBottomSheetHeight) {
    final isCustomer = context.authState.isUserCustomer();
    late Widget child;
    if (widget.isOutgoing) {
      child = CallSnapshotButton(
        label: S.of(context).videoTakePicture,
        onTap: null,
      );
    } else {
      final isCameraEnabledStream =
          isCustomer
              ? widget.call.isCameraEnabled
              : widget.call.isRemoteCameraEnabled;
      child = StreamBuilder<bool>(
        stream: isCameraEnabledStream,
        initialData: isCameraEnabledStream.value,
        builder:
            (context, isCameraEnabled) => CallSnapshotButton(
              label: S.of(context).videoTakePicture,
              onTap:
                  (isCameraEnabled.data ?? false)
                      ? () => takeSnapshot(context)
                      : null,
            ),
      );
    }
    return Positioned(
      bottom: trueBottomSheetHeight + 20,
      left: 20,
      child: child,
    );
  }

  Widget _buildHangupButton(
    BuildContext context,
    double trueBottomSheetHeight,
  ) {
    return Positioned(
      bottom: trueBottomSheetHeight + 20,
      right: 20,
      child: EndCallButton(
        label: S.of(context).end,
        onTap: () => widget.call.hangup(),
      ),
    );
  }

  Widget _buildBottomSheetContainer(
    BuildContext context,
    double trueBottomSheetHeight,
  ) {
    return Positioned(
      bottom: 0,
      left: 0,
      child: Container(
        height: trueBottomSheetHeight,
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          color: Color.fromRGBO(34, 34, 34, 0.60),
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(8),
            topRight: Radius.circular(8),
          ),
        ),
      ),
    );
  }

  Widget _buildBottomActions(double trueBottomSheetHeight) {
    return Positioned(
      bottom: trueBottomSheetHeight - 120,
      left: 20,
      right: 20,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          if (!kIsWeb) ...[_buildAudioButton(), const Spacer()],
          _buildMicrophoneButton(),
          const Spacer(),
          _buildCameraButton(),
          if (!kIsWeb) ...[const Spacer(), _buildTurnCameraButton()],
        ],
      ),
    );
  }

  Widget _buildTorchButton(double trueBottomSheetHeight) {
    return Positioned(
      bottom: trueBottomSheetHeight + 20,
      left: 20 + 82,
      child: StreamBuilder<bool>(
        stream: widget.call.hasTorch,
        initialData: widget.call.hasTorch.value,
        builder: (context, hasTorch) {
          return StreamBuilder<bool>(
            stream: widget.call.isTorchOn,
            initialData: widget.call.isTorchOn.value,
            builder: (context, isTorchOn) {
              return CallActionButton(
                title: S.of(context).flashlight,
                hasOpaqueBackground: false,
                iconAsset: ImageAssets.flashlight,
                isSelected: isTorchOn.data!,
                onTap:
                    hasTorch.data!
                        ? () => widget.call.setTorch(!isTorchOn.data!)
                        : null,
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildMicrophoneButton() {
    return StreamBuilder<bool>(
      stream: widget.call.isMuted,
      initialData: widget.call.isMuted.value,
      builder: (context, isMuted) {
        return CallActionButton(
          title: S.of(context).soundOff,
          iconAsset: ImageAssets.unmute,
          isSelected: isMuted.data!,
          onTap: () {
            widget.call.setMuted(!isMuted.data!);
          },
        );
      },
    );
  }

  Widget _buildAudioButton() {
    return StreamBuilder<bool>(
      stream: widget.call.isSpeakerPhoneOn,
      initialData: widget.call.isSpeakerPhoneOn.value,
      builder: (context, isSpeakerPhoneOn) {
        return CallActionButton(
          title: S.of(context).loudspeaker,
          iconAsset: ImageAssets.speakerphone,
          isSelected: isSpeakerPhoneOn.data!,
          onTap: () {
            widget.call.setSpeakerPhoneOn(
              !isSpeakerPhoneOn.data!,
              override: true,
            );
          },
        );
      },
    );
  }

  Widget _buildCameraButton() {
    return StreamBuilder<bool>(
      stream: widget.call.isCameraEnabled,
      initialData: widget.call.isCameraEnabled.value,
      builder: (context, isCameraEnabled) {
        return CallActionButton(
          title: S.of(context).cameraOff,
          iconAsset: ImageAssets.cameraOff,
          isSelected: !isCameraEnabled.data!,
          onTap: () => widget.call.setCameraEnabled(!isCameraEnabled.data!),
        );
      },
    );
  }

  Widget _buildTurnCameraButton() {
    return StreamBuilder<bool>(
      stream: widget.call.isCameraEnabled,
      initialData: widget.call.isCameraEnabled.value,
      builder: (context, isCameraEnabled) {
        return CallActionButton(
          title: S.of(context).turn,
          iconAsset: ImageAssets.cameraRotate,
          onTap:
              isCameraEnabled.data!
                  ? () async {
                    widget.animationOpacity?.updateOpacity();
                    // await Future<void>.delayed(
                    //     const Duration(milliseconds: OpacityNotifier.opacityDuration));
                    await widget.call.switchCamera();
                  }
                  : null,
        );
      },
    );
  }

  Future<void> takeSnapshot(BuildContext context) async {
    final file = await widget.call.captureFrame(
      isUserCustomer: context.authState.isUserCustomer(),
    );
    if (file == null) return;
    final job = await widget.call.getJobReference();

    await EventLogger.instance.logEvent('capturedVideoSnapshot');
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text(S.of(context).videoImageSaved)));
    await Chat(job).sendItem(
      ChatItem(file, ChatItemType.image),
      currentUser: context.authState.user!.documentReference,
    );
  }
}
