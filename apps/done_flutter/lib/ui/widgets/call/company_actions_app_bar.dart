import 'package:done_flutter/ui/pages/master_detail/layout_adaptive_app_bar.dart';
import 'package:done_flutter/ui/widgets/call/call_app_bar_button.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

/// A version of AppBar that includes actions for making calls
class CompanyActionsAppBar extends StatelessWidget
    implements PreferredSizeWidget {
  const CompanyActionsAppBar({
    required this.job,
    required this.customer,
    required this.title,
    required this.context,
    this.actions,
  });

  final Job job;
  final User customer;
  final Widget title;
  final BuildContext context;

  /// If actions are null, call actions would be shown
  final List<Widget>? actions;

  @override
  Widget build(BuildContext context) {
    final paddedActions =
        (actions != null && actions!.isNotEmpty)
            ? actions!
                .map<Widget>(
                  (e) => Padding(
                    padding: const EdgeInsets.symmetric(
                      vertical: Margins.small,
                    ),
                    child: e,
                  ),
                )
                .separatedBy(HorizontalMargin.medium)
            : null;
    paddedActions?.add(const HorizontalMargin.medium());

    return LayoutAdaptiveAppBar(
      title: title,
      actions: paddedActions ?? _buildCallActions(),
    );
  }

  List<Widget> _buildCallActions() {
    return [
      if (!kIsWeb && customer.lastLoggedIn != null && job.company != null)
        CallAppBarButton(context: context, job: job),
    ];
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
