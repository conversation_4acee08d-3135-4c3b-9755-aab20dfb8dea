import 'package:done_auth/done_auth.dart';
import 'package:done_calls/done_calls.dart';
import 'package:done_flutter/core/services/video_call_service.dart';
import 'package:done_image/done_image.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:done_localizations/done_localizations.dart';

/// Use in an App Bar to show a call button to call
/// customer or company depending on logged in user.
///
/// Calls on [VideoCall] to start call on pressed.
class CallAppBarButton extends StatelessWidget {
  const CallAppBarButton({
    required this.context,
    required this.job,
    this.isWideLayout = false,
    super.key,
  });

  final BuildContext context;
  final Job job;
  final bool isWideLayout;

  @override
  Widget build(BuildContext context) {
    final title = Row(
      children: [
        Image.asset(
          ImageAssets.phone,
          height: 24,
          width: 24,
          color: context.doneColors.uiAlwaysPureWhite,
        ),
        const HorizontalMargin(margin: 6),
        Text(
          S.of(context).callAction,
          style: Theme.of(context).textTheme.labelMedium!.copyWith(
            color: context.doneColors.uiAlwaysPureWhite,
          ),
        ),
      ],
    );

    if (isWideLayout) {
      return DoneButton(title: title, onPressed: action(context));
    }
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 11),
      child: SizedBox(
        height: 34,
        child: DoneActionChip(title: title, onPressed: action(context)),
      ),
    );
  }

  VoidCallback? action(BuildContext context) {
    final userType = context.authState.getUserType();
    final canCall =
        !CallManagerProvider.of(context).hasCall &&
        job.isCommunicationAllowedFor(userType);
    if (!canCall) return null;
    void onPressed() {
      initiateCall(job, context, source: 'AppBarCallButton');
    }

    return onPressed;
  }
}
