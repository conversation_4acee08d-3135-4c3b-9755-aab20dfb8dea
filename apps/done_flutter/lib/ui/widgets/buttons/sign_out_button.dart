import 'package:done_localizations/done_localizations.dart';
import 'package:done_flutter/ui/widgets/profile/profile_dialogs.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

class SignOutButton extends StatelessWidget {
  const SignOutButton({super.key});

  @override
  Widget build(BuildContext context) {
    return DoneButton(
      key: const Key('SignOutButton'),
      style: DoneButtonStyle.negative,
      onPressed: () => signOutDialog(context),
      title: Text(S.of(context).profileSignOut),
    );
  }
}
