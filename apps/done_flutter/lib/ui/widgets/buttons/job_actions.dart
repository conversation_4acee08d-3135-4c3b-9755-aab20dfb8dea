import 'package:done_auth/done_auth.dart';
import 'package:done_database/done_database.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_flutter/ui/widgets/call/call_app_bar_button.dart';
import 'package:done_flutter/ui/widgets/dialogs/duplicate_job_dialog.dart';
import 'package:done_flutter/ui/widgets/jobs/job_close_actions.dart';
import 'package:done_models/done_models.dart';
import 'package:done_router/done_router.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/widgets.dart';

List<Widget> jobActions({required BuildContext context, required Job job}) {
  final isWideLayout = MediaQuery.of(context).size.width >= Layouts.wideLayout;
  final isCraftsman = context.authState.isUserCraftsman();

  return [
    if (!job.isClosed) CloseJobButton(job: job),
    if (isCraftsman && job.network.id == donePartnerId)
      DuplicateJobButton(job: job),
    if (isWideLayout)
      CallAppBarButton(context: context, job: job, isWideLayout: isWideLayout),
  ];
}

class CloseJobButton extends StatelessWidget {
  const CloseJobButton({super.key, required this.job});

  final Job job;

  @override
  Widget build(BuildContext context) {
    final isCustomerUser = context.authState.isUserCustomer();

    return DoneButton(
      title: Text(
        isCustomerUser && !job.isMarkedAsDone
            ? S.of(context).cancelProject
            : S.of(context).closeProject,
      ),
      style:
          job.isMarkedAsDone
              ? DoneButtonStyle.neutral
              : DoneButtonStyle.negative,
      onPressed: () async {
        showCloseJobDialog(
          context: context,
          isJobDone: job.isMarkedAsDone,
          onClose: (JobCloseReason closeReason, {String? otherText}) {
            Mutations.instance
                .job(job.id)
                .close(
                  JobCloseEventEntry(
                    closedBy: context.authState.user!.documentReference,
                    closeType:
                        isCustomerUser
                            ? JobCloseEventType.customerClosedProject
                            : JobCloseEventType.companyClosedProject,
                    closeReason: closeReason,
                    closeOtherReason: otherText,
                  ),
                );
            Future.delayed(const Duration(milliseconds: 500), () {
              if (context.mounted) context.router.pop();
            });
          },
        );
      },
    );
  }
}

class DuplicateJobButton extends StatelessWidget {
  const DuplicateJobButton({super.key, required this.job});

  final Job job;

  @override
  Widget build(BuildContext context) {
    return DoneButton(
      title: Text(S.of(context).createNewProject),
      style: DoneButtonStyle.neutral,
      onPressed: () async {
        showDuplicateJobDialog(context: context, job: job);
      },
    );
  }
}
