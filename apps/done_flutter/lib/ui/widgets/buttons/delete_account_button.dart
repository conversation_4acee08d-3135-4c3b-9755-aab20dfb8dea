import 'package:done_auth/done_auth.dart';
import 'package:done_database/done_database.dart';

import 'package:done_localizations/done_localizations.dart';
import 'package:done_flutter/ui/widgets/profile/profile_dialogs.dart';
import 'package:done_flutter/utils/helpers/launch_url.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:get_it/get_it.dart';

/// A button for showing delete account dialog
///
/// This button is only enabled if the user hasn't posted a request before
class DeleteAccountButton extends StatelessWidget {
  const DeleteAccountButton({super.key});

  @override
  Widget build(BuildContext context) {
    final userId = context.authState.user!.documentReference.id;
    return StreamBuilder<bool>(
      stream: GetIt.instance<UserRepository>().hasRequestedAccountDeletion(
        userId,
      ),
      builder: (context, snapshot) {
        final hasRequested = snapshot.hasData && snapshot.data!;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            DoneAsyncAction(
              action:
                  !hasRequested ? () => showDeleteAccountDialog(context) : null,
              builder:
                  (context, actionOrNull, isLoading) => TextButton(
                    onPressed: actionOrNull,
                    child:
                        isLoading
                            ? const DoneAdaptiveLoadingIndicator()
                            : Text(
                              S.of(context).deleteAccount,
                              style: Theme.of(
                                context,
                              ).textTheme.titleSmall!.copyWith(
                                color:
                                    hasRequested
                                        ? Theme.of(context)
                                            .extension<DoneCustomColors>()!
                                            .uiBlack
                                            .withValues(alpha: 0.5)
                                        : context.doneColors.uiBlack,
                              ),
                            ),
                  ),
            ),
            if (hasRequested) ...[
              const VerticalMargin.medium(),
              MultilineMarkdownBody(
                styleSheet: MarkdownStyleSheet(
                  a: Theme.of(context).textTheme.labelMedium!.apply(
                    color: context.doneColors.purple,
                  ),
                  p: Theme.of(context).textTheme.bodyMedium,
                ),
                data: S.of(context).accountDeletionInfo,
                onTapLink: (_, email, __) => launchUrl('mailto:$email'),
              ),
              const VerticalMargin.medium(),
            ],
          ],
        );
      },
    );
  }
}
