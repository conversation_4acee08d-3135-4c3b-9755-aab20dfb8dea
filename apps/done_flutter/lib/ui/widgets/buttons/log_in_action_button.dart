import 'package:done_auth/done_auth.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

class LoginActionButton extends StatelessWidget {
  const LoginActionButton({super.key, this.noDecoration = false});

  final bool noDecoration;

  @override
  Widget build(BuildContext context) {
    return DoneButton(
      title: Text(S.of(context).loginSignIn),
      style:
          noDecoration ? DoneButtonStyle.noDecoration : DoneButtonStyle.primary,
      onPressed: () {
        showModalBottomSheet<void>(
          context: context,
          isScrollControlled: true,
          isDismissible: false,
          enableDrag: false,
          backgroundColor: Colors.transparent,
          builder:
              (modalContext) => DoneModal(
                child: LoginPage(
                  onLogin: () => Navigator.of(modalContext).pop(),
                ),
                barrierColor: Colors.transparent,
              ),
        );
      },
    );
  }
}
