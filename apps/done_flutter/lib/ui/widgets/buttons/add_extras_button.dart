import 'package:done_flutter/invoicing/widgets/show_invoice_generator_dialog.dart';
import 'package:done_flutter/ui/pages/company/shared_generator/generator_type.dart';
import 'package:done_flutter/ui/widgets/contact_support_snackbar.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_flutter/utils/dialogs/quote_dialogs.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

/// A button to add extras (additions) to a partner job
///
/// When clicked it opens a generator based on [GeneratorType] with articles locked to a certain partnership
class AddExtrasButton extends StatelessWidget {
  const AddExtrasButton({
    required this.originalContext,
    required this.job,
    required this.title,
    required this.generatorType,
    this.style = DoneButtonStyle.secondary,
    super.key,
  });
  final BuildContext originalContext;
  final Job job;
  final DoneButtonStyle style;
  final Widget title;
  final GeneratorType generatorType;

  @override
  Widget build(BuildContext context) {
    return DoneAsyncAction(
      action: () async {
        Navigator.of(context).pop();
        return _showArticlesGenerator(context);
      },
      builder:
          (context, actionOrNull, isLoading) => DoneButton(
            style: style,
            title: isLoading ? const DoneAdaptiveLoadingIndicator() : title,
            onPressed: actionOrNull,
          ),
    );
  }

  Future<void> _showArticlesGenerator(BuildContext context) async {
    final articleGroup = await pickArticleGroup(originalContext, job);
    if (articleGroup == null) return;
    if (generatorType == GeneratorType.quote) {
      return showQuoteGenerator(
        context: originalContext,
        job: job,
        articleGroup: articleGroup,
      );
    } else if (generatorType == GeneratorType.invoice) {
      return showInvoiceGenerator(
        context: originalContext,
        job: job,
        articleGroup: articleGroup,
      );
    }
  }
}

Future<JobArticleGroup?> pickArticleGroup(BuildContext context, Job job) async {
  if (!job.hasArticleGroups || job.articleGroups!.isEmpty) {
    final exceptionMessage =
        'Trying to show articles for job ${job.id} which has no article groups';
    showContactSupportSnackbar(context, exceptionMessage: exceptionMessage);
    return null;
  }

  JobArticleGroup? articleGroup;

  if (job.articleGroups!.length > 1) {
    final result = await pickAmongChoices<JobArticleGroup>(
      title: Text(S.of(context).selectPriceList),
      context: context,
      choices: job.articleGroups!,
      choiceTitleBuilder: (context, choice) => Text(choice.title),
      cancelButtonText: Text(S.of(context).cancel),
    );
    articleGroup = result?.choice;
  } else {
    articleGroup = job.articleGroups!.first;
  }

  return articleGroup;
}
