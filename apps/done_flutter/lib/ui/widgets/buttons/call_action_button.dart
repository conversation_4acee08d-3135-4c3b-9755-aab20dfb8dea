import 'package:done_image/done_image.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

class CallActionButton extends StatelessWidget {
  const CallActionButton({
    super.key,
    required this.title,
    required this.iconAsset,
    this.hasOpaqueBackground = true,
    this.isSelected = false,
    this.onTap,
  });

  final String title;
  final String iconAsset;
  final bool isSelected;
  final bool hasOpaqueBackground;

  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: <Widget>[
        if (title.contains("\n")) const VerticalMargin.large(),
        _buildIcon(context),
        if (hasOpaqueBackground) const VerticalMargin(margin: 6),
        if (hasOpaqueBackground)
          Text(
            title,
            textAlign: TextAlign.center,
            maxLines: 2,
            style: Theme.of(
              context,
            ).textTheme.bodySmall!.copyWith(color: Colors.white),
          )
        else
          _BackgroundedLabel(label: title),
      ],
    );
  }

  Widget _buildIcon(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        customBorder: const CircleBorder(),
        onTap: onTap,
        child: Container(
          decoration: BoxDecoration(
            color:
                isSelected
                    ? context.doneColors.uiPrimary
                    : (onTap != null)
                    ? const Color(0xff787878).withValues(alpha: 0.80)
                    : Colors.white12.withAlpha(15),
            shape: BoxShape.circle,
          ),
          height: 64,
          width: 64,
          child: Center(
            child: Image.asset(
              iconAsset,
              height: 35,
              width: 35,
              color:
                  isSelected
                      ? context.doneColors.uiBlack
                      : (onTap != null)
                      ? context.doneColors.uiPrimary
                      : Colors.white30,
            ),
          ),
        ),
      ),
    );
  }
}

class EndCallButton extends StatelessWidget {
  const EndCallButton({super.key, required this.label, this.onTap});

  final String label;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: <Widget>[
        Material(
          color: Colors.transparent,
          child: InkWell(
            customBorder: const CircleBorder(),
            onTap: onTap,
            child: Container(
              decoration: BoxDecoration(
                color: context.doneColors.hangupCallRed,
                shape: BoxShape.circle,
              ),
              height: 64,
              width: 64,
              child: Center(
                child: Image.asset(
                  ImageAssets.close,
                  height: 50,
                  width: 50,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ),
        _BackgroundedLabel(label: label),
      ],
    );
  }
}

class CallSnapshotButton extends StatelessWidget {
  const CallSnapshotButton({
    super.key,
    required this.label,
    required this.onTap,
  });

  final String label;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: <Widget>[
        InkWell(
          onTap: onTap,
          child: Stack(
            alignment: Alignment.center,
            children: <Widget>[
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(90),
                  border: Border.all(
                    width: 3,
                    color: onTap != null ? Colors.white : Colors.white38,
                  ),
                ),
                height: 64,
                width: 64,
              ),
              Container(
                decoration: const BoxDecoration(
                  color: Colors.transparent,
                  shape: BoxShape.circle,
                ),
                height: 58,
                width: 58,
              ),
              Container(
                decoration: BoxDecoration(
                  color: onTap != null ? Colors.white : Colors.white38,
                  shape: BoxShape.circle,
                ),
                height: 52,
                width: 52,
              ),
            ],
          ),
        ),
        _BackgroundedLabel(label: label),
      ],
    );
  }
}

class _BackgroundedLabel extends StatelessWidget {
  const _BackgroundedLabel({required this.label});

  final String label;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const VerticalMargin(margin: 9),
        DecoratedBox(
          decoration: BoxDecoration(
            color: context.doneColors.darkOverlayOpacity,
            borderRadius: const BorderRadius.all(Radius.circular(4)),
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 3, horizontal: 4),
            child: Center(
              child: Text(
                label,
                textAlign: TextAlign.center,
                style: Theme.of(
                  context,
                ).textTheme.bodySmall!.copyWith(color: Colors.white),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
