import 'package:done_flutter/ui/widgets/dialogs/send_quote_dialog.dart';
import 'package:done_flutter/utils/extensions/enums/quote_creation_type.dart';
import 'package:done_flutter/utils/extensions/models/job.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/cupertino.dart';

class SendQuoteButton extends StatelessWidget {
  const SendQuoteButton({super.key, required this.job});

  final Job job;

  @override
  Widget build(BuildContext context) {
    return DoneButton(
      style:
          job.isOfferSent ? DoneButtonStyle.neutral : DoneButtonStyle.secondary,
      title: Text(job.createQuoteTitle(context)),
      onPressed: () async {
        if (job.quoteCreationTypes.length == 1) {
          return job.quoteCreationTypes.first.quoteAction(context, job);
        }
        return showAdaptivePopup<void>(
          builder:
              (dialogContext) =>
                  SendQuoteDialog(job: job, originalContext: context),
          context: context,
        );
      },
    );
  }
}
