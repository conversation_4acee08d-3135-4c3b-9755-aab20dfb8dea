import 'package:device_calendar/device_calendar.dart';
import 'package:done_auth/done_auth.dart';
import 'package:done_flutter/utils/calendar_util.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class AddCalendarButton extends StatelessWidget {
  const AddCalendarButton({
    super.key,
    required this.job,
    required this.deviceCalendarPlugin,
    required this.scheduleType,
  });

  final ScheduleType scheduleType;
  final Job job;
  final DeviceCalendarPlugin deviceCalendarPlugin;

  @override
  Widget build(BuildContext context) {
    final currentUserId = context.authState.user!.documentReference.id;
    DateTime startTime;
    if (scheduleType == ScheduleType.videoCall) {
      startTime = job.scheduledCallTime!.toDate();
    } else {
      startTime = job.scheduledWorkStartTime!.toDate();
    }
    final calendarUtils = CalendarUtils(
      job: job,
      deviceCalendarPlugin: deviceCalendarPlugin,
      scheduleType: scheduleType,
      scheduledTime: startTime,
    );
    return job.hasCurrentUserCalendarEvent(currentUserId, scheduleType)
        ? DoneButton(
          title: Text(S.of(context).removeFromCalendar),
          style: DoneButtonStyle.neutral,
          onPressed: () => calendarUtils.removeEvent(context),
        )
        : DoneButton(
          title: Text(S.of(context).addToCalendar),
          style: DoneButtonStyle.neutral,
          onPressed: () => calendarUtils.promptAddToCalendar(context),
        );
  }
}
