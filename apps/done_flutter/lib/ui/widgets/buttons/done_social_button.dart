import 'package:done_router/done_router.dart';
import 'package:flutter/material.dart';

class DoneSocialButton extends StatelessWidget {
  const DoneSocialButton({super.key, required this.url, required this.image});

  final String url;
  final String image;

  @override
  Widget build(BuildContext context) {
    return IconButton(
      iconSize:
          32, //iconSize gets within material3 overwritten by the image asset size.
      alignment: Alignment.center,
      icon: Image.asset(image, height: 32),
      onPressed: () => URLRouter.instance.handleUrl(url, context: context),
    );
  }
}
