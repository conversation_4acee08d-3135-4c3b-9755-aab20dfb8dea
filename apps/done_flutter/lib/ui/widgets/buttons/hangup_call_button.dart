import 'package:done_image/done_image.dart';
import 'package:flutter/material.dart';

class HangupCallButton extends StatelessWidget {
  const HangupCallButton({super.key, required this.onHangup});

  final VoidCallback onHangup;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onHangup,
      child: Container(
        decoration: const BoxDecoration(
          color: Color(0xffF54040),
          shape: BoxShape.circle,
        ),
        height: 64,
        width: 64,
        child: Center(child: Image.asset(ImageAssets.close, height: 35)),
      ),
    );
  }
}
