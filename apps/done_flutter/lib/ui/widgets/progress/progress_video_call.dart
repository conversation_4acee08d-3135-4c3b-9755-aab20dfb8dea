import 'package:device_calendar/device_calendar.dart';
import 'package:done_auth/done_auth.dart';
import 'package:done_calls/done_calls.dart';
import 'package:done_flutter/core/services/video_call_service.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_flutter/ui/widgets/buttons/add_calendar_button.dart';
import 'package:done_flutter/ui/widgets/dialogs/schedule_call_dialog.dart';
import 'package:done_flutter/ui/widgets/select_date_and_time.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:done_flutter/ui/widgets/jobs/job_progress_item.dart';
import 'package:done_image/done_image.dart';
import 'package:flutter/material.dart';

import 'package:flutter/scheduler.dart';

class ProgressVideoCall extends StatefulWidget {
  const ProgressVideoCall({super.key, required this.job});

  final Job job;

  @override
  State<ProgressVideoCall> createState() => _ProgressVideoCallState();
}

class _ProgressVideoCallState extends State<ProgressVideoCall> {
  bool _isReminderDisplayed = false;

  @override
  Widget build(BuildContext context) {
    final isUserCraftsman = context.authState.isUserCraftsman();
    final deviceCalendarPlugin = DeviceCalendarPlugin();
    final scheduledCallTime = widget.job.scheduledCallTime;

    var videoCallState = VideoCallState.waiting;
    var subTitle =
        isUserCraftsman
            ? S.of(context).noScheduledCallCraftsmanInfo
            : S.of(context).noScheduledCallCustomerInfo;

    if (widget.job.events.callSkipped != null ||
        (isUserCraftsman &&
            !ScheduleCallDialog.shouldShow(context, widget.job))) {
      final timestamp = widget.job.events.callSkipped;
      subTitle =
          timestamp == null
              ? ''
              : TimeFormatter.getHumanReadableDate(timestamp.toDate(), context);
      videoCallState = VideoCallState.skipped;
    }

    if (scheduledCallTime != null) {
      subTitle = TimeFormatter.getHumanReadableDate(
        scheduledCallTime.toDate(),
        context,
      );
      videoCallState = VideoCallState.scheduled;
    }

    if (widget.job.events.scheduledCallCancelled != null &&
        scheduledCallTime == null) {
      subTitle =
          isUserCraftsman
              ? S.of(context).scheduleCallCanceledCraftsmanInfo
              : S.of(context).scheduleCallCanceledCustomerInfo;
      videoCallState = VideoCallState.scheduledCallCancelled;
    }

    if (widget.job.isCallMade) {
      final timestamp = widget.job.events.callMade;
      subTitle = TimeFormatter.getHumanReadableDate(
        timestamp?.toDate(),
        context,
      );
      videoCallState = VideoCallState.callMade;
    }

    if (!_isReminderDisplayed &&
        ScheduleCallDialog.shouldShow(context, widget.job)) {
      _isReminderDisplayed = true;
      SchedulerBinding.instance.addPostFrameCallback(
        (_) => showDialog<void>(
          context: context,
          builder:
              (_) =>
                  ScheduleCallDialog(job: widget.job, originalContext: context),
        ),
      );
    }
    return JobProgressItem(
      image: Image.asset(
        ImageAssets.videocall,
        scale: 3,
        color: videoCallState.iconColor(context),
      ),
      state: videoCallState.jobState,
      children: [
        Text(
          videoCallState.title(context),
          style: Theme.of(context).textTheme.titleLarge,
        ),
        if (subTitle.isNotEmpty)
          Text(
            subTitle,
            style: Theme.of(context).textTheme.bodyLarge!.apply(
              color: context.doneColors.typographyMediumContrast,
            ),
          ),
        if (videoCallState == VideoCallState.scheduled) ...[
          const VerticalMargin.medium(),
          ..._buildCallButton(widget.job, context),
          AddCalendarButton(
            job: widget.job,
            deviceCalendarPlugin: deviceCalendarPlugin,
            scheduleType: ScheduleType.videoCall,
          ),
          const VerticalMargin.small(),
          DoneButton(
            title: Text(S.of(context).cancel),
            style: DoneButtonStyle.negative,
            onPressed: () => _showCancelBookingDialog(context),
          ),
        ] else if (isUserCraftsman &&
            widget.job.isInteractive &&
            videoCallState != VideoCallState.callMade)
          ..._buildScheduleVideoCallButtonForCompanies(
            context,
            widget.job,
            videoCallState,
          ),
      ],
    );
  }

  List<Widget> _buildScheduleVideoCallButtonForCompanies(
    BuildContext context,
    Job job,
    VideoCallState state,
  ) {
    return [
      const VerticalMargin.small(),
      DoneButton(
        style:
            state == VideoCallState.skipped
                ? DoneButtonStyle.neutral
                : DoneButtonStyle.secondary,
        onPressed: () {
          selectDateAndTime(context, job, ScheduleType.videoCall);
        },
        title: Text(S.of(context).scheduleVideoCall),
      ),
      const VerticalMargin.small(),
    ];
  }

  List<Widget> _buildCallButton(Job job, BuildContext context) {
    final now = DateTime.now();
    final scheduled = job.scheduledCallTime!.toDate();

    final differenceInHours = now.difference(scheduled).inHours;
    final isWithinOneHour = differenceInHours <= 1 && differenceInHours >= -1;
    final userType = context.authState.getUserType();
    final canCall =
        !CallManagerProvider.of(context).hasCall &&
        job.isCommunicationAllowedFor(userType);
    // show if it is within one hour from scheduled
    if (isWithinOneHour) {
      return [
        DoneButton(
          style: DoneButtonStyle.positive,
          onPressed:
              canCall
                  ? () {
                    initiateCall(job, context, source: 'ScheduledCallButton');
                  }
                  : null,
          title: Text(S.of(context).callAction),
        ),
        const VerticalMargin.small(),
      ];
    }

    return [];
  }

  void _showCancelBookingDialog(BuildContext context) {
    showDialog<void>(
      context: context,
      builder:
          (BuildContext context) => CancelScheduledCallDialog(job: widget.job),
    );
  }
}

enum VideoCallState {
  waiting,
  scheduled,
  skipped,
  callMade,
  scheduledCallCancelled,
}

extension VideoCallStateValues on VideoCallState {
  Color iconColor(BuildContext context) {
    switch (this) {
      case VideoCallState.waiting:
      case VideoCallState.scheduled:
      case VideoCallState.skipped:
        return context.doneColors.uiBlack;
      case VideoCallState.callMade:
      case VideoCallState.scheduledCallCancelled:
        return context.doneColors.uiPrimary;
    }
  }

  JobProgressState get jobState {
    switch (this) {
      case VideoCallState.waiting:
      case VideoCallState.skipped:
        return JobProgressState.uninitialized;
      case VideoCallState.scheduled:
        return JobProgressState.ongoing;
      case VideoCallState.scheduledCallCancelled:
        return JobProgressState.cancelled;
      case VideoCallState.callMade:
        return JobProgressState.done;
    }
  }

  String title(BuildContext context) {
    switch (this) {
      case VideoCallState.waiting:
        return S.of(context).jobVideoCallStateTitleWaiting;
      case VideoCallState.scheduled:
        return S.of(context).jobVideoCallStateTitleScheduled;
      case VideoCallState.callMade:
        return S.of(context).jobVideoCallStateTitleMade;
      case VideoCallState.scheduledCallCancelled:
        return S.of(context).cancelScheduledCallProgress;
      case VideoCallState.skipped:
        return S.of(context).callSkipped;
    }
  }
}
