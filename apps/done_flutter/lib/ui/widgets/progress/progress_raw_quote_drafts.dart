import 'package:done_database/done_database.dart';

import 'package:done_flutter/ui/widgets/cards/raw_quote_draft_card.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';

class ProgressRawQuoteDrafts extends StatelessWidget {
  const ProgressRawQuoteDrafts({super.key, required this.job});

  final Job job;

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<List<RawQuote>>(
      stream: GetIt.instance<RawQuotesRepository>().draftQuotes(job: job),
      builder: (context, snapshot) {
        if (snapshot.hasError || !snapshot.hasData) return const SizedBox();

        if (snapshot.hasData) {
          final quotes = snapshot.data!;
          if (quotes.isEmpty) return const SizedBox();

          return Column(
            children: quotes
                .map<Widget>(
                  (rawQuoteDraft) =>
                      RawQuoteDraftCard(rawQuote: rawQuoteDraft, job: job),
                )
                .separatedBy(() => const VerticalMargin.medium()),
          );
        }

        return Container();
      },
    );
  }
}
