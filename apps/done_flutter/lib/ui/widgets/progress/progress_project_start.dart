import 'package:device_calendar/device_calendar.dart';
import 'package:done_auth/done_auth.dart';
import 'package:done_database/done_database.dart';
import 'package:done_flutter/utils/extensions/string_extensions.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_flutter/ui/widgets/buttons/add_calendar_button.dart';
import 'package:done_flutter/ui/widgets/select_date_and_time.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:done_flutter/ui/widgets/jobs/job_progress_item.dart';
import 'package:done_image/done_image.dart';
import 'package:flutter/material.dart';

class ProgressProjectStart extends StatelessWidget {
  const ProgressProjectStart({super.key, required this.job});

  final Job job;

  @override
  Widget build(BuildContext context) {
    final isUserCraftsman = context.authState.isUserCraftsman();
    final deviceCalendarPlugin = DeviceCalendarPlugin();

    final projectStartState = job.projectStartState;

    return JobProgressItem(
      image: Image.asset(
        ImageAssets.calendar,
        scale: 2,
        color: projectStartState.iconColor(context),
      ),
      state: projectStartState.jobState,
      children: [
        Text(
          projectStartState.title(context),
          style: Theme.of(context).textTheme.titleLarge,
        ),
        Text(
          projectStartState.subtitle(context, job).capitalize(),
          style: Theme.of(context).textTheme.bodyLarge!.apply(
            color: context.doneColors.typographyMediumContrast,
          ),
        ),
        if (projectStartState == ProjectStartState.scheduled) ...[
          const VerticalMargin.medium(),
          AddCalendarButton(
            job: job,
            deviceCalendarPlugin: deviceCalendarPlugin,
            scheduleType: ScheduleType.work,
          ),
          const VerticalMargin.small(),
          DoneButton(
            title: Text(S.of(context).cancelSchedule),
            style: projectStartState.buttonStyle,
            onPressed:
                projectStartState.isButtonDisabled
                    ? null
                    : () => projectStartState.buttonAction(context, job),
          ),
        ] else if (isUserCraftsman &&
            job.isInteractive &&
            projectStartState != ProjectStartState.projectCompleted)
          _buildScheduleWorkTimeButton(job, context),
      ],
    );
  }

  Widget _buildScheduleWorkTimeButton(Job job, BuildContext context) {
    return Column(
      children: [
        const VerticalMargin.small(),
        DoneButton(
          style: DoneButtonStyle.secondary,
          onPressed: () => job.projectStartState.buttonAction(context, job),
          title: Text(S.of(context).scheduleWorkTimeButtonTitle),
        ),
        const VerticalMargin.small(),
      ],
    );
  }
}

enum ProjectStartState {
  waiting,
  scheduled,
  projectCompleted,
  scheduledProjectDateCancelled,
}

extension ProjectStartStateValues on ProjectStartState {
  Color iconColor(BuildContext context) {
    switch (this) {
      case ProjectStartState.waiting:
      case ProjectStartState.scheduled:
        return context.doneColors.uiBlack;
      case ProjectStartState.projectCompleted:
      case ProjectStartState.scheduledProjectDateCancelled:
        return context.doneColors.uiPrimary;
    }
  }

  JobProgressState get jobState {
    switch (this) {
      case ProjectStartState.waiting:
        return JobProgressState.uninitialized;
      case ProjectStartState.scheduled:
        return JobProgressState.ongoing;
      case ProjectStartState.projectCompleted:
        return JobProgressState.done;
      case ProjectStartState.scheduledProjectDateCancelled:
        return JobProgressState.cancelled;
    }
  }

  String title(BuildContext context) {
    switch (this) {
      case ProjectStartState.waiting:
        return S.of(context).jobProjectStartTimeStateTitleWaiting;
      case ProjectStartState.scheduled:
        return S.of(context).jobProjectStartTimeStateTitleScheduled;
      case ProjectStartState.projectCompleted:
        return S.of(context).jobProjectStartTimeStateTitleProjectCompleted;
      case ProjectStartState.scheduledProjectDateCancelled:
        return S
            .of(context)
            .jobProjectStartTimeStateTitleWaitingScheduledProjectDateCancelled;
    }
  }

  String subtitle(BuildContext context, Job job) {
    final isUserCraftsman = context.authState.isUserCraftsman();

    switch (this) {
      case ProjectStartState.waiting:
        return isUserCraftsman
            ? S.of(context).noScheduledWorkTimeCraftsmanInfo
            : S.of(context).noScheduledWorkTimelCustomerInfo;
      case ProjectStartState.scheduled:
        return TimeFormatter(
          job.scheduledWorkStartTime!.toDate(),
          context,
        ).toHuman();
      case ProjectStartState.projectCompleted:
        return TimeFormatter.getHumanReadableDate(
          job.events.jobDone?.toDate(),
          context,
        );
      case ProjectStartState.scheduledProjectDateCancelled:
        return isUserCraftsman
            ? S.of(context).cancelledScheduledWorkTimeCraftsmanInfo
            : S.of(context).cancelledScheduledWorkTimeCustomerInfo;
    }
  }

  DoneButtonStyle get chatButtonStyle {
    switch (this) {
      case ProjectStartState.waiting:
      case ProjectStartState.scheduledProjectDateCancelled:
        return DoneButtonStyle.positiveWithBackground;
      case ProjectStartState.scheduled:
        return DoneButtonStyle.negativeWithBackground;
      case ProjectStartState.projectCompleted:
        return DoneButtonStyle.disabled;
    }
  }

  DoneButtonStyle get buttonStyle {
    switch (this) {
      case ProjectStartState.waiting:
      case ProjectStartState.scheduledProjectDateCancelled:
        return DoneButtonStyle.secondary;
      case ProjectStartState.scheduled:
        return DoneButtonStyle.negative;
      case ProjectStartState.projectCompleted:
        return DoneButtonStyle.disabled;
    }
  }

  String buttonTitle(BuildContext context) {
    switch (this) {
      case ProjectStartState.waiting:
      case ProjectStartState.scheduledProjectDateCancelled:
        return S.of(context).scheduleWorkTimeButtonTitle;
      case ProjectStartState.scheduled:
        return S.of(context).cancelScheduledWorkTime;
      case ProjectStartState.projectCompleted:
        return S.of(context).chatJobDone;
    }
  }

  bool get isButtonDisabled => this == ProjectStartState.projectCompleted;

  Future<void>? buttonAction(BuildContext context, Job job) async {
    switch (this) {
      case ProjectStartState.waiting:
      case ProjectStartState.scheduledProjectDateCancelled:
        await selectDateAndTime(context, job, ScheduleType.work);
        break;
      case ProjectStartState.scheduled:
        return _showCancelScheduledWorkTimeDialog(context, job);
      case ProjectStartState.projectCompleted:
    }
  }

  Future<void> _showCancelScheduledWorkTimeDialog(
    BuildContext context,
    Job job,
  ) {
    return showDialog<void>(
      context: context,
      builder:
          (BuildContext context) => DoneDialog(
            title: Text(S.of(context).cancelScheduledWorkTime),
            content: [
              Text(S.of(context).cancelScheduledWorkTimeDialogInfo),
              const VerticalMargin.medium(),
              DoneButton(
                title: Text(S.of(context).cancelSchedule),
                style: DoneButtonStyle.negative,
                onPressed: () {
                  final cancellingUser =
                      context.authState.user!.documentReference;
                  final scheduledWorkCancellation =
                      ScheduledJobEventCancellation(
                        scheduledTime: job.scheduledWorkStartTime!,
                        by: cancellingUser,
                      );

                  Mutations.instance
                      .job(job.id)
                      .cancelScheduledEvent(
                        ScheduleType.work,
                        scheduledWorkCancellation,
                      );

                  Navigator.pop(context);
                },
              ),
              const VerticalMargin(margin: 6),
              DoneButton(
                title: Text(S.of(context).cancel),
                style: DoneButtonStyle.neutral,
                onPressed: () => Navigator.pop(context),
              ),
            ],
          ),
    );
  }
}

extension JobProjectStartState on Job {
  ProjectStartState get projectStartState {
    if (isMarkedAsDone || installationReported) {
      return ProjectStartState.projectCompleted;
    }

    if (scheduledWorkStartTime != null) {
      return ProjectStartState.scheduled;
    }

    if (isScheduledWorkTimeCancelled && scheduledWorkStartTime == null) {
      return ProjectStartState.scheduledProjectDateCancelled;
    }

    return ProjectStartState.waiting;
  }
}
