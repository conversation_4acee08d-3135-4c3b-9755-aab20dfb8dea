import 'package:done_database/done_database.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:done_flutter/ui/widgets/jobs/job_progress_item.dart';
import 'package:done_image/done_image.dart';
import 'package:flutter/material.dart';

class ProgressPrerequisites extends StatelessWidget {
  const ProgressPrerequisites({super.key, required this.job});

  final Job job;

  @override
  Widget build(BuildContext context) {
    final state = job.progressPrerequisitesState;

    return JobProgressItem(
      image: Image.asset(
        ImageAssets.edit,
        width: 18,
        color: state.iconColor(context),
      ),
      state: state.jobState,
      children: [
        Text(
          state.title(context),
          style: Theme.of(context).textTheme.titleLarge,
        ),
        Text(
          state.description(context, job: job),
          style: Theme.of(context).textTheme.bodyLarge!.apply(
            color: context.doneColors.typographyMediumContrast,
          ),
        ),
        if (state != ProgressPrerequisitesState.fulfilled) ...[
          const VerticalMargin.small(),
          DoneButton(
            title: Text(state.buttonTitle(context)),
            style: DoneButtonStyle.secondary,
            onPressed: () => _onButtonPressed(context, state),
          ),
        ],
      ],
    );
  }

  Future<void> _onButtonPressed(
    BuildContext context,
    ProgressPrerequisitesState state,
  ) async {
    final date = await doneDatetimePicker(
      context: context,
      title: state.buttonTitle(context),
      type: DoneDateTimePickerType.date,
      futureDatesAllowed: false,
    );

    if (date != null) {
      switch (state) {
        case ProgressPrerequisitesState.notStarted:
          await Mutations.instance.job(job.id).markAsPrerequisitesPending(date);
          break;
        case ProgressPrerequisitesState.pending:
          await Mutations.instance
              .job(job.id)
              .markAsPrerequisitesFulfilled(date);
          break;
        case ProgressPrerequisitesState.fulfilled:
          // Should be impossible
          break;
      }
    }
  }
}

enum ProgressPrerequisitesState { notStarted, pending, fulfilled }

extension JobProgressPrerequisitesState on Job {
  ProgressPrerequisitesState get progressPrerequisitesState {
    if (events.prerequisitesFulfilled != null) {
      return ProgressPrerequisitesState.fulfilled;
    }

    if (events.prerequisitesPending != null) {
      return ProgressPrerequisitesState.pending;
    }

    return ProgressPrerequisitesState.notStarted;
  }
}

extension ProgressPrerequisitesStateValues on ProgressPrerequisitesState {
  Color iconColor(BuildContext context) {
    switch (this) {
      case ProgressPrerequisitesState.notStarted:
      case ProgressPrerequisitesState.pending:
        return context.doneColors.uiBlack;
      case ProgressPrerequisitesState.fulfilled:
        return context.doneColors.uiPrimary;
    }
  }

  JobProgressState get jobState {
    switch (this) {
      case ProgressPrerequisitesState.notStarted:
      case ProgressPrerequisitesState.pending:
        return JobProgressState.uninitialized;
      case ProgressPrerequisitesState.fulfilled:
        return JobProgressState.done;
    }
  }

  String title(BuildContext context) => S.of(context).prerequisitesTitle;

  String description(BuildContext context, {required Job job}) {
    String formatTimestamp(DateTime timestamp) {
      return TimeFormatter(timestamp, context).toHumanShorter(
        asShortAsPossible: true,
        sayTodayInsteadOfShowingTime: true,
      );
    }

    switch (this) {
      case ProgressPrerequisitesState.notStarted:
        return S.of(context).prerequisitesDescription;
      case ProgressPrerequisitesState.pending:
        if (job.events.prerequisitesPending != null) {
          final sentDate = formatTimestamp(
            job.events.prerequisitesPending!.toDate(),
          );
          return '${S.of(context).prerequisitesSentButton} $sentDate';
        }
      case ProgressPrerequisitesState.fulfilled:
        if (job.events.prerequisitesPending != null &&
            job.events.prerequisitesFulfilled != null) {
          final sentDate = formatTimestamp(
            job.events.prerequisitesPending!.toDate(),
          );
          final fulfilledDate = formatTimestamp(
            job.events.prerequisitesFulfilled!.toDate(),
          );
          return '${S.of(context).prerequisitesSentButton} $sentDate\n${S.of(context).prerequisitesApprovedButton} $fulfilledDate';
        }
    }
    return S.of(context).prerequisitesDescription;
  }

  String buttonTitle(BuildContext context) {
    switch (this) {
      case ProgressPrerequisitesState.notStarted:
        return S.of(context).prerequisitesSentButton;
      case ProgressPrerequisitesState.pending:
        return S.of(context).prerequisitesApprovedButton;
      case ProgressPrerequisitesState.fulfilled:
        return throw UnimplementedError();
    }
  }

  String confirmTitle(BuildContext context) =>
      S.of(context).confirmTopic(title(context).toLowerCase());

  String confirmDescription(BuildContext context) {
    switch (this) {
      case ProgressPrerequisitesState.notStarted:
        return S.of(context).prerequisitesConfirmSentDescription;
      case ProgressPrerequisitesState.pending:
        return S.of(context).prerequisitesConfirmApprovedDescription;
      case ProgressPrerequisitesState.fulfilled:
        return throw UnimplementedError();
    }
  }
}
