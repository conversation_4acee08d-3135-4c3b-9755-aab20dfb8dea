import 'dart:async';

import 'package:cross_file/cross_file.dart';
import 'package:done_auth/done_auth.dart';
import 'package:done_database/done_database.dart';
import 'package:done_flutter/core/router/routes/project_routes.dart';
import 'package:done_flutter/ui/pages/company/shared_generator/widgets/delete_draft_dialog.dart';
import 'package:done_flutter/ui/widgets/progress/reports/editable_job_report_form.dart';
import 'package:done_flutter/ui/widgets/progress/reports/view_only_job_report_form.dart';
import 'package:done_flutter/utils/pdf_util.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_models/done_models.dart';
import 'package:done_router/done_router.dart';
import 'package:done_ui/done_ui.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:get_it/get_it.dart';
import 'package:rxdart/rxdart.dart';

class JobReportForm extends StatefulWidget {
  const JobReportForm({
    required this.reportFormPath,
    required this.jobId,
    this.reportId,
    this.type,
    super.key,
  }) : assert(
         reportId != null || type != null,
         'type must be provided when reportId is not provided',
       );

  final String reportFormPath;
  final String jobId;
  final InstallationReportType? type;
  final String? reportId;

  @override
  State<JobReportForm> createState() => _JobReportFormState();
}

class _JobReportFormState extends State<JobReportForm> {
  JobReport? _report;
  dynamic _formData = <String, dynamic>{};
  Job? _job;
  InstallationReportForm? _reportForm;

  final _fromDataSubject = BehaviorSubject<dynamic>.seeded(<String, dynamic>{});
  StreamSubscription<dynamic>? _formDataListener;

  @override
  void initState() {
    super.initState();
    SchedulerBinding.instance.addPostFrameCallback(
      (_) => _initializeFormData(),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isInitializing =
        _report == null || _reportForm == null || _job == null;
    return DoneModal(
      child: Scaffold(
        body: Scrollbar(
          child: Padding(
            padding: const EdgeInsets.all(Margins.xlarge).copyWith(top: 0),
            child: ScrollConfiguration(
              behavior:
              // Remove default scroll bar on web so we don't end up with two scroll bars
              ScrollConfiguration.of(context).copyWith(scrollbars: false),
              child: ListView(
                primary: true,
                padding: const EdgeInsets.only(top: Margins.xxlarge),
                children: [
                  if (isInitializing)
                    const _PendingInitializationView()
                  else
                    _JobReportFormView(
                      report: _report!,
                      job: _job!,
                      reportForm: _reportForm!,
                      onFormDismiss: () async {
                        return _shouldDismissForm();
                      },
                      onChanged: (data) {
                        _formData = data;
                        _fromDataSubject.add(data);
                      },
                    ),
                  const VerticalMargin.small(),
                  if (_report?.isAccepted ?? false)
                    _ViewFormPdfButton(report: _report!),
                  DoneButton(
                    title: Text(
                      (_report?.isAccepted ?? false)
                          ? S.of(context).close
                          : S.of(context).cancel,
                    ),
                    style: DoneButtonStyle.neutral,
                    onPressed: () async {
                      if (await _shouldDismissForm()) context.router.pop();
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _initializeFormData() async {
    _reportForm = await GetIt.instance<JobReportsRepository>()
        .fetchInstallationReportForm(widget.reportFormPath);

    _job = await GetIt.instance<JobsRepository>().fetchJob(widget.jobId);

    _report =
        widget.reportId != null
            ? await GetIt.instance<JobReportsRepository>().fetchJobReport(
              widget.jobId,
              widget.reportId!,
            )
            : await GetIt.instance<JobReportsRepository>().createJobReport(
              type: widget.type!,
              job: _job!,
              reporterId: context.authState.user!.documentReference.id,
            );

    setState(() {});
    // We shouldn't try to update a read-only report
    if (_report!.isAccepted) return;
    // Prefill the subject with the report's data.
    _fromDataSubject.value = _report!.data;
    // Automatically keep draft up-to-date.
    _formDataListener = _fromDataSubject
        .debounceTime(const Duration(seconds: 5))
        .listen((data) {
          Mutations.instance
              .jobReport(_report!.reference)
              .update((data as Map).cast());
        });
  }

  Future<bool> _shouldDismissForm() async {
    if (_report == null || _report!.isAccepted) return true;

    final selection = await showShouldDeleteDraftDialog(context);
    final mutations = Mutations.instance.jobReport(_report!.reference);

    switch (selection) {
      case LeavingGeneratorSelection.deleteDraft:
        await mutations.delete();
        return true;
      case LeavingGeneratorSelection.saveDraft:
        await mutations.update((_formData as Map).cast());
        return true;
      default:
        return false;
    }
  }

  @override
  void dispose() {
    _formDataListener?.cancel();
    super.dispose();
  }
}

class _ViewFormPdfButton extends StatelessWidget {
  const _ViewFormPdfButton({required this.report});

  final JobReport report;

  @override
  Widget build(BuildContext context) {
    return DoneButton(
      style: DoneButtonStyle.neutral,
      title: Text(S.of(context).viewAsPdf),
      onPressed: () async {
        final jobId = report.reference.parent.parent!.id;
        await viewAsPdf(
          context: context,
          storagePath: '${report.bucket(jobId)}/report.pdf',
          pushRoute: (url) async {
            final params = ViewReportPdfRouteParams(
              projectId: jobId,
              reportId: report.reference.id,
              url: url,
            );
            ViewReportPdfRoute(params: params).navigate(context);
          },
        );
      },
    );
  }
}

class _JobReportFormView extends StatelessWidget {
  const _JobReportFormView({
    required this.report,
    required this.job,
    required this.reportForm,
    required this.onFormDismiss,
    required this.onChanged,
  });

  final JobReport report;
  final Job job;
  final InstallationReportForm reportForm;
  final AsyncValueGetter<bool> onFormDismiss;
  final ValueChanged<dynamic> onChanged;

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: Theme.of(context).copyWith(
        inputDecorationTheme: InputDecorationTheme(
          fillColor: context.doneColors.uiPrimary,
          filled: true,
          labelStyle: Theme.of(context).textTheme.labelMedium!.copyWith(
            color: context.doneColors.typographyLowContrast,
          ),
          contentPadding: const EdgeInsets.only(
            top: Margins.medium,
            left: Margins.small + 2,
            right: Margins.small + 2,
            bottom: Margins.small,
          ),
          enabledBorder: UnderlineInputBorder(
            borderSide: BorderSide(
              color: context.doneColors.typographyLowContrast,
            ),
          ),
          border: const UnderlineInputBorder(),
          errorMaxLines: 5,
        ),
      ),
      // FIXME: Rewrite to use PopScope instead
      // ignore: deprecated_member_use
      child: WillPopScope(
        onWillPop: () async {
          return onFormDismiss();
        },
        child:
            report.isAccepted
                ? ViewOnlyJobReportForm(reportForm: reportForm, report: report)
                : EditableJobReportForm(
                  job: job,
                  report: report,
                  reportForm: reportForm,
                  onChanged: onChanged,
                  onSubmitted: ({bool success = false}) async {
                    // FIXME: Check why we need to wait one frame here
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      if (success) context.router.pop(true);
                    });
                  },
                ),
      ),
    );
  }
}

class _PendingInitializationView extends StatelessWidget {
  const _PendingInitializationView();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: Margins.xxlarge),
      child: EmptyState(
        description: Text(S.of(context).oneMoment),
        children: [CenteredProgressIndicator()],
      ),
    );
  }
}

extension PlatformFileX on PlatformFile {
  XFile toXFile() {
    if (kIsWeb) return XFile.fromData(bytes!, name: name);
    return XFile(path!, name: name, bytes: bytes);
  }
}
