import 'package:done_flutter/core/services/firebase_storage_service.dart';
import 'package:done_flutter/ui/pages/chat/video_player_page.dart';
import 'package:done_flutter/ui/pages/view_pdf/pdf_router_page.dart';
import 'package:done_flutter/ui/widgets/progress/reports/form_file_preview.dart';
import 'package:done_flutter/ui/widgets/progress/reports/relevant_mime_type.dart';
import 'package:done_flutter/utils/extensions/models/job_report.dart';
import 'package:done_image/done_image.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_jsonschema_builder/flutter_jsonschema_builder.dart';
import 'package:get_it/get_it.dart';

class ViewOnlyJobReportForm extends StatelessWidget {
  const ViewOnlyJobReportForm({
    required this.reportForm,
    required this.report,
    super.key,
  });

  final InstallationReportForm reportForm;
  final JobReport report;

  @override
  Widget build(BuildContext context) {
    final lines = report.dataEntries(reportForm);

    if (reportForm.uiSchema != null) lines.sort((a, b) => a.order - b.order);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        _ViewOnlyJobReportHeader(
          title: reportForm.formSchema['title'] as String,
        ),
        ...lines.map((e) => _JobReportEntryView(entry: e)),
      ].separatedBy(() => const VerticalMargin.small()),
    );
  }
}

class _ViewOnlyJobReportHeader extends StatelessWidget {
  const _ViewOnlyJobReportHeader({required this.title});
  final String title;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: <Widget>[
        SizedBox(
          width: double.infinity,
          child: Text(
            title,
            style: Theme.of(context).textTheme.titleLarge,
            textAlign: TextAlign.center,
          ),
        ),
        const Divider(),
      ],
    );
  }
}

class _JobReportEntryView extends StatelessWidget {
  const _JobReportEntryView({required this.entry});
  final JobReportEntry entry;

  @override
  Widget build(BuildContext context) {
    if (isUrl) return _ViewOnlyFormFilePreview(path: value, label: entry.label);

    return LabeledValue(
      label: entry.label,
      value: value,
      showCopyButton: showCopyButton,
    );
  }

  bool get isUrl =>
      entry.answer is String && entry.property['format'] == 'data-url';
  bool get isPassword =>
      entry.answer is String && entry.property['format'] == 'password';

  bool get showCopyButton => value != 'Ja' && value != 'Nej';

  String get value {
    final answer = entry.answer;
    if (answer is bool) return answer ? 'Ja' : 'Nej';

    if (answer is String) return isPassword ? '•' * answer.length : answer;

    if (answer is num) return answer.toString();

    if (answer is List) return answer.join(', ');

    return '';
  }
}

class _ViewOnlyFormFilePreview extends StatelessWidget {
  const _ViewOnlyFormFilePreview({required this.path, required this.label});
  final String path;
  final String label;
  @override
  Widget build(BuildContext context) {
    return FutureBuilder<SchemaFormFile>(
      future: GetIt.instance<FirebaseStorageService>().downloadSchemaFormFile(
        path,
      ),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Align(
            alignment: Alignment.centerLeft,
            child: DoneAdaptiveLoadingIndicator(),
          );
        }
        if (!snapshot.hasData || snapshot.data?.bytes == null) {
          return const SizedBox.shrink();
        }

        return DefaultTextStyle(
          style: Theme.of(context).textTheme.bodyLarge!.apply(
            color: context.doneColors.typographyMediumContrast,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(label),
              FormFilePreview(
                file: snapshot.data!,
                onRemove: null,
                previewHandlers: _previewHandlers(context),
              ),
            ].separatedBy(() => const VerticalMargin.small()),
          ),
        );
      },
    );
  }

  Map<RelevantMimeType, VoidCallback?> _previewHandlers(BuildContext context) {
    final photoDataSource = UrlsPhotoDataSource(paths: [path]);

    return {
      RelevantMimeType.image:
          () => Navigator.of(context).push(
            MaterialPageRoute<void>(
              builder: (context) => PhotoPage(dataSource: photoDataSource),
            ),
          ),
      RelevantMimeType.pdf:
          () => Navigator.of(context).push(
            MaterialPageRoute<void>(
              builder:
                  (context) =>
                      PdfRouterPage(pdfFilePath: path, title: Text(label)),
            ),
          ),
      RelevantMimeType.video:
          () => Navigator.of(context).push(
            MaterialPageRoute<void>(
              builder:
                  (context) => FutureBuilder<String>(
                    future: getDownloadUrl(path),
                    builder: (context, snapshot) {
                      if (!snapshot.hasData) return const LoadingPage();
                      return VideoPlayerPage(videoRef: snapshot.data!);
                    },
                  ),
            ),
          ),
    };
  }
}
