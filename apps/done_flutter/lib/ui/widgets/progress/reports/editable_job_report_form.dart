import 'dart:convert';

import 'package:collection/collection.dart';
import 'package:done_database/done_database.dart';
import 'package:done_flutter/core/router/routes/project_routes.dart';
import 'package:done_flutter/core/services/firebase_storage_service.dart';
import 'package:done_flutter/ui/widgets/jobs/job_close_actions.dart';
import 'package:done_flutter/ui/widgets/progress/reports/form_file_preview.dart';
import 'package:done_flutter/utils/extensions/models/schema_form_file.dart';
import 'package:done_image/done_image.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_jsonschema_builder/flutter_jsonschema_builder.dart';
import 'package:logger/logger.dart';
import 'package:get_it/get_it.dart';

class EditableJobReportForm extends StatefulWidget {
  const EditableJobReportForm({
    required this.job,
    required this.reportForm,
    required this.report,
    required this.onChanged,
    required this.onSubmitted,
  });

  final Job job;
  final InstallationReportForm reportForm;
  final JobReport report;
  final ValueChanged<dynamic> onChanged;
  final void Function({bool success}) onSubmitted;

  @override
  State<EditableJobReportForm> createState() => _EditableJobReportFormState();
}

class _EditableJobReportFormState extends State<EditableJobReportForm> {
  bool _isUploadingFiles = false;
  bool _isFormValid = true;
  @override
  Widget build(BuildContext context) {
    final initialData =
        widget.report.data.isEmpty
            ? widget.report.type.defaults(widget.job)
            : widget.report.data;
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      child: AbsorbPointer(
        absorbing: widget.report.status == JobReportStatus.accepted,
        child: JsonForm(
          padding: EdgeInsets.zero,
          jsonSchema: json.encode(widget.reportForm.formSchema),
          uiSchema: json.encode(widget.reportForm.uiSchema),
          initialData: initialData,
          onChanged: widget.onChanged,
          showDebugElements: false,
          initialFileValueHandler: () => {"*": _defaultInitialFileValueHandler},
          fileHandler:
              () => {
                "*": (property) async {
                  setState(() => _isUploadingFiles = true);
                  final result = await _defaultFileHandler(context, property);
                  setState(() => _isUploadingFiles = false);

                  return result;
                },
              },
          customPickerHandler:
              () => {
                '*': (data) async {
                  final choices = data.enumm ?? [];
                  FocusScope.of(context).unfocus();
                  final result = await pickAmongChoices(
                    title: Text(data.title),
                    context: context,
                    choices: choices,
                    choiceTitleBuilder:
                        (context, choice) => Text(
                          data.enumNames?[(choices).indexOf(choice)]
                                  .toString() ??
                              choice.toString(),
                        ),
                    cancelButtonText: Text(S.of(context).cancel),
                  );

                  return result?.choice;
                },
              },
          jsonFormSchemaUiConfig: JsonFormSchemaUiConfig(
            addFileButtonBuilder: (onPressed, property) {
              final fileType = _fileTypeFromString(property.fileType);
              final isImage = fileType == FileType.image;
              final title =
                  isImage ? S.of(context).addPhoto : S.of(context).sendFile;
              return DoneAsyncAction(
                action: onPressed,
                builder:
                    (context, actionOrNull, loading) => DoneButton(
                      image:
                          loading
                              ? null
                              : Icon(
                                isImage ? Icons.add_a_photo : Icons.attach_file,
                                color: context.doneColors.uiOnPrimary,
                              ),
                      style: DoneButtonStyle.neutral,
                      title:
                          loading
                              ? const DoneAdaptiveLoadingIndicator()
                              : Text(title),
                      onPressed: actionOrNull,
                    ),
              );
            },
            selectionTitle: S.of(context).selectionFieldHint,
            requiredText: S.of(context).validationForEmptyFields,
            filesBuilder: (files, {required onRemove}) {
              if (files == null) return const SizedBox.shrink();

              return Padding(
                padding: const EdgeInsets.only(bottom: Margins.verySmall),
                child: Wrap(
                  spacing: Margins.small,
                  runSpacing: Margins.small,
                  children:
                      files
                          .map(
                            (file) =>
                                FormFilePreview(file: file, onRemove: onRemove),
                          )
                          .toList(),
                ),
              );
            },
            submitButtonBuilder:
                (onPressed) => Column(
                  children: [
                    const Divider(),
                    if (!_isFormValid)
                      Padding(
                        padding: const EdgeInsets.all(Margins.medium),
                        child: Text(
                          '* ${S.of(context).formValidationIssue}',
                          style: Theme.of(context).textTheme.bodyMedium!.apply(
                            color: Theme.of(context).colorScheme.error,
                          ),
                        ),
                      ),
                    const VerticalMargin.small(),
                    DoneButton(
                      style: DoneButtonStyle.secondary,
                      title: Text(S.of(context).send),
                      onPressed:
                          _isUploadingFiles
                              ? null
                              : () {
                                FocusScope.of(context).unfocus();
                                setState(() => _isFormValid = onPressed());
                              },
                    ),
                  ],
                ),
          ),
          onFormDataSaved: (data) async {
            if (widget.report.type == InstallationReportType.installation) {
              final shouldContinue = await showPendingQuoteCancellationDialog(
                context: context,
                job: widget.job,
              );

              if (!shouldContinue) return;
            }

            // Show confirmation popup before submitting the report
            final confirmed = await showConfirmationPopup(
              context: context,
              message: Text(S.of(context).sendReportConfirmationMessage),
              actionTitle: Text(S.of(context).send),
              cancelTitle: Text(S.of(context).cancel),
            );

            if (!confirmed) {
              GetIt.I<Logger>().i("Job report submission cancelled by user");
              return;
            }

            final formData =
                (data as Map).cast<String, dynamic>().deepRemoveEmptyValues();

            // Submit the report for validation.
            await Mutations.instance
                .jobReport(widget.report.reference)
                .submit(formData);

            // Display the status of the report and return result
            final params = JobReportStatusRouteParams(
              projectId: widget.job.id,
              reportId: widget.report.reference.id,
            );
            final didSave = await JobReportStatusRoute(
              params: params,
            ).push<bool>(context);

            // Notify the parent widget that the report has been submitted
            widget.onSubmitted(success: didSave ?? false);
          },
        ),
      ),
    );
  }

  Future<List<SchemaFormFile>?> _defaultFileHandler(
    BuildContext context,
    SchemaProperty property,
  ) async {
    final fileType = _fileTypeFromString(property.fileType);
    final bucket = widget.report.bucket(widget.job.id);

    if (fileType == FileType.image) {
      final result = await pickImages(
        context,
        pickMultiple: property.isMultipleFile,
      );
      if (result == null) return null;

      return result.toSchemaFormFiles(bucket);
    }

    final result = await FilePicker.platform.pickFiles(
      type: fileType,
      allowedExtensions:
          property.acceptedFiles?.map((e) => e.replaceFirst('.', '')).toList(),
      allowMultiple: property.isMultipleFile,
      withData: true,
    );

    if (result == null) return null;

    return result.files.toSchemaFormFiles(bucket);
  }

  Future<List<SchemaFormFile>?> _defaultInitialFileValueHandler(
    dynamic defaultValue,
  ) async {
    final downloadSchemaFormFile =
        GetIt.instance<FirebaseStorageService>().downloadSchemaFormFile;
    if (defaultValue is List) {
      final result = await Future.wait(
        defaultValue.cast<String>().map(downloadSchemaFormFile),
      );
      return result.whereType<SchemaFormFile>().toList();
    }

    if (defaultValue is String) {
      final file = await downloadSchemaFormFile(defaultValue);
      return [file];
    }

    return null;
  }
}

FileType _fileTypeFromString(String? type) {
  return FileType.values.firstWhereOrNull((element) => element.name == type) ??
      FileType.any;
}
