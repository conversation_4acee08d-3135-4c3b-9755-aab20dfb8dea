import 'package:done_deductions/done_deductions.dart';
import 'package:done_flutter/core/router/routes/project_routes.dart';
import 'package:done_flutter/ui/widgets/contact_support_snackbar.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

class JobReportCard extends StatelessWidget {
  const JobReportCard({
    super.key,
    required this.job,
    required this.report,
    this.didCreateReport,
  });

  final JobReport report;
  final Job job;
  final void Function()? didCreateReport;

  @override
  Widget build(BuildContext context) {
    final formRef = report.form ?? report.type.formRef(job);
    return ContentCard(
      title:
          report.type == InstallationReportType.precheck
              ? S.of(context).precheckButton
              : S.of(context).reportNoun,
      subTitle: Text(report.status.title(context)),
      onTap: () async {
        if (formRef == null) {
          showContactSupportSnackbar(
            context,
            exceptionMessage:
                'Routing to report form with id ${report.reference.id} without form reference',
          );
          return;
        }
        final reportWasCreated = await showInstallationReportForm(
          context: context,
          job: job,
          reportFormPath: formRef.path,
          type: report.type,
          report: report,
        );

        if (reportWasCreated) {
          didCreateReport?.call();
        }
      },
      leading: const SizedBox.shrink(),
      backgroundColor: report.status.color(context),
      height: 74,
    );
  }
}

/// Shows the installation report form for the given job
/// and return a boolean indicating if we created a new report or not.
Future<bool> showInstallationReportForm({
  required BuildContext context,
  required Job job,
  required String reportFormPath,
  required InstallationReportType type,
  JobReport? report,
}) async {
  if (report == null) {
    final params = NewJobReportRouteParams(
      projectId: job.id,
      reportFormPath: reportFormPath,
      type: type,
    );
    final didCreateReport = await NewJobReportRoute(
      params: params,
    ).push<bool>(context);
    return didCreateReport ?? false;
  } else {
    final params = JobReportRouteParams(
      projectId: job.id,
      reportId: report.reference.id,
      reportFormPath: reportFormPath,
    );
    final didCreateReport = await JobReportRoute(
      params: params,
    ).push<bool>(context);
    return didCreateReport ?? false;
  }
}
