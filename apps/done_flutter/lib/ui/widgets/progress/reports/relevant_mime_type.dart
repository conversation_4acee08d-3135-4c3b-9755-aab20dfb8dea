import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_jsonschema_builder/flutter_jsonschema_builder.dart';
import 'package:mime/mime.dart';

enum RelevantMimeType { image, text, pdf, video, audio, unknown }

extension FileSchemaMimeType on SchemaFormFile {
  RelevantMimeType get mime {
    // Try to detect MIME type using the original filename (value) first,
    // then fall back to the unique filename (name)
    final mime = lookupMimeType(name, headerBytes: bytes);
    if (mime == null) return RelevantMimeType.unknown;

    return RelevantMimeType.values.firstWhere(
      (value) => mime.contains(value.name),
      orElse: () => RelevantMimeType.unknown,
    );
  }
}

extension RelevantMimeTypeFilePreview on RelevantMimeType {
  Widget _previewWidget(BuildContext context, SchemaFormFile file) {
    switch (this) {
      case RelevantMimeType.image:
        return Image.memory(
          file.bytes,
          width: 100,
          height: 100,
          fit: BoxFit.cover,
        );
      case RelevantMimeType.text:
        return _fileIconContainer(
          context: context,
          child: const Icon(Icons.text_snippet),
        );
      case RelevantMimeType.pdf:
        return _fileIconContainer(
          context: context,
          child: const Icon(Icons.picture_as_pdf_rounded),
        );

      case RelevantMimeType.audio:
        return _fileIconContainer(
          context: context,
          child: const Icon(Icons.audio_file),
        );
      case RelevantMimeType.video:
        return _fileIconContainer(
          context: context,
          child: const Icon(Icons.video_file),
        );
      case RelevantMimeType.unknown:
        return _fileIconContainer(
          context: context,
          child: const Icon(Icons.question_mark),
        );
    }
  }

  Widget _fileIconContainer({
    required BuildContext context,
    required Widget child,
  }) {
    return Container(
      color: context.doneColors.uiBg2,
      width: 100,
      height: 100,
      child: Center(child: child),
    );
  }

  Widget widget(
    BuildContext context,
    SchemaFormFile file, {
    required ValueChanged<String>? onRemove,
    VoidCallback? previewHandler,
  }) {
    return SizedBox(
      width: 100,
      child: Column(
        children: [
          Stack(
            children: [
              GestureDetector(
                onTap: previewHandler,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(Margins.small),
                  child: _previewWidget(context, file),
                ),
              ),
              if (onRemove != null)
                Positioned(
                  right: 0,
                  top: 0,
                  child: DoneRoundedIconButton(
                    onPressed: () => onRemove(file.name),
                    size: 24,
                    icon: Icons.close_rounded,
                  ),
                ),
            ],
          ),
          if (onRemove != null) ...[
            const VerticalMargin.small(),
            Text(
              file.name,
              style: Theme.of(context).textTheme.bodySmall,
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
          ],
        ],
      ),
    );
  }
}
