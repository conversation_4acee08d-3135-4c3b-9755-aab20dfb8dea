import 'package:done_flutter/ui/widgets/progress/reports/relevant_mime_type.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_jsonschema_builder/flutter_jsonschema_builder.dart';

class FormFilePreview extends StatelessWidget {
  const FormFilePreview({
    required this.file,
    required this.onRemove,
    this.previewHandlers,
    super.key,
  });

  final SchemaFormFile file;
  final ValueChanged<String>? onRemove;
  final Map<RelevantMimeType, VoidCallback?>? previewHandlers;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(Margins.small),
          child: childFromType(context, onRemove: onRemove),
        ),
      ],
    );
  }

  Widget childFromType(
    BuildContext context, {
    required ValueChanged<String>? onRemove,
    VoidCallback? onImageTap,
  }) {
    final mime = file.mime;
    return mime.widget(
      context,
      file,
      onRemove: onRemove,
      previewHandler: previewHandlers?[mime],
    );
  }
}
