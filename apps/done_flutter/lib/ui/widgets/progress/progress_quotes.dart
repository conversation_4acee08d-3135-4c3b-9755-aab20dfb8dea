import 'package:done_auth/done_auth.dart';
import 'package:done_database/done_database.dart';
import 'package:done_flutter/utils/extensions/enums/quote.dart';
import 'package:done_flutter/utils/extensions/models/job.dart';

import 'package:done_flutter/ui/widgets/buttons/send_quote_button.dart';
import 'package:done_flutter/ui/widgets/progress/progress_raw_quote_drafts.dart';
import 'package:done_flutter/utils/dialogs/quote_dialogs.dart';
import 'package:done_flutter/utils/first_build_callback.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:done_flutter/ui/widgets/cards/quote_card.dart';
import 'package:done_flutter/ui/widgets/jobs/job_progress_item.dart';
import 'package:done_image/done_image.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';

class ProgressQuotes extends StatelessWidget {
  const ProgressQuotes({super.key, required this.job});

  final Job job;

  @override
  Widget build(BuildContext context) {
    final progressOfferState = _progressOffersStateFromJob(job);
    final userType = context.authState.getUserType();

    final authState = context.authState;
    final isCustomer = authState.isUserCustomer();
    final id =
        isCustomer
            ? authState.user!.documentReference.id
            : authState.getUserCompany()!.id;
    final quotesRepo = GetIt.instance<QuotesRepository>();
    final stream =
        isCustomer
            ? quotesRepo.customerQuotes(jobId: job.id, customerId: id)
            : quotesRepo.companyQuotes(jobId: job.id, companyId: id);
    return FirstBuildCallback(
      onFirstBuild: () async {
        // No answered quotes or job is not invoiced through Done, no need to show the dialog
        if (job.isInvoicedThroughPartnership ||
            progressOfferState != ProgressOffersState.answered) {
          return;
        }
        final isRotInfoNeeded = checkIfRotInformationIsNeeded(
          context,
          job: job,
        );
        if (isRotInfoNeeded) return showEnterRotInformationDialog(context);
      },
      child: JobProgressItem(
        image: Image.asset(
          ImageAssets.dollar,
          scale: 2,
          color: progressOfferState.iconColor(context),
        ),
        state: progressOfferState.jobState,
        children: [
          Text(
            progressOfferState.title(context, job),
            style: Theme.of(context).textTheme.titleLarge,
          ),
          if (progressOfferState == ProgressOffersState.waiting)
            Text(
              job.emptyQuoteStateText(context),
              style: Theme.of(context).textTheme.bodyLarge!.apply(
                color: context.doneColors.typographyMediumContrast,
              ),
            ),
          const VerticalMargin.small(),
          StreamBuilder<List<Quote>>(
            stream: stream,
            builder: (context, snapshot) {
              if (!snapshot.hasData) return const SizedBox();

              final quotes = snapshot.data!;
              return Column(
                children: quotes
                    .map<Widget>(
                      (quote) => QuoteCard(quote: quote, jobId: job.id),
                    )
                    .separatedBy(() => const VerticalMargin.small()),
              );
            },
          ),
          if (userType == UserType.craftsman && job.isInteractive) ...[
            ProgressRawQuoteDrafts(job: job),
            const VerticalMargin.medium(),
            SendQuoteButton(job: job),
          ],
        ],
      ),
    );
  }
}

enum ProgressOffersState { waiting, notAnswered, answered }

extension ProgressOffersStateValues on ProgressOffersState {
  Color iconColor(BuildContext context) {
    switch (this) {
      case ProgressOffersState.waiting:
      case ProgressOffersState.notAnswered:
        return context.doneColors.uiBlack;
      case ProgressOffersState.answered:
        return context.doneColors.uiPrimary;
    }
  }

  JobProgressState get jobState {
    switch (this) {
      case ProgressOffersState.waiting:
        return JobProgressState.uninitialized;
      case ProgressOffersState.notAnswered:
        return JobProgressState.ongoing;
      case ProgressOffersState.answered:
        return JobProgressState.done;
    }
  }

  String title(BuildContext context, Job job) {
    switch (this) {
      case ProgressOffersState.waiting:
        return job.quoteTitle(context);
      case ProgressOffersState.notAnswered:
        final isAdditionalCosts = job.isInvoicedThroughPartnership;
        return QuoteStatus.pending.informationText(
          context,
          isAdditionalCosts: isAdditionalCosts,
        );
      case ProgressOffersState.answered:
        return job.quoteAccepted(context);
    }
  }

  String createQuoteButtonText(BuildContext context, Job job) =>
      job.createQuoteTitle(context);
}

ProgressOffersState _progressOffersStateFromJob(Job job) {
  if (job.isOfferAccepted) return ProgressOffersState.answered;

  if (job.isOfferSent) return ProgressOffersState.notAnswered;

  return ProgressOffersState.waiting;
}
