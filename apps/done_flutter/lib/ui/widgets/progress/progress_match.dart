import 'package:done_localizations/done_localizations.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:done_flutter/ui/widgets/jobs/job_progress_item.dart';
import 'package:done_image/done_image.dart';
import 'package:flutter/material.dart';

class ProgressMatch extends StatelessWidget {
  const ProgressMatch({super.key, required this.job});

  final Job job;

  @override
  Widget build(BuildContext context) {
    var progressOfferState = ProgressMatchState.searchingCompany;

    if (job.company != null) {
      progressOfferState = ProgressMatchState.companyFound;
    }

    final companyName = job.companyName ?? "";
    return JobProgressItem(
      image: Image.asset(
        ImageAssets.search,
        scale: 2,
        color: progressOfferState.iconColor(context),
      ),
      state: progressOfferState.jobState,
      children: [
        Text(
          progressOfferState.title(context),
          style: Theme.of(context).textTheme.titleLarge,
        ),
        Text(
          progressOfferState.description(context, companyName),
          style: Theme.of(context).textTheme.bodyLarge!.apply(
            color: context.doneColors.typographyMediumContrast,
          ),
        ),
      ],
    );
  }
}

enum ProgressMatchState { searchingCompany, companyFound }

extension ProgressMatchStateValues on ProgressMatchState {
  Color iconColor(BuildContext context) {
    switch (this) {
      case ProgressMatchState.searchingCompany:
        return context.doneColors.uiBlack;
      case ProgressMatchState.companyFound:
        return context.doneColors.uiPrimary;
    }
  }

  JobProgressState get jobState {
    switch (this) {
      case ProgressMatchState.searchingCompany:
        return JobProgressState.ongoing;
      case ProgressMatchState.companyFound:
        return JobProgressState.done;
    }
  }

  String title(BuildContext context) => S.of(context).matchStateTitle;

  String description(BuildContext context, String companyName) {
    switch (this) {
      case ProgressMatchState.searchingCompany:
        return S.of(context).matchStateDescriptionSearching;
      case ProgressMatchState.companyFound:
        return S.of(context).matchStateDescriptionMatched(companyName);
    }
  }
}
