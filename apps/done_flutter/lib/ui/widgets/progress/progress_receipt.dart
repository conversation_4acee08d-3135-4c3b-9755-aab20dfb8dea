import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:done_ui/done_ui.dart';

import 'package:done_flutter/ui/widgets/jobs/job_progress_item.dart';
import 'package:done_image/done_image.dart';
import 'package:flutter/material.dart';

class ProgressReceipt extends StatelessWidget {
  const ProgressReceipt({super.key, required this.job});

  final DocumentSnapshot job;

  //TODO: Move Strings to Airtable
  @override
  Widget build(BuildContext context) {
    return JobProgressItem(
      image: Image.asset(ImageAssets.creditCard, scale: 2),
      state: JobProgressState.uninitialized,
      children: [
        Text("Faktura", style: Theme.of(context).textTheme.titleLarge),
        Text(
          "När du och din hantverkare är överrens om när uppdraget ska påbörjas kommer det att synas här",
          style: Theme.of(context).textTheme.bodyLarge!.apply(
            color: context.doneColors.typographyMediumContrast,
          ),
        ),
        const VerticalMargin.large(),
        ContentCard(
          title: "this-is-a-filename.pdf",
          subTitle: const Text("optional file status"),
          leading: Image.asset(ImageAssets.attachment, scale: 2),
        ),
      ],
    );
  }
}
