import 'package:done_localizations/done_localizations.dart';
import 'package:done_router/done_router.dart';

import 'package:done_ui/done_ui.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';

class DoneNotificationDialog extends StatelessWidget {
  const DoneNotificationDialog({
    super.key,
    required this.message,
    required this.uri,
  });

  final RemoteMessage message;
  final Uri? uri;

  @override
  Widget build(BuildContext context) {
    return DoneDialog(
      title:
          message.notification?.title != null
              ? Text(message.notification!.title!)
              : null,
      content: [
        if (message.notification?.body != null)
          Text(message.notification!.body!),
        if (uri != null)
          DoneButton(
            title: Text(uri!.title(context)),
            style: DoneButtonStyle.secondary,
            onPressed: () {
              Navigator.of(context).pop();
              URLRouter.instance.open(uri!, router: context.router);
            },
          ),
        Done<PERSON>utton(
          title: Text(S.of(context).dismiss),
          style: DoneButtonStyle.neutral,
          onPressed: () => Navigator.of(context).pop(),
        ),
      ].separatedBy(() => const VerticalMargin.small()),
    );
  }
}

extension _TitleFromUriPath on Uri {
  String title(BuildContext context) {
    switch (pathSegments.last) {
      // TODO : extract route paths so we don't have to use hardcoded strings
      case 'jobDetail':
        return S.of(context).goToProject;
      default:
        return S.of(context).genericOpen;
    }
  }
}
