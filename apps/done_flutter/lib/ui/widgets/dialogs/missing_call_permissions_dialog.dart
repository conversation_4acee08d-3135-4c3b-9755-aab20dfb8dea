import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:permission_handler/permission_handler.dart';

/// Used to inform users (on iOS) that permissions for camera
/// and/or microphone is denied.
///
/// Includes a button to go to settings and a button to dismiss.
class MissingCallPermissionsDialog extends StatelessWidget {
  const MissingCallPermissionsDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return DoneDialog(
      title: Text(S.of(context).missingCameraMicrophonePermissionsTitle),
      content: [
        Text(S.of(context).missingCameraMicrophonePermissionsBody),
        const VerticalMargin.medium(),
        Done<PERSON><PERSON>on(
          style: DoneButtonStyle.secondary,
          title: Text(S.of(context).openSettings),
          onPressed: openAppSettings,
        ),
        const VerticalMargin.small(),
        <PERSON><PERSON><PERSON><PERSON>(
          title: Text(S.of(context).dismiss),
          style: DoneButtonStyle.neutral,
          onPressed: () => Navigator.pop(context),
        ),
      ],
    );
  }
}
