import 'package:done_flutter/ui/pages/company/shared_generator/generator_type.dart';

import 'package:done_flutter/utils/extensions/enums/quote_creation_type.dart';
import 'package:done_flutter/utils/extensions/models/job.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:flutter/material.dart';

/// Used to let the user choose which quote type to send
///
/// Current options are new quote or quote from fixed price job or send PDF quote
class SendQuoteDialog extends StatelessWidget {
  const SendQuoteDialog({
    required this.job,
    required this.originalContext,
    super.key,
  });

  final Job job;

  /// This is used to pass down the origin page context as this dialog will be popped on selection
  ///
  /// The original context is needed for showing further dialog down the flow
  final BuildContext originalContext;

  @override
  Widget build(BuildContext context) {
    return DonePopup(
      title: Text(job.createQuoteTitle(context)),
      content: [
        const VerticalMargin.verySmall(),
        ...job.quoteCreationTypes.map(
          (type) => type.button(originalContext, GeneratorType.quote, job),
        ),
        DoneButton(
          title: Text(S.of(context).cancel),
          style: DoneButtonStyle.neutral,
          onPressed: () => Navigator.pop(context),
        ),
      ].separatedBy(() => const VerticalMargin.small()),
    );
  }
}
