import 'package:done_analytics/done_analytics.dart';
import 'package:done_auth/done_auth.dart';
import 'package:done_database/done_database.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_flutter/ui/widgets/progress/progress_video_call.dart';
import 'package:done_flutter/ui/widgets/select_date_and_time.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

class ScheduleCallDialog extends StatelessWidget {
  const ScheduleCallDialog({
    super.key,
    required this.job,
    required this.originalContext,
  });

  /// This is used to pass down the origin page context as this dialog will be popped on selection
  ///
  /// The original context is needed for showing further dialog down the flow
  final BuildContext originalContext;

  final Job job;
  @override
  Widget build(BuildContext context) {
    return DoneDialog(
      title: Text(VideoCallState.waiting.title(context)),
      content: [
        Text(S.of(context).noScheduledCallCraftsmanInfo),
        const VerticalMargin.medium(),
        DoneButton(
          style: DoneButtonStyle.secondary,
          onPressed: () {
            EventLogger.instance.logEvent('scheduleCallPopUpDismissed', {
              'buttonPressed': 'scheduleNow',
            });
            selectDateAndTime(context, job, ScheduleType.videoCall);
            Navigator.of(context).pop();
          },
          title: Text(S.of(context).scheduleVideoCall),
        ),
        const VerticalMargin.small(),
        DoneButton(
          style: DoneButtonStyle.neutral,
          title: Text(S.of(context).companyRatingsPopUpLaterButton),
          onPressed: () {
            EventLogger.instance.logEvent('scheduleCallPopUpDismissed', {
              'buttonPressed': 'scheduleLater',
            });
            Navigator.pop(context);
          },
        ),
        const VerticalMargin.small(),
        DoneButton(
          style: DoneButtonStyle.negative,
          onPressed: () {
            EventLogger.instance.logEvent('scheduleCallPopUpDismissed', {
              'buttonPressed': 'callNotNeeded',
            });
            Navigator.of(context).pop();
            Mutations.instance.job(job.documentReference.id).skipCall();
          },
          title: Text(S.of(context).notNeeded),
        ),
      ],
    );
  }

  static bool shouldShow(BuildContext context, Job job) {
    final isUserCraftsman = context.authState.isUserCraftsman();

    return isUserCraftsman && job.shouldScheduleCall;
  }
}

class CancelScheduledCallDialog extends StatelessWidget {
  const CancelScheduledCallDialog({super.key, required this.job});
  final Job job;
  @override
  Widget build(BuildContext context) {
    return DoneDialog(
      title: Text(S.of(context).cancelScheduledCall),
      content: [
        Text(S.of(context).cancelScheduledCallAlertBody),
        const VerticalMargin.medium(),
        DoneButton(
          title: Text(S.of(context).cancel),
          style: DoneButtonStyle.secondary,
          onPressed: () {
            final cancellingUser = context.authState.user!.documentReference;
            final scheduledCallCancellation = ScheduledJobEventCancellation(
              scheduledTime: job.scheduledCallTime!,
              by: cancellingUser,
            );

            Mutations.instance
                .job(job.documentReference.id)
                .cancelScheduledEvent(
                  ScheduleType.videoCall,
                  scheduledCallCancellation,
                );

            Navigator.pop(context);
          },
        ),
        const VerticalMargin(margin: 6),
        DoneButton(
          title: Text(S.of(context).skip),
          style: DoneButtonStyle.neutral,
          onPressed: () => Navigator.pop(context),
        ),
      ],
    );
  }
}
