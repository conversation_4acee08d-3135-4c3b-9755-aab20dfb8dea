import 'package:done_auth/done_auth.dart';
import 'package:done_booking/done_booking.dart';
import 'package:done_flutter/core/router/routes/project_routes.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_flutter/ui/widgets/cards/customer_card.dart';
import 'package:done_flutter/utils/extensions/models/job.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

void showDuplicateJobDialog({required BuildContext context, required Job job}) {
  showAdaptivePopup<void>(
    context: context,
    builder: (context) => DuplicateJobDialog(job: job),
  );
}

class DuplicateJobDialog extends StatefulWidget {
  const DuplicateJobDialog({required this.job, super.key});
  final Job job;

  @override
  State<DuplicateJobDialog> createState() => _DuplicateJobDialogState();
}

class _DuplicateJobDialogState extends State<DuplicateJobDialog> {
  late final TextEditingController _descriptionController;
  late ServiceType _service;
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    final jobServices = widget.job.services;
    _service =
        jobServices == null || jobServices.isEmpty
            ? ServiceType.values.first
            : serviceTypeFrom(jobServices.first)!;
    _descriptionController = TextEditingController(text: '');
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: DonePopup(
        title: Text(S.of(context).createNewProject),
        content: <Widget>[
          _buildCustomerSection(context),
          _buildNewProjectInfoForm(context),
          DoneAsyncAction(
            action: _duplicateJob,
            builder:
                (context, actionOrNull, isLoading) => DoneButton(
                  title:
                      isLoading
                          ? const DoneAdaptiveLoadingIndicator()
                          : Text(S.of(context).createProject),
                  style: DoneButtonStyle.secondary,
                  onPressed: actionOrNull,
                ),
          ),
          DoneButton(
            title: Text(S.of(context).cancel),
            style: DoneButtonStyle.neutral,
            onPressed: () => Navigator.of(context).pop(),
          ),
        ].separatedBy(() => const VerticalMargin.medium()),
      ),
    );
  }

  Form _buildNewProjectInfoForm(BuildContext context) {
    return Form(
      key: _formKey,
      child: Column(
        children: [
          DoneFormSingleSelection<ServiceType>(
            title: Text(S.of(context).serviceType),
            choices: ServiceType.values,
            choicesTitleConverter: (context, service) => service.title(context),
            cancelText: S.of(context).cancel,
            initialValue: _service,
            onChanged: (service) => setState(() => _service = service),
          ),
          const VerticalMargin.medium(),
          DoneFormFieldHolder(
            title: S.of(context).quoteItemDescriptionHint,
            widget: TextFormField(
              keyboardType: TextInputType.multiline,
              minLines: 5,
              maxLines: 5,
              controller: _descriptionController,
              validator: FormValidators.compose(context, [
                FormValidators.required,
              ]),
              textCapitalization: TextCapitalization.sentences,
              style: Theme.of(
                context,
              ).textTheme.bodyMedium!.apply(color: context.doneColors.uiBlack),
              decoration: DoneFormFieldHolder.inputDecoration(
                textStyle: Theme.of(context).textTheme.bodyLarge,
                context: context,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCustomerSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        const VerticalMargin.medium(),
        DoneDetailsSection(
          title: S.of(context).customer,
          titleStyle: Theme.of(context).textTheme.labelMedium!.copyWith(
            color: context.doneColors.typographyLowContrast,
          ),
          dense: true,
          children: [
            CustomerCard(
              customerRef: widget.job.customer,
              hasSubtitle: false,
              onTap: () {
                final params = ProjectCustomerProfileRouteParams(
                  customerId: widget.job.customer.id,
                  projectId: widget.job.id,
                );
                ProjectCustomerProfileRoute(params: params).navigate(context);
              },
            ),
          ],
        ),
      ],
    );
  }

  Future<void> _duplicateJob() async {
    final isFromValid = _formKey.currentState!.validate();
    if (!isFromValid) return;
    final copy = await widget.job.duplicateToDone(
      createdBy: context.authState.user!.documentReference,
      newDescription: _descriptionController.text,
      service: _service,
    );
    if (mounted) Navigator.of(context).pop();

    final params = ProjectDetailsRouteParams(projectId: copy.id);
    ProjectDetailsRoute(params: params).navigate(context);
  }
}
