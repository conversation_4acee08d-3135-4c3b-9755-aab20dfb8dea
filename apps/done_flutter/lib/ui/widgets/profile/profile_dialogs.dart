import 'package:cloud_functions/cloud_functions.dart';
import 'package:done_auth/done_auth.dart';

import 'package:done_localizations/done_localizations.dart';
import 'package:done_flutter/utils/ask_reason.dart';
import 'package:done_flutter/utils/extensions/enums/delete_account_reason.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

void signOutDialog(BuildContext context) {
  showDialog<void>(
    context: context,
    builder:
        (BuildContext context) => DoneDialog(
          title: Text(S.of(context).profileSignOut),
          content: [
            Text(
              S.of(context).areYouSureSignout,
              style: Theme.of(context).textTheme.bodyMedium!.apply(
                color: context.doneColors.uiOnPrimary,
              ),
            ),
            const VerticalMargin.medium(),
            DoneButton(
              key: const Key("SignOutDialogButton"),
              style: DoneButtonStyle.negative,
              title: Text(S.of(context).profileSignOut),
              onPressed: () {
                Navigator.of(context).pop();
                BlocProvider.of<AuthCubit>(context).signOut();
              },
            ),
            const VerticalMargin(margin: 6),
            DoneButton(
              style: DoneButtonStyle.neutral,
              title: Text(S.of(context).cancel),
              onPressed: () async {
                Navigator.of(context).pop();
              },
            ),
          ],
        ),
  );
}

typedef OnSaveProfileChanges = Future<void> Function();

/// promts the user to save new changes before leaving profile page
void showYouHaveChangesDialog(
  BuildContext context,
  OnSaveProfileChanges saveChanges,
) {
  showDialog<void>(
    context: context,
    builder: (BuildContext context) {
      return DoneDialog(
        title: Text(S.of(context).changesHaveBeenMade),
        content: [
          DoneButton(
            title: Text(S.of(context).save),
            style: DoneButtonStyle.secondary,
            onPressed: () {
              saveChanges();
              Navigator.pop(context);
            },
          ),
          const VerticalMargin(margin: 6),
          DoneButton(
            title: Text(S.of(context).discardChanges),
            style: DoneButtonStyle.negative,
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop();
            },
          ),
        ],
      );
    },
  );
}

// ** Delete account dialogs **

Future<void> showDeleteAccountDialog(BuildContext context) {
  return _askDeletionReason(context);
}

Future<void> _askDeletionReason(BuildContext context) async {
  final result = await askReason<DeleteAccountReason>(
    context: context,
    reasons: DeleteAccountReason.values,
    title: Text(S.of(context).deleteAccount),
    reasonTitleBuilder: (context, choice) => Text(choice.title(context)),
    actionTitle: S.of(context).genericContinue,
  );
  if (result == null) return;
  final shouldDelete = await _confirmAccountDeletion(context);

  if (shouldDelete) {
    final request = DeleteAccountRequest(
      result.reason,
      deleteAccountReasonOther: result.otherText,
    );

    await FirebaseFunctions.instanceFor(
      region: 'europe-west1',
    ).httpsCallable('deleteAccountRequest').call<void>(request.toMap());
  }
}

Future<bool> _confirmAccountDeletion(BuildContext context) async {
  final shouldDelete = await showCupertinoModalPopup<bool>(
    context: context,
    builder:
        (context) => DoneBottomSheet(
          title: Text(S.of(context).deleteAccount),
          subtitle: Text(S.of(context).accountDeletionPrompt),
          content: [
            DoneButton(
              title: Text(S.of(context).deleteAccount),
              style: DoneButtonStyle.negative,
              onPressed: () => Navigator.of(context).pop(true),
            ),
            DoneButton(
              title: Text(S.of(context).cancel),
              style: DoneButtonStyle.neutral,
              onPressed: () => Navigator.of(context).pop(false),
            ),
          ],
        ),
  );
  return shouldDelete ?? false;
}
