import 'package:done_localizations/done_localizations.dart';
import 'package:done_flutter/invoicing/widgets/invoice_card.dart';
import 'package:done_flutter/ui/pages/chat/chat_card_message.dart';
import 'package:done_flutter/ui/widgets/cards/quote_card.dart';
import 'package:done_flutter/ui/widgets/chat/chat_ui_mixin.dart';
import 'package:done_flutter/ui/widgets/chat/message_types/call_missed_or_declined.dart';
import 'package:done_flutter/ui/widgets/chat/message_types/chat_call_finished.dart';
import 'package:done_flutter/ui/widgets/chat/message_types/chat_cancelled_schedule.dart';
import 'package:done_flutter/ui/widgets/chat/message_types/chat_file_message.dart';
import 'package:done_flutter/ui/widgets/chat/message_types/chat_image_message.dart';
import 'package:done_flutter/ui/widgets/chat/message_types/chat_text_message.dart';
import 'package:done_flutter/ui/widgets/chat/message_types/chat_info_message.dart';
import 'package:done_flutter/ui/widgets/chat/video_message.dart';
import 'package:done_models/done_models.dart';
import 'package:flutter/material.dart';

/// Takes in a message and decides which kind of [MessageType] should be returned for it.
class ChatMessageWidget extends StatelessWidget with ChatUiMixin {
  const ChatMessageWidget({
    required this.message,
    required this.job,
    required this.isPreviousMessageCoalesced,
  });

  final ChatMessage message;
  final Job job;
  final bool isPreviousMessageCoalesced;

  @override
  Widget build(BuildContext context) {
    switch (message.type) {
      case MessageType.cancelledScheduledWorkTime:
      case MessageType.cancelledBookedCall:
        return ChatCancelledSchedule(message: message);
      case MessageType.text:
        return ChatTextMessage(
          key: const Key("ChatTextMessage"),
          message: message as TextChatMessage,
          isPreviousMessageCoalesced: isPreviousMessageCoalesced,
        );
      case MessageType.info:
        return ChatInfoMessage(
          message: message as InfoChatMessage,
          isPreviousMessageCoalesced: isPreviousMessageCoalesced,
        );
      case MessageType.image:
        return ChatImageMessage(message as ImageChatMessage, jobId: job.id);
      case MessageType.file:
        return ChatFileMessage(
          message: message as FileChatMessage,
          jobId: job.id,
          isPreviousMessageCoalesced: isPreviousMessageCoalesced,
        );
      case MessageType.video:
        return VideoMessage(
          message: message as VideoChatMessage,
          jobId: job.id,
        );
      case MessageType.offer:
        final quoteCard = QuoteCard(
          quote: (message as QuoteChatMessage).quote,
          jobId: job.id,
          isChatItem: true,
        );

        return ChatCardMessage(
          message: message,
          isPreviousMessageCoalesced: isPreviousMessageCoalesced,
          child: quoteCard,
        );
      case MessageType.invoice:
        final invoiceCard = InvoiceCard(
          invoice: (message as InvoiceChatMessage).invoice,
          job: job,
          isChatItem: true,
        );

        return ChatCardMessage(
          message: message,
          isPreviousMessageCoalesced: isPreviousMessageCoalesced,
          child: invoiceCard,
        );

      case MessageType.call:
        return ChatCallFinished(message: message as CallChatMessage, job: job);
      case MessageType.missedCall:
      case MessageType.declinedCall:
        return CallMissedOrDeclined(message: message as CallChatMessage);
      default:
        return Padding(
          padding: const EdgeInsets.all(16),
          child: Text(
            S.of(context).chatUnknown,
            style: Theme.of(context).textTheme.bodySmall,
          ),
        );
    }
  }
}
