import 'package:done_models/done_models.dart';
import 'package:done_router/done_router.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'dart:convert';

class MetadataPreview extends StatelessWidget {
  const MetadataPreview({
    required this.metadata,
    this.isOwnMessage = false,
    super.key,
  });

  final RichMetadata metadata;
  final bool isOwnMessage;

  @override
  Widget build(BuildContext context) {
    final uri = Uri.tryParse(metadata.url ?? '');
    return InkWell(
      onTap:
          uri != null
              ? () => URLRouter.instance.open(
                uri,
                router: context.router,
                pushDoneRoute: true,
              )
              : null,
      borderRadius: BorderRadius.circular(Margins.medium).copyWith(
        bottomLeft: Radius.circular(
          isOwnMessage ? Margins.medium : Margins.medium / 2,
        ),
        bottomRight: Radius.circular(
          isOwnMessage ? Margins.medium / 2 : Margins.medium,
        ),
      ),
      child: DecoratedBox(
        decoration: BoxDecoration(
          border: Border.all(color: context.doneColors.uiBg2),
          borderRadius: BorderRadius.circular(Margins.medium).copyWith(
            bottomLeft: Radius.circular(
              isOwnMessage ? Margins.medium : Margins.medium / 2,
            ),
            bottomRight: Radius.circular(
              isOwnMessage ? Margins.medium / 2 : Margins.medium,
            ),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (metadata.image != null)
              ClipRRect(
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(Margins.medium),
                ),
                child: ConstrainedBox(
                  constraints: const BoxConstraints(
                    maxHeight: 130,
                    minHeight: 20,
                    minWidth: double.infinity,
                  ),
                  child: _buildImage(metadata.image!),
                ),
              ),
            Padding(
              padding: const EdgeInsets.all(Margins.small),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Row(
                    children: [
                      if (metadata.logo != null)
                        Padding(
                          padding: const EdgeInsets.only(right: Margins.small),
                          child: SizedBox(
                            height: Margins.xxlarge,
                            width: Margins.xxlarge,
                            child: _buildImage(
                              metadata.logo!,
                              fit: BoxFit.contain,
                            ),
                          ),
                        ),
                      if (metadata.url != null)
                        Text(
                          Uri.parse(metadata.url!).host.toUpperCase(),
                          style: Theme.of(
                            context,
                          ).textTheme.bodyMedium!.copyWith(
                            color: context.doneColors.typographyLowContrast,
                          ),
                        ),
                    ],
                  ),
                  if (metadata.title != null)
                    Text(
                      metadata.title!,
                      style: Theme.of(context).textTheme.labelMedium,
                    ),
                  if (metadata.description != null) Text(metadata.description!),
                ].separatedBy(() => const VerticalMargin.verySmall()),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

Widget _buildImage(String imageUrl, {BoxFit fit = BoxFit.cover}) {
  if (imageUrl.startsWith('data:image')) {
    return Image.memory(base64Decode(imageUrl.split(',')[1]), fit: fit);
  }
  return Image.network(imageUrl, fit: fit);
}
