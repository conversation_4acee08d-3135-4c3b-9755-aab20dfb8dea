import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

class ChatAvatar extends StatelessWidget {
  const ChatAvatar(this.iconPath);

  final String iconPath;

  @override
  Widget build(BuildContext context) {
    return CircleAvatar(
      backgroundColor: context.doneColors.uiBg2,
      child: Image.asset(
        iconPath,
        height: 22,
        color: context.doneColors.uiOnPrimary,
      ),
      radius: 22,
    );
  }
}
