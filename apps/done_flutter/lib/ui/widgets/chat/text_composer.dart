import 'package:done_analytics/done_analytics.dart';
import 'package:done_flutter/core/enums/document_type.dart';
import 'package:done_auth/done_auth.dart';
import 'package:done_flutter/core/services/chat_service.dart' show Chat;
import 'package:done_localizations/done_localizations.dart';
import 'package:done_image/done_image.dart';
import 'package:done_flutter/utils/file_picker_util.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Holds the UI and logic responsible for sending
/// text or media from the chat page
class TextComposer extends StatefulWidget {
  const TextComposer({super.key, required this.job});

  final Job job;

  @override
  TextComposerState createState() => TextComposerState();
}

class TextComposerState extends State<TextComposer>
    with SingleTickerProviderStateMixin {
  late final AnimationController _animationController;
  late final FocusNode _textBoxFocusNode;
  late final FocusNode _textFieldFocusNode;
  late bool _isAttatchmentsVisible;
  late final TextEditingController _textController;

  @override
  void initState() {
    super.initState();
    _textController = TextEditingController();
    _isAttatchmentsVisible = true;
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 250),
      vsync: this,
    );

    IntTween(
      begin: 200,
      end: 800,
    ).animate(_animationController).addListener(() => setState(() {}));

    _textBoxFocusNode = FocusNode();
    _textFieldFocusNode = FocusNode();
    _textBoxFocusNode.addListener(() {
      showAttachmentIcons(!_textBoxFocusNode.hasFocus);
    });
    _textFieldFocusNode.addListener(() {
      showAttachmentIcons(!_textFieldFocusNode.hasFocus);
    });
  }

  @override
  void dispose() {
    _textBoxFocusNode.dispose();
    _textFieldFocusNode.dispose();
    _animationController.dispose();
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // TODO: Evaluate this popup to see if people actually use it
    // if (!widget.job.isFirstMessageSentByCraftsman &&
    //     (context.authState.isUserCraftsman())) {
    //   SchedulerBinding.instance.addPostFrameCallback((_) {
    //     if (!mounted || _isAutomaticMessageShown) return;
    //     _isAutomaticMessageShown = true;
    //     showAutoMessagePopup(
    //       context: context,
    //       job: widget.job,
    //       onMessageSelected: (message) {
    //         _textController.text = message;
    //         _textFieldFocusNode.requestFocus();
    //       },
    //     );
    //   });
    // }
    return IconTheme(
      data: IconThemeData(color: Theme.of(context).primaryColor),
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 8),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: <Widget>[
            _buildAttatchmentsToggler(),
            _buildInputWithSendButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildAttatchmentsToggler() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: AnimatedCrossFade(
        duration: const Duration(milliseconds: 200),
        //* First child are the attatchment actions
        firstChild: Wrap(
          children: <Widget>[
            if (!kIsWeb) ...[
              const HorizontalMargin.verySmall(),
              _buildTogglerItem(
                onTap: () => _selectMediaType(ImageSource.camera),
                imageAsset: ImageAssets.takePhoto,
                key: "CameraChatButton",
              ),
            ],
            const HorizontalMargin(margin: 6),
            _buildTogglerItem(
              onTap: () => _selectMediaType(ImageSource.gallery),
              imageAsset: ImageAssets.attachGallery,
              key: "GalleryChatButton",
            ),
            const HorizontalMargin(margin: 6),
            _buildTogglerItem(
              onTap: _sendDocument,
              imageAsset: ImageAssets.attachFile,
              key: "FileChatButton",
            ),
            const HorizontalMargin.small(),
          ],
        ),
        //* Second child is the toggle button
        secondChild: InkWell(
          child: Row(
            children: <Widget>[
              const HorizontalMargin.small(),
              Icon(Icons.arrow_forward_ios, color: context.doneColors.uiPurple),
              const HorizontalMargin.small(),
            ],
          ),
          onTap: () => showAttachmentIcons(true),
        ),
        crossFadeState:
            _isAttatchmentsVisible
                ? CrossFadeState.showFirst
                : CrossFadeState.showSecond,
      ),
    );
  }

  Widget _buildTogglerItem({
    required Function onTap,
    required String imageAsset,
    required String key,
  }) {
    return InkWell(
      key: Key(key),
      onTap: () => onTap(),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
        child: Image.asset(
          imageAsset,
          height: 20,
          width: 20,
          color: context.doneColors.uiPurple,
        ),
      ),
    );
  }

  Widget _buildInputWithSendButton() {
    return Expanded(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: <Widget>[
          _buildTextInput(),
          Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: _buildSendButton(),
          ),
        ],
      ),
    );
  }

  Widget _buildTextInput() {
    final screenWidth = MediaQuery.of(context).size.width;
    return Expanded(
      child: Column(
        children: [
          if (kIsWeb)
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Text(
                  S.of(context).pressToSendKey,
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
          Scrollbar(
            child: KeyboardListener(
              focusNode: _textBoxFocusNode,
              onKeyEvent: handleKeyPress,
              child: CupertinoTextField(
                key: const Key("ChatTextField"),
                focusNode: _textFieldFocusNode,
                style: Theme.of(context).textTheme.bodyLarge!.apply(
                  color: context.doneColors.uiOnPrimary,
                ),
                keyboardAppearance: Theme.of(context).brightness,
                decoration: BoxDecoration(
                  color: context.doneColors.uiPrimary,
                  border: Border.all(color: context.doneColors.uiBg1),
                  borderRadius: const BorderRadius.all(Radius.circular(18)),
                ),
                autofocus: kIsWeb,
                controller: _textController,
                onTap: () => showAttachmentIcons(false),
                onChanged: (String text) {
                  showAttachmentIcons(false);
                  if (kIsWeb)
                    setState(
                      () {},
                    ); // Needed to re-evaluate send-button enabled-state
                },
                onSubmitted: _handleSubmitted,
                placeholder: S.of(context).chatNewMessage,
                placeholderStyle: Theme.of(context).textTheme.bodyMedium!.apply(
                  color: context.doneColors.typographyLowContrast,
                ),
                textCapitalization: TextCapitalization.sentences,
                minLines: screenWidth > Layouts.wideLayout ? 3 : 1,
                maxLines: 10,
                keyboardType: TextInputType.multiline,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSendButton() {
    return InkWell(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12),
        child: Icon(
          Icons.send,
          key: const Key("SendChatButton"),
          size: 25,
          color:
              _textController.text.isNotEmpty
                  ? context.doneColors.uiPurple
                  : context.doneColors.typographyLowContrast,
        ),
      ),
      onTap:
          _textController.text.isNotEmpty
              ? () => _handleSubmitted(_textController.text)
              : null,
    );
  }

  Future<void> _sendMedia(
    ImageSource source, {
    MediaType type = MediaType.all,
  }) async {
    FocusScope.of(context).requestFocus(FocusNode()); // Close Keyboard

    final pickedFiles = await pickMedia(context, source: source, type: type);
    if (pickedFiles.isEmpty) {
      return; // User cancelled
    }

    await EventLogger.instance.logEvent('sendMediaInChat', {
      'type': type.name,
      'source': source == ImageSource.camera ? 'camera' : 'library',
    });

    final chatItems =
        pickedFiles.map((pickedFile) {
          final extension = pickedFile.path.split('.').last;
          final type = getFileTypeFromExtension(extension);
          return ChatItem(pickedFile, type ?? ChatItemType.image);
        }).toList();

    await Chat(widget.job.documentReference).sendItems(
      chatItems,
      currentUser: context.authState.user!.documentReference,
    );
  }

  Future<void> _sendDocument() async {
    final filePickingResult = await pickFile(DocumentType.values);

    if (filePickingResult == null) return;

    ChatItemType fileType;
    switch (filePickingResult.type) {
      case DocumentType.pdf:
        fileType = ChatItemType.pdf;
        break;
      case DocumentType.txt:
        fileType = ChatItemType.textFile;
        break;
    }

    await EventLogger.instance.logEvent('sendFileInChat', {
      'type': filePickingResult.type.name,
    });

    return Chat(widget.job.documentReference).sendItem(
      ChatItem(filePickingResult.file, fileType),
      currentUser: context.authState.user!.documentReference,
    );
  }

  Future<void> _handleSubmitted(String text) async {
    final trimmedText = text.trim();

    if (trimmedText.isEmpty) return;

    _textController.clear();

    showAttachmentIcons(false);
    final isUserCraftsman = context.authState.isUserCraftsman();

    final sender = context.authState.user!.documentReference;
    await Chat(widget.job.documentReference).sendTextMessage(
      text,
      currentUser: sender,
      isUserCraftsman: isUserCraftsman,
    );

    await EventLogger.instance.logEvent('messageSent');
  }

  void showAttachmentIcons(bool show) {
    final isWideLayout = MediaQuery.of(context).size.width > Layouts.wideLayout;

    if (!isWideLayout) {
      if (!show) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
      setState(() {
        _isAttatchmentsVisible = show;
      });
    }
  }

  void _selectMediaType(ImageSource source) {
    // If source is gallery, we can directly send the media, no need to show the picker
    if (source == ImageSource.gallery) {
      _sendMedia(source);
      return;
    }
    // Otherwise, show the picker to select the media type
    showCupertinoModalPopup<void>(
      context: context,
      builder: (context) {
        return DoneBottomSheet(
          content: [
            DoneButton(
              centered: false,
              image: Icon(Icons.image, color: context.doneColors.uiBlack),
              style: DoneButtonStyle.neutral,
              title: Text(S.of(context).attachmentImage),
              onPressed: () {
                Navigator.of(context).pop();
                _sendMedia(source, type: MediaType.image);
              },
            ),
            const VerticalMargin.medium(),
            DoneButton(
              centered: false,
              image: Icon(
                Icons.video_library,
                color: context.doneColors.uiBlack,
              ),
              style: DoneButtonStyle.neutral,
              title: Text(S.of(context).attachmentVideo),
              onPressed: () {
                Navigator.of(context).pop();
                _sendMedia(source, type: MediaType.video);
              },
            ),
            const VerticalMargin.xlarge(),
            DoneButton(
              style: DoneButtonStyle.secondary,
              title: Text(
                S.of(context).cancel,
                style: const TextStyle(fontWeight: FontWeight.w600),
              ),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  void handleKeyPress(KeyEvent _) {
    if (HardwareKeyboard.instance.isControlPressed ||
        HardwareKeyboard.instance.isMetaPressed) {
      if (HardwareKeyboard.instance.isLogicalKeyPressed(
        LogicalKeyboardKey.enter,
      ))
        _handleSubmitted(_textController.text);
    }
  }
}
