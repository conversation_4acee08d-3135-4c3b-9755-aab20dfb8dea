import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:done_flutter/ui/widgets/user_avatar.dart';
import 'package:flutter/material.dart';

/// Used in chat related widgets, has common helpers
mixin ChatUiMixin {
  static const _avatarRadius = 22.0;

  EdgeInsets buildMessageMargins(
    bool isOwnMessage,
    bool isPreviousMessageCoalesced,
  ) {
    const standardInsets = Margins.medium;

    // Corresponds to the total inset if a message has a leading avatar
    const avatarInsets = standardInsets + 2 * _avatarRadius + Margins.small;

    // Used to force a non-overlap when chatting back & forth so it's easy to see who's message is who's.
    const overlapInset = 40.0;

    return EdgeInsets.only(
      left:
          isOwnMessage
              ? overlapInset + avatarInsets
              : isPreviousMessageCoalesced
              ? avatarInsets
              : standardInsets,
      right: isOwnMessage ? standardInsets : overlapInset,
    );
  }

  bool shouldShowAvatar({
    required bool isOwnMessage,
    required bool isPreviousMessageCoalesced,
  }) => !isOwnMessage && !isPreviousMessageCoalesced;

  BoxDecoration chatBoxDecoration(BuildContext context) => BoxDecoration(
    border: Border.all(color: context.doneColors.uiBg1),
    borderRadius: BorderRadius.circular(8),
  );

  /// Builds the user avatar for the sender of a message + adds right margin to actual message
  Widget buildMessageAvatarWithSpacing({
    required DocumentReference<User> user,
  }) => Container(
    margin: const EdgeInsets.only(right: Margins.small),
    child: UserAvatar(user, radius: _avatarRadius),
  );
}
