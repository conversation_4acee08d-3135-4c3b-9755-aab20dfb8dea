import 'package:done_analytics/done_analytics.dart';
import 'package:done_flutter/utils/extensions/models/predrafted_messages.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_flutter/core/blocs/admin_configuration_cubit/admin_configuration_cubit.dart';
import 'package:done_flutter/utils/first_build_callback.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

Future<void> showAutoMessagePopup({
  required BuildContext context,
  required Job job,
  required ValueChanged<String> onMessageSelected,
}) async {
  final message = await showAdaptivePopup<String>(
    context: context,
    builder: (context) => AutoMessagePopup(job: job),
  );
  if (message != null) onMessageSelected(message);
}

class AutoMessagePopup extends StatefulWidget {
  const AutoMessagePopup({required this.job, super.key});
  final Job job;

  @override
  State<AutoMessagePopup> createState() => _AutoMessagePopupState();
}

class _AutoMessagePopupState extends State<AutoMessagePopup> {
  PredraftedMessagesEntry? _selectedMessage;
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AdminConfigurationCubit, AdminConfiguration?>(
      builder: (context, state) {
        final messages = state?.predraftedMessages?.messagesFor(
          widget.job.tags ?? [],
        );

        return FirstBuildCallback(
          onFirstBuild: () async {
            if (messages == null) {
              Navigator.of(context).pop();
            } else {
              SchedulerBinding.instance.addPostFrameCallback((_) {
                if (mounted) setState(() => _selectedMessage = messages.first);
              });
            }
          },
          child: DonePopup(
            title: Text(S.of(context).newProjectsSection),
            subtitle: Text(S.of(context).selectMessagePresetDescription),
            content: <Widget>[
              _SelectedMessagePreview(
                message: _selectedMessage.replaceTemplatesOnValue(
                  context,
                  widget.job,
                ),
              ),
              if (messages != null)
                ...messages.map(
                  (entry) => _PredraftedMessageButton(
                    job: widget.job,
                    entry: entry,
                    selected: entry == _selectedMessage,
                    onPressed: () {
                      if (mounted) setState(() => _selectedMessage = entry);
                    },
                  ),
                ),
              DoneButton(
                style:
                    _selectedMessage == null
                        ? DoneButtonStyle.selected
                        : DoneButtonStyle.neutral,
                title: Flexible(child: Text(S.of(context).iWriteMyOwn)),
                onPressed: () => setState(() => _selectedMessage = null),
              ),
              DoneButton(
                style: DoneButtonStyle.secondary,
                title: Flexible(child: Text(S.of(context).selectionFieldHint)),
                onPressed: () {
                  final message = _selectedMessage.replaceTemplatesOnValue(
                    context,
                    widget.job,
                  );
                  EventLogger.instance.logEvent('preset_message_picked', {
                    'value':
                        _selectedMessage == null
                            ? 'Craftsman will write own'
                            : message,
                  });
                  Navigator.of(context).pop(message);
                },
              ),
            ].separatedBy(() => const VerticalMargin.small()),
          ),
        );
      },
    );
  }
}

class _PredraftedMessageButton extends StatelessWidget {
  const _PredraftedMessageButton({
    required this.job,
    required this.entry,
    required this.onPressed,
    this.selected = false,
  });
  final bool selected;
  final PredraftedMessagesEntry entry;
  final VoidCallback onPressed;
  final Job job;

  @override
  Widget build(BuildContext context) {
    return DoneButton(
      style: selected ? DoneButtonStyle.selected : DoneButtonStyle.neutral,
      title: Flexible(child: Text(entry.label)),
      onPressed: onPressed,
    );
  }
}

class _SelectedMessagePreview extends StatelessWidget {
  const _SelectedMessagePreview({required this.message});
  final String message;
  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(Margins.small),
        border: Border.all(color: context.doneColors.purple),
        color: context.doneColors.purple,
      ),
      child: Padding(
        padding: const EdgeInsets.all(Margins.small),
        child: Text(
          message,
          style: Theme.of(context).textTheme.labelMedium!.copyWith(
            color: context.doneColors.uiPrimary,
            fontWeight: FontWeight.normal,
          ),
        ),
      ),
    );
  }
}
