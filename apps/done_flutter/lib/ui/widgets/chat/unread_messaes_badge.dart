import 'package:done_auth/done_auth.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

class UnreadMessagesBadge extends StatelessWidget {
  const UnreadMessagesBadge({
    required this.job,
    this.brightness = Brightness.dark,
    this.isDense = false,
    super.key,
  });

  final Job job;
  final Brightness brightness;
  final bool isDense;

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future: job.unreadMessages(context.authState.user!.documentReference.id),
      builder: (context, snapshot) {
        final isLoading = snapshot.connectionState != ConnectionState.done;
        final shouldHide = isLoading || (snapshot.data ?? 0) == 0;

        // Wrapping with Visibility to avoid having the badge change layout when it shows vs not.
        return Visibility(
          visible: !shouldHide,
          maintainSize: true,
          maintainAnimation: true,
          maintainState: true,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(20),
            child: ColoredBox(
              color: brightness.backgroundColor(context),
              child: Padding(
                padding: EdgeInsets.symmetric(
                  vertical: 4,
                  horizontal: isDense ? 10 : 12,
                ),
                child: Text(
                  shouldHide ? ' ' : _title(context, snapshot.data!),
                  style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                    fontWeight: FontWeight.w500,
                    color: brightness.textColor(context),
                    fontSize: isDense ? 13 : 15,
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  String _title(BuildContext context, int count) {
    if (count == 1) {
      return isDense ? S.of(context).oneUnread : S.of(context).oneUnreadMessage;
    } else {
      return isDense
          ? S.of(context).unread(count)
          : S.of(context).unreadMessages(count);
    }
  }
}

extension _UnreadMessagesColors on Brightness {
  Color textColor(BuildContext context) {
    switch (this) {
      case Brightness.dark:
        return context.doneColors.typographyHightContrast;
      case Brightness.light:
        return context.doneColors.uiAlwaysPureWhite;
    }
  }

  Color backgroundColor(BuildContext context) {
    switch (this) {
      case Brightness.dark:
        return context.doneColors.uiPrimary;
      case Brightness.light:
        return context.doneColors.uiPurple;
    }
  }
}
