import 'package:cloud_firestore/cloud_firestore.dart';

import 'package:done_database/done_database.dart';
import 'package:done_flutter/core/router/routes/project_routes.dart';

import 'package:done_flutter/ui/widgets/chat/chat_ui_mixin.dart';
import 'package:done_flutter/ui/widgets/chat/message_info_row.dart';
import 'package:done_flutter/ui/widgets/radial_progress.dart';
import 'package:done_image/done_image.dart';
import 'package:done_models/done_models.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:done_ui/done_ui.dart';
import 'package:done_auth/done_auth.dart';
import 'package:get_it/get_it.dart';

class VideoMessage extends StatefulWidget {
  const VideoMessage({super.key, required this.message, required this.jobId});

  final VideoChatMessage message;
  final String jobId;

  @override
  _VideoMessageState createState() => _VideoMessageState();
}

class _VideoMessageState extends State<VideoMessage>
    with SingleTickerProviderStateMixin, ChatUiMixin {
  late final AnimationController _progressAnimationController;
  VideoProgressStatus currentProgress = VideoProgressStatus.notStarted;

  @override
  void initState() {
    super.initState();

    _progressAnimationController = AnimationController(
      vsync: this,
      value: 0,
      duration: const Duration(milliseconds: 2000),
    );

    if (widget.message.status == VideoProgressStatus.uploadFinished) {
      _progressAnimationController.value = 1.0;
    }
  }

  @override
  void dispose() {
    _progressAnimationController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final senderRef = widget.message.sender;
    final isOwnMessage = context.authState.isCurrentUser(senderRef);

    return StreamBuilder<User>(
      stream: GetIt.instance<UserRepository>().user(senderRef.id),
      builder: (context, senderSnapshot) {
        widget.message.documentReference!.snapshots().listen((
          DocumentSnapshot<ChatMessage> snapshot,
        ) {
          final snapshotStatus =
              (snapshot.data() as VideoChatMessage?)?.status ??
              VideoProgressStatus.inProgress;
          if (snapshotStatus == VideoProgressStatus.thumbnailUploaded &&
              currentProgress == VideoProgressStatus.notStarted) {
            currentProgress = VideoProgressStatus.thumbnailUploaded;

            _progressAnimationController
              ..forward(from: 0)
              ..animateTo(0.33);
          }
          if (snapshotStatus == VideoProgressStatus.encodeFinished &&
              currentProgress == VideoProgressStatus.thumbnailUploaded) {
            currentProgress = VideoProgressStatus.encodeFinished;
            _progressAnimationController.animateTo(0.66);
          }
          if (snapshotStatus == VideoProgressStatus.uploadFinished &&
              currentProgress == VideoProgressStatus.encodeFinished) {
            currentProgress = VideoProgressStatus.uploadFinished;
            _progressAnimationController.animateTo(1).then((_) {
              _progressAnimationController.value = 1.0;
            });
          }
        });

        return Container(
          margin: buildMessageMargins(isOwnMessage, false),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              MessageInfoRow(
                sender: senderSnapshot.data,
                message: widget.message,
              ),
              Row(
                mainAxisAlignment:
                    isOwnMessage
                        ? MainAxisAlignment.end
                        : MainAxisAlignment.start,
                children: <Widget>[
                  HorizontalMargin(margin: isOwnMessage ? 0 : 55),
                  _buildVideoContainer(),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Future<String?> _getThumbnailURL() async {
    return widget.message.thumbRef == null
        ? null
        : await getDownloadUrl(widget.message.thumbRef!);
  }

  Widget _buildVideoContainer() {
    return Container(
      margin: const EdgeInsets.only(top: 5, bottom: 5),
      child: FutureBuilder<String?>(
        future: _getThumbnailURL(),
        builder: (context, snapshot) {
          const maxSideLength = 220.0;

          return SizedBox(
            height: maxSideLength,
            width: maxSideLength,
            child: _buildMessageVideoHolder(snapshot.data),
          );
        },
      ),
    );
  }

  Widget _buildMessageVideoHolder(String? thumbnailUrl) {
    return GestureDetector(
      onTap: () async {
        // This is rare case but videoRef can be null if upload didn't complete normally
        if (widget.message.videoRef == null) return;
        final params = VideoPlayerRouteParams(
          projectId: widget.jobId,
          videoRef: widget.message.videoRef!,
          aspectRatio: widget.message.aspectRatio,
        );
        VideoPlayerRoute(params: params).navigate(context);
      },
      child: Stack(
        alignment: AlignmentDirectional.center,
        children: [
          if (thumbnailUrl != null)
            CachedNetworkImage(
              imageUrl: thumbnailUrl,
              progressIndicatorBuilder:
                  (context, string, progress) =>
                      (progress.progress != 1 || progress.progress == null)
                          ? const CupertinoActivityIndicator()
                          : const SizedBox(),
              imageBuilder:
                  (context, imageProvider) => ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    //Exception: Invalid image data when video is uploaded from the web
                    child: Image(image: imageProvider),
                  ),
            )
          else
            Container(
              height: 200,
              width: 200,
              decoration: BoxDecoration(
                color: context.doneColors.uiBg2,
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          Icon(
            Icons.play_arrow,
            color: context.doneColors.typographyLowContrast,
            size: 80,
          ),
          RadialProgress(controller: _progressAnimationController),
        ],
      ),
    );
  }
}
