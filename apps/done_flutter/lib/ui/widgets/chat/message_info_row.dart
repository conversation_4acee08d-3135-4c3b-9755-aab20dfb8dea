import 'package:done_auth/done_auth.dart';
import 'package:done_flutter/core/services/chat_service.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_flutter/ui/widgets/user_avatar.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

/// Used to display message/user avatar + message info
/// Or just the timestamp if the message displayed belongs to the current user
/// TODO - use this widget at other places like [CallMissedOrDeclined, VideoMessage] etc...
class MessageInfoRow extends StatelessWidget {
  const MessageInfoRow({
    super.key,
    required this.sender,
    required this.message,
  });

  final ChatMessage message;
  final User? sender;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment:
          _isOwnMsg(context) ? MainAxisAlignment.end : MainAxisAlignment.start,
      children: _avatarWithMsgInfo(context),
    );
  }

  bool _isOwnMsg(BuildContext context) =>
      context.authState.isCurrentUser(message.sender);

  List<Widget> _avatarWithMsgInfo(BuildContext context) {
    if (_isOwnMsg(context)) {
      return [_buildMsgInfoForMessageInfoRowWidget(context)];
    }
    return [
      UserAvatar(message.sender, radius: 22),
      const HorizontalMargin.small(),
      _buildMsgInfoForMessageInfoRowWidget(context),
    ];
  }

  Widget _buildMsgInfoForMessageInfoRowWidget(BuildContext context) {
    final isOwnMessage = context.authState.isCurrentUser(message.sender);
    return Expanded(
      child: Column(
        crossAxisAlignment:
            isOwnMessage ? CrossAxisAlignment.end : CrossAxisAlignment.start,
        children: <Widget>[
          Row(
            mainAxisAlignment:
                isOwnMessage ? MainAxisAlignment.end : MainAxisAlignment.start,
            children: <Widget>[
              ..._buildTimeAndUsernameText(context, isOwnMessage, sender),
            ],
          ),
          if (!isOwnMessage)
            Text(
              message.type == MessageType.video
                  ? S.of(context).sentAVideo
                  : S.of(context).sentAPicture,
              style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                color: context.doneColors.typographyMediumContrast,
              ),
            ),
        ],
      ),
    );
  }

  List<Widget> _buildTimeAndUsernameText(
    BuildContext context,
    bool isOwnMessage,
    User? sender,
  ) {
    var widgets = <Widget>[];
    final messageTime = getMessageTime(message.createTime, context);
    if (isOwnMessage) {
      widgets = [
        Text(messageTime, style: Theme.of(context).textTheme.bodySmall),
      ];
    } else {
      widgets = [
        Text(
          sender?.firstName ?? '',
          style: Theme.of(context).textTheme.labelMedium,
        ),
        const HorizontalMargin(margin: 6),
        Text(
          messageTime,
          style: Theme.of(context).textTheme.labelMedium!.copyWith(
            color: context.doneColors.typographyMediumContrast,
          ),
        ),
      ];
    }

    return widgets;
  }
}
