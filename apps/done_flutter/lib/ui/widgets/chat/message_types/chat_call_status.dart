import 'package:done_flutter/utils/extensions/enums/message_type.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

/// Common widget for displaying different call status messages
// ignore: comment_references
/// Takes in 4 different [MessageTypes]
// ignore: comment_references
/// [missedCall], [cancelledBookedCall], [declinedCall], [call]
class ChatCallStatus extends StatelessWidget {
  const ChatCallStatus({
    super.key,
    required this.callText,
    required this.callTime,
    required this.messageType,
  });

  final String callText;
  final String callTime;
  final MessageType messageType;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(left: 12, right: 12, top: 10, bottom: 10),
      child: Row(
        children: <Widget>[
          messageType.avatar,
          const HorizontalMargin.medium(),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Flexible(
                  child: Container(
                    padding: const EdgeInsets.only(right: 10),
                    child: Text(
                      callText,
                      style: messageType.textStyle(context),
                      maxLines: 2,
                    ),
                  ),
                ),
                Text(
                  callTime,
                  style: Theme.of(context).textTheme.labelMedium!.copyWith(
                    color: context.doneColors.typographyMediumContrast,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
