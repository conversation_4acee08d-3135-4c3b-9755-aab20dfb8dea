import 'package:done_auth/done_auth.dart';
import 'package:done_flutter/ui/widgets/chat/chat_ui_mixin.dart';
import 'package:done_flutter/ui/widgets/chat/message_types/chat_image_message.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

/// A widget that displays multiple images in a message.
class MultiImagesMessage extends StatelessWidget with ChatUiMixin {
  const MultiImagesMessage({
    super.key,
    required this.images,
    required this.jobId,
    required this.isPreviousMessageCoalesced,
    this.backgroundColor = Colors.black87,
    this.height = 200,
    this.width,
  });

  /// Color of the background image.
  final Color backgroundColor;

  final String jobId;

  /// List of chat images to display.
  final List<ImageChatMessage> images;

  /// Height of the container.
  ///
  /// If not set, it will be a height of 200.0.
  final double height;

  /// Width of the images' container.
  final double? width;

  final bool isPreviousMessageCoalesced;

  @override
  Widget build(BuildContext context) {
    /// MediaQuery Width
    final defaultWidth = MediaQuery.sizeOf(context).width;

    if (images.length < 4) return const SizedBox();
    final senderRef = images.first.sender;
    final isOwnMessage = context.authState.isCurrentUser(senderRef);
    final imageHeight = (height / 2) - Margins.medium;
    return Container(
      margin: buildMessageMargins(isOwnMessage, isPreviousMessageCoalesced),
      width: width ?? defaultWidth,
      height: height,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          if (shouldShowAvatar(
            isOwnMessage: isOwnMessage,
            isPreviousMessageCoalesced: isPreviousMessageCoalesced,
          ))
            buildMessageAvatarWithSpacing(user: senderRef),
          Expanded(
            child: Align(
              alignment:
                  isOwnMessage ? Alignment.centerRight : Alignment.centerLeft,
              child: Container(
                decoration: BoxDecoration(
                  color:
                      isOwnMessage
                          ? context.doneColors.purple
                          : context.doneColors.uiBg2,
                  borderRadius: BorderRadius.circular(Margins.medium).copyWith(
                    bottomLeft: Radius.circular(
                      isOwnMessage ? Margins.medium : Margins.medium / 2,
                    ),
                    bottomRight: Radius.circular(
                      isOwnMessage ? Margins.medium / 2 : Margins.medium,
                    ),
                  ),
                ),
                margin: const EdgeInsets.only(top: Margins.medium),
                child: Padding(
                  padding: const EdgeInsets.all(Margins.verySmall),
                  child: Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            SingleChatImageView(
                              message: images.first,
                              jobId: jobId,
                              height: imageHeight,
                              borderRadius: BorderRadius.circular(
                                Margins.small,
                              ),
                              fit: BoxFit.cover,
                            ),
                            const VerticalMargin.verySmall(),
                            SingleChatImageView(
                              message: images[2],
                              jobId: jobId,
                              height: imageHeight,
                              borderRadius: BorderRadius.circular(
                                Margins.small,
                              ).copyWith(
                                bottomLeft: Radius.circular(
                                  isOwnMessage
                                      ? Margins.small
                                      : Margins.small / 2,
                                ),
                              ),
                              fit: BoxFit.cover,
                            ),
                          ],
                        ),
                      ),
                      const HorizontalMargin.verySmall(),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            SingleChatImageView(
                              message: images[1],
                              jobId: jobId,
                              height: imageHeight,
                              borderRadius: BorderRadius.circular(
                                Margins.small,
                              ),
                              fit: BoxFit.cover,
                            ),
                            const VerticalMargin.verySmall(),
                            SingleChatImageView(
                              message: images[3],
                              height: imageHeight,
                              jobId: jobId,
                              fit: BoxFit.cover,
                              borderRadius: BorderRadius.circular(
                                Margins.small,
                              ).copyWith(
                                bottomRight: Radius.circular(
                                  isOwnMessage
                                      ? Margins.small / 2
                                      : Margins.small,
                                ),
                              ),
                              decoration: _buildDecoration(
                                isOwnMessage: isOwnMessage,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDecoration({required bool isOwnMessage}) {
    if (images.length <= 4) return const SizedBox.shrink();
    return DecoratedBox(
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.5),
        borderRadius: BorderRadius.only(
          bottomRight: Radius.circular(
            isOwnMessage ? Margins.medium / 2 : Margins.medium,
          ),
        ),
      ),
      child: Center(
        child: Text(
          "+${images.length - 4}",
          style: const TextStyle(fontSize: 30, color: Colors.white),
        ),
      ),
    );
  }
}
