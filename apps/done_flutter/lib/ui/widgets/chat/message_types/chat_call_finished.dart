import 'package:done_auth/done_auth.dart';
import 'package:done_flutter/core/services/chat_service.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_flutter/ui/pages/call/call_feedback.dart';
import 'package:done_flutter/ui/widgets/chat/message_types/chat_call_status.dart';
import 'package:done_models/done_models.dart';
import 'package:flutter/material.dart';
import 'package:done_flutter/utils/extensions/string_extensions.dart';

/// Checks if the user left feedback after the call was finished
/// If no feedback was left, we return [CallFeedback]
/// otherwise [ChatCallFinished]
class ChatCallFinished extends StatelessWidget {
  const ChatCallFinished({required this.message, required this.job});

  final CallChatMessage message;
  final Job job;

  @override
  Widget build(BuildContext context) {
    final isCaller = context.authState.isCurrentUser(message.callingUser);

    final start = message.startTime!.toDate();
    final end = message.endTime!.toDate();
    final duration = end.difference(start);
    final durationText =
        '${getMessageTime(message.createTime, context)}, ${duration.inMinutes > 0 ? '${duration.inMinutes} min' : '${duration.inSeconds} s'}'
            .capitalize();

    if (_showLeaveFeedback(context)) {
      return CallFeedback(
        job: job,
        message: message,
        role: isCaller ? CallFeedbackRole.caller : CallFeedbackRole.receiver,
        durationText: durationText,
      );
    }

    return ChatCallStatus(
      callText: S.of(context).videoCallFinished,
      messageType: MessageType.call,
      callTime: durationText,
    );
  }

  bool _showLeaveFeedback(BuildContext context) {
    final isCaller = context.authState.isCurrentUser(message.callingUser);
    final isReceiver = context.authState.isCurrentUser(message.receivingUser);

    if (isCaller || isReceiver) {
      assert(
        !isCaller || !isReceiver,
        'You should not be both the caller and receiver of a call',
      );

      final feedbackLeft =
          (isCaller ? message.callerFeedback : message.receiverFeedback) !=
          null;
      final feedbackSkipped =
          (isCaller
              ? message.callerSkippedFeedback
              : message.receiverSkippedFeedback) ??
          false;

      final withinOneHour = message.endTime!.toDate().isAfter(
        DateTime.now().subtract(const Duration(hours: 1)),
      );

      if (!feedbackLeft && !feedbackSkipped && withinOneHour) {
        return true;
      }
    }

    return false;
  }
}
