import 'package:done_auth/done_auth.dart';
import 'package:done_flutter/core/services/chat_service.dart';
import 'package:done_database/done_database.dart';

import 'package:done_localizations/done_localizations.dart';
import 'package:done_flutter/ui/widgets/chat/message_types/chat_call_status.dart';
import 'package:done_models/done_models.dart';

import 'package:flutter/material.dart';
import 'package:done_flutter/utils/extensions/string_extensions.dart';
import 'package:get_it/get_it.dart';

/// Used to display canceled or missed calls
class CallMissedOrDeclined extends StatelessWidget {
  const CallMissedOrDeclined({super.key, required this.message});

  final CallChatMessage message;

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<User>(
      stream: _getOtherUserStream(context),
      builder: (context, snapshot) {
        if (!snapshot.hasData) return Container();

        final userFirstName = snapshot.data!.firstName;
        final callText = _getText(userFirstName, context);

        return ChatCallStatus(
          callText: callText,
          messageType: message.type,
          callTime: getMessageTime(message.createTime, context).capitalize(),
        );
      },
    );
  }

  Stream<User> _getOtherUserStream(BuildContext context) {
    final isReceivingUser = _isReceivingUser(context);

    final otherUserDocRef =
        isReceivingUser ? message.callingUser : message.receivingUser;

    return GetIt.instance<UserRepository>().user(otherUserDocRef.id);
  }

  String _getText(String? userFirstName, BuildContext context) {
    final isReceivingUser = _isReceivingUser(context);

    if (message.type == MessageType.declinedCall) {
      return isReceivingUser
          ? S.of(context).youRejectedOtherUser(userFirstName)
          : S.of(context).rejectedCallFrom(userFirstName);
    }

    return isReceivingUser
        ? S.of(context).missedCallFrom(userFirstName)
        : S.of(context).otherUserMissedACall(userFirstName);
  }

  bool _isReceivingUser(BuildContext context) =>
      context.authState.isCurrentUser(message.receivingUser);
}
