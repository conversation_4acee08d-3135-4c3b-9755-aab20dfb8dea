import 'package:done_analytics/done_analytics.dart';
import 'package:done_auth/done_auth.dart';
import 'package:done_flutter/core/services/chat_service.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_flutter/ui/widgets/chat/chat_ui_mixin.dart';
import 'package:done_flutter/ui/widgets/chat/metadata_preview.dart';
import 'package:done_models/done_models.dart';
import 'package:done_router/done_router.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_linkify/flutter_linkify.dart';

/// Text message for chat
class ChatTextMessage extends StatelessWidget with ChatUiMixin {
  const ChatTextMessage({
    super.key,
    required this.message,
    required this.isPreviousMessageCoalesced,
  });

  final TextChatMessage message;
  final bool isPreviousMessageCoalesced;

  @override
  Widget build(BuildContext context) {
    final senderRef = message.sender;
    final isOwnMessage = context.authState.isCurrentUser(senderRef);

    return Container(
      margin: buildMessageMargins(isOwnMessage, isPreviousMessageCoalesced),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: <Widget>[
          if (shouldShowAvatar(
            isOwnMessage: isOwnMessage,
            isPreviousMessageCoalesced: isPreviousMessageCoalesced,
          )) ...[
            Padding(
              padding: const EdgeInsets.only(bottom: 25),
              child: buildMessageAvatarWithSpacing(user: senderRef),
            ),
          ],
          Expanded(
            child: Column(
              crossAxisAlignment:
                  isOwnMessage
                      ? CrossAxisAlignment.end
                      : CrossAxisAlignment.start,
              children: <Widget>[
                if (kIsWeb)
                  _buildSelectableTextWidget(isOwnMessage, context, message)
                else
                  CupertinoContextMenu.builder(
                    actions: <Widget>[
                      CupertinoContextMenuAction(
                        child: Text(S.of(context).copy),
                        onPressed: () {
                          EventLogger.instance.logEvent('copiedMessage');
                          Clipboard.setData(ClipboardData(text: message.body));
                          Navigator.of(context).pop();
                        },
                      ),
                    ],
                    builder: (context, animation) {
                      // When context menu is open, we add some padding to the text container
                      final padding =
                          animation.value > 0.0
                              ? const EdgeInsets.symmetric(
                                horizontal: Margins.large,
                              )
                              : EdgeInsets.zero;
                      return Padding(
                        padding: padding,
                        child: _buildSelectableTextWidget(
                          isOwnMessage,
                          context,
                          message,
                        ),
                      );
                    },
                  ),
                if (message.metadata != null) const VerticalMargin.small(),
                if (message.metadata != null)
                  ...message.metadata!
                      .map<Widget>(
                        (m) => MetadataPreview(
                          metadata: m,
                          isOwnMessage: isOwnMessage,
                        ),
                      )
                      .separatedBy(() => const VerticalMargin.verySmall()),
                if (!isPreviousMessageCoalesced) ...[
                  const VerticalMargin.verySmall(),
                  Text(
                    getMessageTime(message.createTime, context),
                    style: Theme.of(context).textTheme.bodySmall!.apply(
                      color: context.doneColors.uiOnPrimary,
                    ),
                  ),
                  const VerticalMargin.small(),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSelectableTextWidget(
    bool isOwnMessage,
    BuildContext context,
    TextChatMessage message,
  ) {
    const isSelectable = kIsWeb;

    // TODO: Replace with text scaler once flutter_linkify have supportfor it
    // ignore: deprecated_member_use
    final currentTextScaleFactor = MediaQuery.of(context).textScaleFactor;

    return Container(
      decoration: BoxDecoration(
        color:
            isOwnMessage ? context.doneColors.purple : context.doneColors.uiBg2,
        borderRadius: BorderRadius.circular(Margins.medium).copyWith(
          bottomLeft: Radius.circular(
            isOwnMessage ? Margins.medium : Margins.medium / 2,
          ),
          bottomRight: Radius.circular(
            isOwnMessage ? Margins.medium / 2 : Margins.medium,
          ),
        ),
      ),
      margin: const EdgeInsets.only(top: 5),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child:
            isSelectable
                ? SelectableLinkify(
                  linkStyle: _chatTextStyle(
                    context,
                    isOwnMessage,
                  ).copyWith(decoration: TextDecoration.underline),
                  text: message.body,
                  textScaleFactor: currentTextScaleFactor,
                  style: _chatTextStyle(context, isOwnMessage),
                  onOpen: (link) {
                    URLRouter.instance.handleUrl(link.url, context: context);
                  },
                )
                : Material(
                  color:
                      isOwnMessage
                          ? context.doneColors.purple
                          : context.doneColors.uiBg2,
                  child: Linkify(
                    linkStyle: _chatTextStyle(
                      context,
                      isOwnMessage,
                    ).copyWith(decoration: TextDecoration.underline),
                    text: message.body,
                    textScaleFactor: currentTextScaleFactor,
                    style: _chatTextStyle(context, isOwnMessage),
                    onOpen: (link) {
                      URLRouter.instance.handleUrl(link.url, context: context);
                    },
                  ),
                ),
      ),
    );
  }

  TextStyle _chatTextStyle(BuildContext context, bool isOwnMessage) {
    return Theme.of(context).textTheme.bodyLarge!.copyWith(
      fontSize: 16,
      color:
          isOwnMessage
              ? context.doneColors.uiAlwaysPureWhite
              : context.doneColors.uiOnPrimary,
    );
  }
}
