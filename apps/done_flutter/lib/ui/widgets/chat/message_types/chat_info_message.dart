import 'package:done_flutter/core/services/chat_service.dart';
import 'package:done_flutter/ui/widgets/chat/chat_ui_mixin.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

/// Info message for chat
class ChatInfoMessage extends StatelessWidget with ChatUiMixin {
  const ChatInfoMessage({
    super.key,
    required this.message,
    required this.isPreviousMessageCoalesced,
  });

  final InfoChatMessage message;
  final bool isPreviousMessageCoalesced;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: buildMessageMargins(false, false),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          const VerticalMargin.small(),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.info, color: context.doneColors.typographyLowContrast),
              const HorizontalMargin.small(),
              Flexible(child: Text(message.body, textAlign: TextAlign.left)),
            ],
          ),
          if (!isPreviousMessageCoalesced) ...[
            const VerticalMargin.verySmall(),
            Text(
              getMessageTime(message.createTime, context),
              textAlign: TextAlign.end,
              style: const TextStyle(fontSize: 11),
            ),
          ],
          const VerticalMargin.small(),
        ],
      ),
    );
  }
}
