import 'package:done_auth/done_auth.dart';
import 'package:done_database/done_database.dart';
import 'package:done_image/done_image.dart';

import 'package:done_flutter/ui/widgets/chat/chat_ui_mixin.dart';
import 'package:done_flutter/ui/widgets/chat/message_info_row.dart';
import 'package:done_models/done_models.dart';
import 'package:done_router/done_router.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';

/// Image message for chat.
/// Has ability to download or to navigate to "photo" - separate page for image only.
class ChatImageMessage extends StatelessWidget with ChatUiMixin {
  const ChatImageMessage(this.message, {super.key, required this.jobId});

  final ImageChatMessage message;
  final String jobId;

  @override
  Widget build(BuildContext context) {
    final senderRef = message.sender;
    final isOwnMessage = context.authState.isCurrentUser(senderRef);
    return StreamBuilder<User>(
      stream: GetIt.instance<UserRepository>().user(senderRef.id),
      builder: (context, senderSnapshot) {
        return Container(
          margin: buildMessageMargins(isOwnMessage, false),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              MessageInfoRow(message: message, sender: senderSnapshot.data),
              Row(
                mainAxisAlignment:
                    isOwnMessage
                        ? MainAxisAlignment.end
                        : MainAxisAlignment.start,
                children: <Widget>[
                  HorizontalMargin(margin: isOwnMessage ? 0 : 55),
                  _buildImageContainer(isOwnMessage),
                ],
              ),
              const VerticalMargin.medium(),
            ],
          ),
        );
      },
    );
  }

  Widget _buildImageContainer(bool isOwnMessage) {
    return Container(
      margin: const EdgeInsets.only(top: 5, bottom: 5),
      child: Builder(
        builder: (context) {
          const width = 200.0;
          final height =
              message.aspectRatio != null
                  ? width / message.aspectRatio!
                  : width;
          if (message.hasThumbnail == false) {
            return SizedBox(
              width: width,
              height: height,
              child: const _ChatImageLoading(),
            );
          }
          return SingleChatImageView(
            message: message,
            jobId: jobId,
            borderRadius: BorderRadius.circular(Margins.medium),
            width: width,
            height: height,
          );
        },
      ),
    );
  }
}

class SingleChatImageView extends StatelessWidget {
  const SingleChatImageView({
    super.key,
    required this.message,
    required this.jobId,
    required this.borderRadius,
    this.fit = BoxFit.fitWidth,
    this.height = 105,
    this.width = 105,
    this.decoration,
  });

  final ImageChatMessage message;
  final String jobId;
  final BoxFit fit;
  final BorderRadiusGeometry borderRadius;
  final double width;
  final double height;

  /// A decorating widget that is displayed on top of the image.
  final Widget? decoration;

  @override
  Widget build(BuildContext context) {
    if (message.imageRef == null) return const _ChatImageLoading();

    final image = DoneCachedFirebaseImage(
      blurHash: message.thumbBlurHash,
      reference: message.thumbRef!,
      fit: fit,
    );
    return ClipRRect(
      borderRadius: borderRadius,
      child: SizedBox(
        width: width,
        height: height,
        child: GestureDetector(
          onTap: () async {
            if (message.imageRef == null) return;
            // FIXME: Currently uses absolute path instead of relative
            // because on web desktop, the path is '/' when in the
            // side by side chat view.
            await GoRouter.of(context).push(
              '/projects/$jobId/chat/photo?initialPath=${message.imageRef}',
            );
          },
          child:
              decoration == null
                  ? image
                  : Stack(
                    children: [Positioned.fill(child: image), decoration!],
                  ),
        ),
      ),
    );
  }
}

class _ChatImageLoading extends StatelessWidget {
  const _ChatImageLoading();

  @override
  Widget build(BuildContext context) {
    return Center(
      child: DoneAdaptiveLoadingIndicator(
        brightness:
            Theme.of(context).brightness == Brightness.dark
                ? Brightness.light
                : Brightness.dark,
      ),
    );
  }
}
