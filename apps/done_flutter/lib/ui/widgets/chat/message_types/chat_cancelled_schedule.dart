import 'package:done_auth/done_auth.dart';
import 'package:done_flutter/core/services/chat_service.dart';
import 'package:done_database/done_database.dart';

import 'package:done_flutter/ui/widgets/chat/message_types/chat_call_status.dart';
import 'package:done_models/done_models.dart';
import 'package:flutter/material.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_flutter/utils/extensions/string_extensions.dart';
import 'package:get_it/get_it.dart';

class ChatCancelledSchedule extends StatelessWidget {
  const ChatCancelledSchedule({super.key, required this.message});

  final ChatMessage message;

  @override
  Widget build(BuildContext context) {
    final senderUserRef = message.sender;

    final isCurrentUserSender = context.authState.isCurrentUser(senderUserRef);

    if (isCurrentUserSender) {
      return ChatCallStatus(
        callText: _getText(context, name: S.of(context).you),
        messageType: MessageType.cancelledBookedCall,
        callTime: getMessageTime(message.createTime, context).capitalize(),
      );
    }

    //* If currentUser is not sender, we need StreamBuilder
    return StreamBuilder<User>(
      stream: GetIt.instance<UserRepository>().user(senderUserRef.id),
      builder: (context, userSnapshot) {
        if (!userSnapshot.hasData) return const SizedBox();

        return ChatCallStatus(
          callText: _getText(context, name: userSnapshot.data!.firstName),
          messageType: MessageType.cancelledBookedCall,
          callTime: getMessageTime(message.createTime, context).capitalize(),
        );
      },
    );
  }

  String _getText(BuildContext context, {String? name}) {
    if (message.type == MessageType.cancelledScheduledWorkTime) {
      return S.of(context).cancelScheduledWorkMessage(name);
    } else {
      return S.of(context).cancelScheduledCallMessage(name);
    }
  }
}
