import 'package:done_auth/done_auth.dart';
import 'package:done_flutter/core/router/routes/project_routes.dart';
import 'package:done_flutter/core/services/chat_service.dart';
import 'package:done_flutter/utils/pdf_util.dart';
import 'package:done_image/done_image.dart';
import 'package:done_models/done_models.dart';
import 'package:done_router/done_router.dart';
import 'package:done_ui/done_ui.dart';
import 'package:done_flutter/ui/widgets/chat/chat_ui_mixin.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:logger/logger.dart';

/// UI for file message in chat, has ability to view/download file
class ChatFileMessage extends StatelessWidget with ChatUiMixin {
  const ChatFileMessage({
    super.key,
    required this.message,
    required this.jobId,
    required this.isPreviousMessageCoalesced,
  });

  final FileChatMessage message;
  final String jobId;
  final bool isPreviousMessageCoalesced;

  @override
  Widget build(BuildContext context) {
    final senderRef = message.sender;
    final isOwnMessage = context.authState.isCurrentUser(senderRef);

    return Container(
      margin: const EdgeInsets.only(left: 12, right: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          if (shouldShowAvatar(
            isOwnMessage: isOwnMessage,
            isPreviousMessageCoalesced: isPreviousMessageCoalesced,
          ))
            buildMessageAvatarWithSpacing(user: senderRef),
          Expanded(
            child: Column(
              crossAxisAlignment:
                  isOwnMessage
                      ? CrossAxisAlignment.end
                      : CrossAxisAlignment.start,
              children: <Widget>[
                Container(
                  decoration: chatBoxDecoration(context),
                  margin: const EdgeInsets.only(top: 5),
                  child: GestureDetector(
                    onTap: () async {
                      final fileRef = message.fileRef;
                      if (fileRef == null) {
                        return GetIt.instance<Logger>().w(
                          'fileRef on message @ ${message.documentReference?.path} is null',
                        );
                      }

                      if (fileRef.contains("pdf")) {
                        await viewAsPdf(
                          context: context,
                          storagePath: fileRef,
                          pushRoute: (url) {
                            final params = ViewChatPdfRouteParams(
                              projectId: jobId,
                              url: url,
                            );
                            ViewChatPdfRoute(params: params).navigate(context);
                          },
                        );
                      } else {
                        final url = await getDownloadUrl(fileRef);
                        await URLRouter.instance.handleUrl(
                          url,
                          context: context,
                        );
                      }
                    },
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: 16,
                      ),
                      child: Row(
                        children: <Widget>[
                          Image.asset(
                            ImageAssets.attachment,
                            height: 18,
                            color: context.doneColors.typographyLowContrast,
                          ),
                          const HorizontalMargin(margin: 18),
                          Expanded(
                            child: Text(
                              message.fileName ?? "Fil",
                              style: Theme.of(context).textTheme.labelMedium,
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                              softWrap: false,
                            ),
                          ),
                          Icon(
                            Icons.arrow_forward_ios,
                            size: 18,
                            color: context.doneColors.uiBg1,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                if (!isPreviousMessageCoalesced)
                  Text(
                    getMessageTime(message.createTime, context),
                    style: const TextStyle(fontSize: 11),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
