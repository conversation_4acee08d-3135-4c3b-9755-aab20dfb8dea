import 'package:done_auth/done_auth.dart';
import 'package:done_flutter/ui/pages/company/offers/company_offers_view.dart';
import 'package:done_flutter/ui/widgets/notification_dot.dart';
import 'package:done_flutter/utils/extensions/models/job_offer.dart';
import 'package:done_flutter/utils/extensions/models/job_offer_reply.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:done_flutter/ui/widgets/jobs/job_offer_reply_chip.dart';
import 'package:flutter/material.dart';
import 'package:done_localizations/done_localizations.dart';

class JobOfferRow extends StatelessWidget {
  const JobOfferRow({
    super.key,
    required this.jobOffer,
    required this.index,
    required this.jobOfferType,
    required this.onTap,
    this.isSelected = false,
  });

  final JobOffer jobOffer;
  final int index;
  final OfferPageType jobOfferType;
  final void Function(String, int) onTap;
  final bool isSelected;

  @override
  Widget build(BuildContext context) {
    final reply = jobOffer.replies[context.authState.user!.company!.id];
    return InkWell(
      child: Ink(
        padding: const EdgeInsets.symmetric(vertical: Margins.small),
        color: _color(context),
        child: Row(
          children: [
            const HorizontalMargin.small(),
            NotificationDot(
              isRead:
                  !((jobOffer.status == JobOfferStatus.pending) &&
                      reply?.answer == null),
            ),
            const HorizontalMargin.small(),
            Expanded(child: _buildRowContent(context, reply)),
            const HorizontalMargin.large(),
            Icon(
              Icons.keyboard_arrow_right,
              color: context.doneColors.uiChevron,
            ),
            const HorizontalMargin.large(),
          ],
        ),
      ),
      onTap: () => onTap(jobOffer.id, index),
    );
  }

  Color _color(BuildContext context) =>
      isSelected
          ? context.doneColors.uiPurpleSelection
          : context.doneColors.uiPrimary;

  Widget _buildRowContent(BuildContext context, JobOfferReply? reply) {
    final chips = [
      if (jobOffer.fixedPriceJobs?.isNotEmpty ?? false)
        const _FixedPriceJobChip(),
      if (reply?.answer != null)
        JobbOfferReplyChip(jobOfferReply: reply!.answer),
      if (reply?.answer == null && jobOffer.status == JobOfferStatus.resolved)
        const _AlreadyMatchedBadge(),
    ];

    return Opacity(
      opacity: (jobOffer.status == JobOfferStatus.resolved) ? 0.3 : 1.0,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            jobOffer.servicesAndLocationString(context),
            style: Theme.of(context).textTheme.labelMedium,
          ),
          Text(
            jobOffer.description!,
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          ),
          if (reply?.answer.replyLabel(context) != null)
            DoneKeyValuePair(
              label: reply!.answer.replyLabel(context)!,
              value: reply.answer.replyValue(context, reply)!,
            ),
          if (jobOffer.preferredCallTime != null)
            DoneKeyValuePair(
              label: S.of(context).call,
              value: jobOffer.preferredCallTime!,
            ),
          if (jobOffer.preferredStartDate != null)
            DoneKeyValuePair(
              label: S.of(context).projectStart,
              value: jobOffer.preferredStartDate!,
            ),
          // This check is needed for avoiding unnecessary space
          if (chips.isNotEmpty)
            Row(
              children: chips.separatedBy(() => const HorizontalMargin.small()),
            ),
        ].separatedBy(() => const VerticalMargin.small()),
      ),
    );
  }
}

class _AlreadyMatchedBadge extends StatelessWidget {
  const _AlreadyMatchedBadge();

  @override
  Widget build(BuildContext context) {
    return Chip(
      backgroundColor: context.doneColors.uiNegative,
      label: Text(
        S.of(context).alreadyMatched,
        style: Theme.of(
          context,
        ).textTheme.labelMedium!.copyWith(color: context.doneColors.uiPrimary),
      ),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      padding: EdgeInsets.zero,
    );
  }
}

class _FixedPriceJobChip extends StatelessWidget {
  const _FixedPriceJobChip();

  @override
  Widget build(BuildContext context) {
    return Chip(
      backgroundColor: context.doneColors.uiBg2,
      label: Text(
        S.of(context).fixedPrice,
        style: Theme.of(context).textTheme.labelMedium,
      ),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      padding: EdgeInsets.zero,
    );
  }
}
