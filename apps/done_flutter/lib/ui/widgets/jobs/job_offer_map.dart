import 'package:done_maps/done_maps.dart';
import 'package:done_models/done_models.dart';
import 'package:flutter/material.dart';

class JobOfferMap extends StatelessWidget {
  const JobOfferMap({super.key, required this.location, required this.height});

  final DoneLocation location;
  final double height;

  @override
  Widget build(BuildContext context) {
    return SizedBox(height: height, child: StaticMap(location: location));
  }
}
