import 'package:done_flutter/ui/widgets/user_avatar.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

import 'package:cloud_firestore/cloud_firestore.dart';

import 'package:done_flutter/ui/widgets/jobs/job_status_string.dart';
import 'package:done_localizations/done_localizations.dart';

class JobRow extends StatelessWidget {
  const JobRow({
    super.key,
    required this.job,
    this.onTap,
    this.selected = false,
  });

  final Job job;
  final VoidCallback? onTap;
  final bool selected;

  String _jobCreatedTimeString(Timestamp? timestamp, BuildContext context) {
    if (timestamp == null) return '';
    return TimeFormatter.toDayMonthHourMinuteFormat(timestamp.toDate());
  }

  @override
  Widget build(BuildContext context) {
    return ListTile(
      selected: selected,
      selectedTileColor: context.doneColors.uiBg2WithOpacity,
      minVerticalPadding: 12,
      title: Padding(
        padding: const EdgeInsets.only(bottom: 8),
        child: Row(
          children: <Widget>[
            UserAvatar(job.customer, radius: 12),
            const HorizontalMargin.small(),
            Text(
              job.customerName ?? S.of(context).anonymous,
              style: Theme.of(context).textTheme.labelMedium,
            ),
            Expanded(
              child: Text(
                _jobCreatedTimeString(job.createTime, context),
                textAlign: TextAlign.end,
                style: Theme.of(context).textTheme.bodyMedium!.apply(
                  color: context.doneColors.typographyLowContrast,
                ),
              ),
            ),
          ],
          textBaseline: TextBaseline.alphabetic,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
        ),
      ),
      subtitle: Padding(
        padding: const EdgeInsets.only(left: 34),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              jobStatusString(job, context),
              style: Theme.of(context).textTheme.labelMedium,
            ),
            const VerticalMargin.small(),
            Text(
              job.description!,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              style: Theme.of(context).textTheme.bodyMedium!.apply(
                color: context.doneColors.typographyLowContrast,
              ),
            ),
          ],
        ),
      ),
      onTap: onTap,
    );
  }
}
