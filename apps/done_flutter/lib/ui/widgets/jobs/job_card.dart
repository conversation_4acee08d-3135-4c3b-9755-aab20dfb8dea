import 'package:done_flutter/core/blocs/master_detail_cubit/home_selection_cubit.dart';
import 'package:done_flutter/core/models/navigation_bar.dart';
import 'package:done_flutter/ui/widgets/chat/unread_messaes_badge.dart';

import 'package:done_flutter/utils/extensions/models/job.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:done_flutter/ui/widgets/jobs/job_status_string.dart';
import 'package:done_flutter/utils/curve_painter.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class JobCard extends StatelessWidget {
  const JobCard({super.key, required this.job, required this.onTap});

  final Job job;
  final ValueChanged<String> onTap;
  Widget _illustration(Job job) {
    final illustrationName = job.illustrationFileName;
    if (illustrationName == null) return const SizedBox();
    return Image.asset(illustrationName, height: 72);
  }

  @override
  Widget build(BuildContext context) {
    final tabIndex = CustomerHomeTabs.projects.index;
    const shape = RoundedRectangleBorder(
      borderRadius: BorderRadius.all(Radius.circular(Margins.medium)),
    );
    final selected =
        context.watch<HomeSelectionCubit>().state[tabIndex] == job.id;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: Margins.medium),
      child: Card(
        shape: shape,
        color:
            selected
                ? context.doneColors.uiPurpleDarkSelection
                : context.doneColors.navyBlue,
        child: InkWell(
          customBorder: shape,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: Stack(
              children: [
                Positioned.fill(
                  child: CustomPaint(
                    painter: CurvePainter(context.doneColors.lightNavyBlue),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        job.description!,
                        style: Theme.of(context).textTheme.titleMedium!.apply(
                          color: context.doneColors.uiAlwaysPureWhite,
                        ),
                        maxLines: 2,
                      ),
                      const VerticalMargin(margin: 6),
                      Text(
                        job.companyName ?? '',
                        style: Theme.of(context).textTheme.bodyMedium!.apply(
                          color: context.doneColors.uiAlwaysPureWhite
                              .withValues(alpha: 0.7),
                        ),
                      ),
                      const VerticalMargin.medium(),
                      Text(
                        jobStatusString(job, context),
                        style: Theme.of(context).textTheme.bodyMedium!.apply(
                          color: context.doneColors.uiAlwaysPureWhite,
                        ),
                      ),
                      const VerticalMargin.medium(),
                      UnreadMessagesBadge(job: job),
                    ],
                  ),
                ),
                Positioned(
                  bottom: 0,
                  right: 0,
                  child: ClipRRect(
                    borderRadius: const BorderRadius.only(
                      bottomRight: Radius.circular(12),
                    ),
                    child: _illustration(job),
                  ),
                ),
              ],
            ),
          ),
          onTap: () => onTap(job.id),
        ),
      ),
    );
  }
}
