import 'package:done_flutter/ui/widgets/progress/progress_project_start.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:done_localizations/done_localizations.dart';

String jobStatusString(Job job, BuildContext context) {
  // We need cloud function for that for proper job status!!
  if (job.company == null) return S.of(context).jobsStatusWaiting;

  if (job.events.companyReviewed != null) {
    return S.of(context).jobReviewed;
  }

  if (job.isMarkedAsDone) {
    return S.of(context).jobsStatusJobDone;
  }

  if (job.scheduledWorkStartTime != null) {
    return '${S.of(context).jobsStatusWorkScheduled} ${job.projectStartState.subtitle(context, job)}';
  }

  if (job.isOfferAccepted) {
    return S.of(context).jobsStatusOfferAccepted;
  }

  if (job.isOfferDeclined) {
    return S.of(context).jobsStatusOfferDeclined;
  }

  if (job.isOfferSent) {
    return S.of(context).jobsStatusOfferSent;
  }

  if (job.merchant.id != donePartnerId) {
    return S.of(context).notScheduledProjectsSection;
  }

  if (!job.shouldScheduleCall) {
    return S.of(context).jobsStatusOfferSentNull;
  }

  if (job.scheduledCallTime != null) {
    return S
        .of(context)
        .jobsStatusScheduledCallTimeNotNull(
          TimeFormatter.toDayMonthHourMinuteFormat(
            job.scheduledCallTime!.toDate(),
          ),
        );
  }

  return S.of(context).jobsStatusScheduledCallTimeNull;
}

Color jobStatusColor(BuildContext context, Job job) {
  // We need cloud function for that for proper job status!!

  if (job.events.companyReviewed != null) {
    return context.doneColors.yellow.withValues(alpha: 0.3);
  }

  if (job.isMarkedAsDone) {
    return context.doneColors.uiPositiveBg10;
  }

  if (job.isOfferAccepted) {
    return context.doneColors.uiPositiveBg10;
  }

  if (job.isOfferDeclined) {
    return context.doneColors.uiNegativeBg10;
  }

  return context.doneColors.uiBg2WithOpacity;
}
