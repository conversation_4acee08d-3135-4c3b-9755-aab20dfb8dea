import 'package:done_flutter/core/blocs/master_detail_cubit/home_selection_cubit.dart';
import 'package:done_flutter/core/models/navigation_bar.dart';
import 'package:done_flutter/utils/extensions/models/job.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class PreviousProjectCard extends StatelessWidget {
  const PreviousProjectCard({
    super.key,
    required this.job,
    required this.onTap,
  });

  final Job job;
  final ValueChanged<String> onTap;
  @override
  Widget build(BuildContext context) {
    final tabIndex = CustomerHomeTabs.projects.index;
    final selected =
        context.watch<HomeSelectionCubit>().state[tabIndex] == job.id;

    return InkWell(
      onTap: () => onTap(job.id),
      child: ColoredBox(
        color:
            selected ? context.doneColors.uiBg2WithOpacity : Colors.transparent,
        child: Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: Margins.large,
          ).copyWith(top: Margins.small),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              CircleAvatar(
                radius: 22,
                backgroundColor: context.doneColors.uiPrimary,
                foregroundImage:
                    job.illustrationFileName != null
                        ? AssetImage(job.illustrationFileName!)
                        : null,
              ),
              Flexible(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: <Widget>[
                    if (job.companyName != null)
                      Text(
                        job.companyName ?? '',
                        style: Theme.of(context).textTheme.labelMedium,
                      ),
                    Text(job.description ?? '', maxLines: 4),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        Text(
                          closeText(context),
                          style: Theme.of(context).textTheme.bodySmall!.apply(
                            color: context.doneColors.typographyMediumContrast,
                          ),
                        ),
                        const Divider(),
                      ],
                    ),
                  ].separatedBy(() => const VerticalMargin.verySmall()),
                ),
              ),
              Icon(
                Icons.chevron_right,
                color: context.doneColors.typographyMediumContrast,
              ),
            ].separatedBy(() => const HorizontalMargin.small()),
          ),
        ),
      ),
    );
  }

  String closeText(BuildContext context) {
    final prefix =
        job.isMarkedAsDone ? S.of(context).completed : S.of(context).aborted;
    final timestamp =
        job.isMarkedAsDone
            ? job.events.jobDone
            : job.events.jobCancelled ??
                job.events.companyClosedProject ??
                job.events.customerClosedProject;
    final date = TimeFormatter.getShortHumanReadableDate(
      timestamp?.toDate(),
      context,
      includeYear: true,
    );
    return '$prefix $date';
  }
}
