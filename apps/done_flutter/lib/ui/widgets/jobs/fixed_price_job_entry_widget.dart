import 'package:done_booking/done_booking.dart';
import 'package:done_flutter/ui/widgets/jobs/job_product_list.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

class FixedPriceJobEntryWidget extends StatelessWidget {
  const FixedPriceJobEntryWidget({
    super.key,
    required this.entry,
    this.includeStartCost = true,
  });

  final FixedPriceJobEntry entry;
  final bool includeStartCost;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        FixedPriceCard(
          fixedPriceJobId: entry.id,
          count: entry.quantity,
          includeStartCost: includeStartCost,
        ),
        if (entry.products != null) ...[
          const VerticalMargin.medium(),
          JobProductList(products: entry.products!),
          const Divider(),
        ],
      ],
    );
  }
}
