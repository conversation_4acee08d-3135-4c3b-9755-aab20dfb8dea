import 'package:done_auth/done_auth.dart';
import 'package:done_flutter/ui/pages/company/shared_generator/generator_type.dart';
import 'package:done_flutter/utils/extensions/models/job.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_flutter/ui/widgets/buttons/add_extras_button.dart';
import 'package:done_flutter/utils/ask_reason.dart';
import 'package:done_flutter/utils/extensions/enums/job_close_reason.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

void showCloseJobDialog({
  required BuildContext context,
  required bool isJobDone,
  required void Function(JobCloseReason closeReason, {String? otherText})
  onClose,
}) {
  _askCloseReason(context: context, onClose: onClose, isJobDone: isJobDone);
}

/// Shows a dialog with information regarding the cancellation
/// of pending quotes when proceeding if pending quotes found
///
/// Returns a boolean representing whether the user agreed to cancellation or not,
/// if no pending quotes are found it returns true
Future<bool> showPendingQuoteCancellationDialog({
  required BuildContext context,
  required Job job,
}) async {
  final hasPendingQuotes = await job.hasPendingQuotes(context);
  if (!hasPendingQuotes) return true;
  final shouldContinue = await showConfirmationPopup(
    context: context,
    actionTitle: Text(S.of(context).genericContinue),
    cancelTitle: Text(S.of(context).cancel),
    title: Text(S.of(context).jobHasPendingQuotesTitle),
    message: Text(S.of(context).jobHasPendingQuotesMessage),
  );

  return shouldContinue;
}

Future<bool> showMarkAsCompletedDialog({
  required BuildContext context,
  required Job job,
}) async {
  final jobHasNoAcceptedOffers = job.events.offerAccepted == null;
  final jobHasPendingQuotes = await job.hasPendingQuotes(context);
  final shouldMarkAsCompleted = await showDialog<bool>(
    context: context,
    builder:
        (BuildContext nestedContext) => DoneDialog(
          content: [
            if (jobHasNoAcceptedOffers) ...[
              Text(
                S.of(context).completeProjectDialogNoQuoteWarning,
                style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                  fontWeight: FontWeight.bold,
                  color: context.doneColors.uiNegative,
                ),
              ),
              const VerticalMargin.medium(),
            ],
            if (jobHasPendingQuotes) ...[
              Text(
                S.of(context).jobHasPendingQuotesMessage,
                style: TextStyle(
                  color: context.doneColors.uiNegative,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const VerticalMargin.medium(),
            ],
            Text(
              job.isInvoicedThroughPartnership
                  ? S.of(context).installationDonePrompt
                  : S.of(context).completeProjectDialogDescription,
            ),
            const VerticalMargin.medium(),
            DoneButton(
              style: DoneButtonStyle.secondary,
              title: Text(
                S.of(context).markAsCompleted,
                style: const TextStyle(fontWeight: FontWeight.w600),
              ),
              onPressed: () {
                Navigator.of(nestedContext).pop(true);
              },
            ),
            if (job.isInvoicedThroughPartnership) ...[
              const VerticalMargin.verySmall(),
              AddExtrasButton(
                originalContext: context,
                generatorType: GeneratorType.quote,
                job: job,
                title: Text(S.of(context).fillInAdditions),
                style: DoneButtonStyle.neutral,
              ),
            ],
            const VerticalMargin.verySmall(),
            DoneButton(
              style: DoneButtonStyle.negative,
              title: Text(S.of(context).cancel),
              onPressed: () => Navigator.pop(nestedContext),
            ),
          ],
        ),
  );

  return shouldMarkAsCompleted ?? false;
}

Future<void> _askCloseReason({
  required BuildContext context,
  bool isJobDone = false,
  required void Function(JobCloseReason closeReason, {String? otherText})
  onClose,
}) async {
  final isCustomerUser = context.authState.isUserCustomer();

  final result = await askReason<JobCloseReason>(
    context: context,
    reasons: getJobCloseReasonChoices(isCustomerUser, isJobDone: isJobDone),
    title: Text(
      S.of(context).jobWhyCloseProject,
      style: Theme.of(
        context,
      ).textTheme.bodyMedium!.apply(color: context.doneColors.uiOnPrimary),
    ),
    reasonTitleBuilder: (context, reason) => Text(reason.title(context)),
    actionTitle: S.of(context).closeProject,
    otherReasons: [
      JobCloseReason.companyCannotDoJob,
      JobCloseReason.customerOther,
      JobCloseReason.companyOther,
    ],
  );
  if (result == null) return;
  onClose(result.reason, otherText: result.otherText);
}
