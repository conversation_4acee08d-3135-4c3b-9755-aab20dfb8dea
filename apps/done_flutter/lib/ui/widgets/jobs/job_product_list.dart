import 'package:done_image/done_image.dart';
import 'package:done_models/done_models.dart';
import 'package:done_router/done_router.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

/// Displays a column of list tiles with products and opens
/// the product URLs on tap.
class JobProductList extends StatelessWidget {
  const JobProductList({super.key, required this.products});

  final List<JobProduct> products;

  @override
  Widget build(BuildContext context) {
    return Column(
      children:
          products
              .map<Widget>((p) => _JobProduct(product: p))
              .separatedBy(() => const VerticalMargin.medium())
              .toList(),
    );
  }
}

class _JobProduct extends StatelessWidget {
  const _JobProduct({required this.product});

  final JobProduct product;

  @override
  Widget build(BuildContext context) {
    return ListTile(
      minVerticalPadding: 8,
      leading: CachedNetworkImage(imageUrl: product.imageURL, width: 40),
      title: Text(product.title, style: Theme.of(context).textTheme.bodyLarge),
      onTap: () {
        URLRouter.instance.handleUrl(product.webURL, context: context);
      },
      trailing: Icon(Icons.chevron_right, color: context.doneColors.uiChevron),
    );
  }
}
