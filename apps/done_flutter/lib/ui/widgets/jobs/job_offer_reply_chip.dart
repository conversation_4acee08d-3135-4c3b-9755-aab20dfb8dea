import 'package:done_flutter/utils/extensions/models/job_offer_reply.dart';
import 'package:done_models/done_models.dart';
import 'package:flutter/material.dart';

class JobbOfferReplyChip extends StatelessWidget {
  const JobbOfferReplyChip({super.key, required this.jobOfferReply});

  final JobOfferReplyAnswer jobOfferReply;

  @override
  Widget build(BuildContext context) {
    return Chip(
      backgroundColor: jobOfferReply.bgColor(context),
      label: Text(
        jobOfferReply.title(context),
        style: Theme.of(
          context,
        ).textTheme.labelMedium!.apply(color: Colors.white),
      ),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      padding: EdgeInsets.zero,
    );
  }
}
