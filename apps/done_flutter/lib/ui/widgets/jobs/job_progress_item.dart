import 'package:done_image/done_image.dart';
import 'package:done_ui/done_ui.dart';
import 'package:done_flutter/ui/widgets/bordered_circle_avatar.dart';
import 'package:flutter/material.dart';

class JobProgressItem extends StatelessWidget {
  const JobProgressItem({
    required this.image,
    required this.state,
    required this.children,
  });

  final Widget image;
  final JobProgressState state;
  final List<Widget> children;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: LayoutMargins.of(context)?.margins.left ?? 0,
        vertical: 8,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              BorderedCircleAvatar(
                radius: 24,
                borderColor: state.iconBorderColor(context),
                image:
                    (state == JobProgressState.done)
                        ? Image.asset(
                          ImageAssets.check,
                          scale: 3,
                          color: context.doneColors.uiAlwaysPureWhite,
                        )
                        : image,
                backgroundColor: state.iconBackgroundColor(context),
              ),
            ],
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.only(left: 16),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: children,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

enum JobProgressState { uninitialized, ongoing, done, cancelled }

extension ButtonTypeUIValues on JobProgressState {
  Color iconBackgroundColor(BuildContext context) {
    switch (this) {
      case JobProgressState.uninitialized:
        return context.doneColors.uiPrimary;
      case JobProgressState.ongoing:
        return context.doneColors.uiBg2;
      case JobProgressState.done:
        return context.doneColors.uiPositive;
      case JobProgressState.cancelled:
        return context.doneColors.uiNegative;
    }
  }

  Color iconBorderColor(BuildContext context) {
    switch (this) {
      case JobProgressState.uninitialized:
        return context.doneColors.uiBg2;
      case JobProgressState.ongoing:
        return context.doneColors.uiBg2;
      case JobProgressState.done:
        return context.doneColors.uiPositive;
      case JobProgressState.cancelled:
        return context.doneColors.uiNegative;
    }
  }
}
