import 'package:done_flutter/ui/widgets/expandable_text.dart';
import 'package:done_flutter/ui/widgets/reviews/rating_display.dart';
import 'package:done_flutter/utils/formatted_user_name.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

class CompanyReviewCard extends StatelessWidget {
  const CompanyReviewCard({required this.review});

  final CompanyReview review;

  @override
  Widget build(BuildContext context) {
    final sixMonthsAgo = DateTime.now().subtract(const Duration(days: 182));
    final shouldShowDate = review.createTime.toDate().isAfter(sixMonthsAgo);
    return Padding(
      padding: const EdgeInsets.only(right: 20),
      child: Container(
        decoration: BoxDecoration(
          color: context.doneColors.uiBg2WithOpacity,
          border: Border.all(color: context.doneColors.uiBg2WithOpacity),
          borderRadius: BorderRadius.circular(16),
        ),
        padding: const EdgeInsets.all(Margins.large),
        child: Container(
          constraints: const BoxConstraints(minHeight: 132),
          width: 260,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  RatingDisplay(
                    rating: review.rating?.toDouble() ?? 0,
                    size: 3,
                    color: context.doneColors.purple,
                  ),
                  const Spacer(),
                  if (shouldShowDate)
                    Expanded(
                      child: Text(
                        TimeFormatter.getShortHumanReadableDate(
                          review.createTime.toDate(),
                          context,
                          asShortAsPossible: true,
                        ),
                        textAlign: TextAlign.end,
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ),
                ],
              ),
              Text(
                fullNameToHiddenName(review.author.name),
                style: Theme.of(context).textTheme.labelMedium,
              ),
              if (!review.publicFeedback.isNullOrEmpty())
                DoneExpandableText(review.publicFeedback!, maxLines: 4),
            ].separatedBy(() => const VerticalMargin.small()),
          ),
        ),
      ),
    );
  }
}
