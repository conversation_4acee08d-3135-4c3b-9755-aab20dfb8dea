import 'package:done_auth/done_auth.dart';
import 'package:done_deductions/done_deductions.dart';

import 'package:done_database/done_database.dart';
import 'package:done_flutter/core/router/routes/project_routes.dart';

import 'package:done_localizations/done_localizations.dart';
import 'package:done_flutter/utils/extensions/enums/quote.dart';
import 'package:done_flutter/utils/pdf_util.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:done_flutter/utils/dialogs/quote_dialogs.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';

/// Shows informationg regarding a speciifc [quote]
///
/// It will open the [Quote]'s details page if [onTap] is not specified
class QuoteCard extends StatelessWidget {
  const QuoteCard({
    required this.quote,
    required this.jobId,
    this.isChatItem = false,
    this.onTap,
  });

  final Quote quote;
  final String jobId;
  final bool isChatItem;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    if (quote.companyName.isNullOrEmpty()) {
      //* this happens inside ChatPage when quote is actually just a chat message and contains no company data
      //* therefor we have to fetch it
      StreamBuilder<Company>(
        stream: GetIt.instance<CompanyRepository>().company(quote.company!.id),
        builder: (BuildContext context, AsyncSnapshot<Company> snapshot) {
          if (!snapshot.hasData) return CenteredProgressIndicator();

          return _buildCard(context: context);
        },
      );
    }

    return _buildCard(context: context);
  }

  Widget _buildCard({required BuildContext context}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (!isChatItem) ...[
          Text(
            TimeFormatter.getShortHumanReadableDate(
              quote.createTime?.toDate(),
              context,
            ),
            style: Theme.of(context).textTheme.bodyLarge!.apply(
              color: context.doneColors.typographyMediumContrast,
            ),
          ),
          const VerticalMargin.verySmall(),
        ],
        ContentCard(
          title: _quoteTitle(context),
          subTitle: Text(
            quote.informationText(context),
            style: Theme.of(context).textTheme.bodyMedium!.apply(
              color: context.doneColors.uiOnPrimary,
            ),
          ),
          onTap: onTap ?? () => _showQuote(context, quote),
          backgroundColor: quote.bgColor(context),
          height:
              (quote.deductions != null && quote.deductions!.length > 1)
                  ? 88
                  : 74,
          leading: DeductionTypesBadge(deductionTypes: quote.deductions),
          actions: _actions(context, quote.status),
        ),
      ],
    );
  }

  String _quoteTitle(BuildContext context) {
    final amountText =
        quote.amountAfterTaxDeductions > 0
            ? '(${getPriceString(quote.amountAfterTaxDeductions)})'
            : '- ${S.of(context).prepaid}';
    return "${quote.title(context)} $amountText";
  }

  Future<void> _showQuote(BuildContext context, Quote quote) async {
    if (quote.rawQuote != null) {
      final params = QuoteDetailsRouteParams(
        projectId: jobId,
        quoteId: quote.id!,
      );
      return QuoteDetailsRoute(params: params).navigate(context);
    } else {
      return viewAsPdf(
        context: context,
        storagePath: quote.fileRef!,
        pushRoute: (url) {
          final params = ViewQuotePdfRouteParams(
            projectId: jobId,
            quoteId: quote.id!,
            url: url,
          );
          ViewQuotePdfRoute(params: params).navigate(context);
        },
      );
    }
  }

  List<Widget> _actions(BuildContext context, QuoteStatus quoteStatus) {
    final isCustomer = context.authState.isUserCustomer();

    if (isCustomer && quoteStatus == QuoteStatus.pending) {
      return [
        DoneButton(
          title: Text(S.of(context).accept),
          style: DoneButtonStyle.positive,
          onPressed: () => showQuoteAcceptConfirmation(context, quote),
        ),
        const VerticalMargin.verySmall(),
        DoneButton(
          title: Text(S.of(context).offerDecline),
          style: DoneButtonStyle.negative,
          onPressed: () => showQuoteDeclineDialog(context, quote),
        ),
      ];
    } else if (!isCustomer && quoteStatus == QuoteStatus.declined) {
      final reasonString =
          (quote.declineReason == QuoteDeclineReason.other)
              ? quote.declineReasonOther
              : quote.declineReason?.title(context);

      if (reasonString == null ||
          quote.declineReason == QuoteDeclineReason.dislikedContractor)
        return [];

      return [Text('${S.of(context).cause}: $reasonString')];
    } else {
      return [];
    }
  }
}
