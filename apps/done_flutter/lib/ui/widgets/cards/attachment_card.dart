import 'package:done_flutter/ui/widgets/cards/rich_metadata_card.dart';
import 'package:done_models/done_models.dart';
import 'package:flutter/material.dart';

class AttachmentCard extends StatelessWidget {
  const AttachmentCard({super.key, required this.attachment});

  final Attachment attachment;

  @override
  Widget build(BuildContext context) {
    final title =
        attachment.quantity != null
            ? "${attachment.quantity} x ${attachment.title}"
            : attachment.title;
    return RichMetadataCard(
      title: title!,
      url: attachment.url,
      description: attachment.description,
      image: attachment.image,
      logo: attachment.logo,
      isDense: true,
    );
  }
}
