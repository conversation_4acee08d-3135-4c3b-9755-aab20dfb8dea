import 'package:cloud_firestore/cloud_firestore.dart';

import 'package:done_database/done_database.dart';

import 'package:done_flutter/ui/widgets/company_avatar.dart';
import 'package:done_flutter/ui/widgets/company_reviews_summary.dart';
import 'package:done_image/done_image.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

import 'package:get_it/get_it.dart';

class CompanyCard extends StatelessWidget {
  const CompanyCard({
    this.companyRef,
    this.type = CompanyCardType.info,
    this.company,
    required this.onTap,
  }) : assert(company != null || companyRef != null);

  final DocumentReference<Company>? companyRef;
  final Company? company;
  final VoidCallback onTap;
  final CompanyCardType type;

  @override
  Widget build(BuildContext context) {
    if (company != null) {
      return _CompanyContentCard(
        onTap: onTap,
        company: company!,
        type: type,
        isCached: true,
      );
    }

    return _StreamCompanyCard(
      companyRef: companyRef!,
      type: type,
      onTap: onTap,
    );
  }
}

class _StreamCompanyCard extends StatelessWidget {
  const _StreamCompanyCard({
    required this.companyRef,
    this.type = CompanyCardType.info,
    required this.onTap,
  });

  final DocumentReference<Company> companyRef;
  final CompanyCardType type;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<Company>(
      stream: GetIt.instance<CompanyRepository>().company(companyRef.id),
      builder: (context, company) {
        if (!company.hasData) return const EmptyContentCard(height: 70);
        return _CompanyContentCard(
          company: company.data!,
          type: type,
          onTap: onTap,
        );
      },
    );
  }
}

class _CompanyContentCard extends StatelessWidget {
  const _CompanyContentCard({
    this.type = CompanyCardType.info,
    required this.company,
    this.isCached = false,
    required this.onTap,
  });

  final CompanyCardType type;
  final Company company;

  /// Whether the company data is cached or live
  /// This is useful for when we want to show cached data instead of live for invoicing
  final bool isCached;

  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    final reviewsCount = company.statistics?.numberOfReviews ?? 0;
    return ContentCard(
      icon: type == CompanyCardType.edit ? Icons.edit : Icons.chevron_right,
      title: company.name,
      subTitle:
          reviewsCount > 0 ? CompanyReviewsSummary(company: company) : null,
      titleBadge: Image.asset(ImageAssets.verified),
      leading: CompanyAvatar(company.reference, radius: 24),
      onTap: onTap,
    );
  }
}

enum CompanyCardType { edit, info }
