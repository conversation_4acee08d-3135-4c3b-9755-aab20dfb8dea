import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:done_flutter/ui/widgets/user_avatar.dart';
import 'package:done_database/done_database.dart';

typedef _CustomerCardBuilder = Widget Function(User customer);

/// A card that display Customer's basic info
///
/// It takes either a [customer] or [customerRef]
class CustomerCard extends StatelessWidget {
  const CustomerCard({
    required this.onTap,
    this.customerRef,
    this.hasSubtitle = true,
    this.customer,
    this.borderColor,
    this.iconColor,
    this.type = CustomerCardType.info,
    this.icon,
  }) : assert(customer != null || customerRef != null);

  final DocumentReference? customerRef;

  /// Cached customer data, should include `sensitiveCustomerData`
  final User? customer;
  final bool hasSubtitle;

  final Color? borderColor;
  final Color? iconColor;
  final IconData? icon;

  /// It affects how the card looks (different icons) and its behavior
  ///
  /// Defaults to [CustomerCardType.info]
  final CustomerCardType type;

  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    if (customer != null) {
      return buildContentCard(customer!);
    }

    return _CustomerStreamCard(
      customerRef: customerRef!,
      builder: buildContentCard,
    );
  }

  Widget buildContentCard(User customer) {
    return _CustomerContentCard(
      onTap: onTap,
      customer: customer,
      hasSubtitle: hasSubtitle,
      type: type,
      borderColor: borderColor,
      iconColor: iconColor,
      icon: icon,
    );
  }
}

class _CustomerStreamCard extends StatelessWidget {
  const _CustomerStreamCard({required this.customerRef, required this.builder});

  final DocumentReference customerRef;

  final _CustomerCardBuilder builder;

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<User>(
      stream: GetIt.instance<UserRepository>().user(customerRef.id),
      builder: (context, userSnapshot) {
        if (!userSnapshot.hasData) return const EmptyContentCard(height: 90);
        return builder(userSnapshot.data!);
      },
    );
  }
}

class _CustomerContentCard extends StatelessWidget {
  const _CustomerContentCard({
    required this.customer,
    required this.hasSubtitle,
    required this.onTap,
    this.type = CustomerCardType.info,
    this.borderColor,
    this.iconColor,
    this.icon,
  });

  final User customer;
  final CustomerCardType type;
  final bool hasSubtitle;
  final Color? borderColor;
  final Color? iconColor;
  final IconData? icon;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return ContentCard(
      icon:
          icon ??
          (type == CustomerCardType.edit ? Icons.edit : Icons.chevron_right),
      iconColor: iconColor ?? context.doneColors.typographyMediumContrast,
      title: customer.fullName,
      titleBadge:
          (customer.sensitiveCustomerData?.hasNeccessaryRotInfo ?? false)
              ? DoneTinyBadge(value: DeductionType.rot.name)
              : null,
      subTitle: hasSubtitle ? Text(S.of(context).customer) : null,
      borderColor: borderColor ?? context.doneColors.uiBg1,
      leading: NonFetchingUserAvatar(user: customer, radius: 24),
      onTap: onTap,
    );
  }
}

enum CustomerCardType { edit, info }
