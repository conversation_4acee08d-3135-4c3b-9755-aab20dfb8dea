import 'package:done_router/done_router.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

class RichMetadataCard extends StatelessWidget {
  const RichMetadataCard({
    super.key,
    required this.title,
    this.url,
    this.description,
    this.image,
    this.logo,
    this.borderColor,
    this.isDense = false,
    this.onTap,
  });

  final String title;
  final String? url;
  final String? description;
  final String? image;
  final String? logo;
  final Color? borderColor;
  final bool isDense;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    final image = this.image == null ? null : Image.network(this.image!);
    final height = isDense ? 52.0 : 72.0;
    final imageWidth = isDense ? 50.0 : 70.0;

    return ContentCard(
      height: height,
      borderColor: borderColor ?? context.doneColors.uiBg1,
      title: title,
      subTitle: (description?.isNotEmpty ?? false) ? Text(description!) : null,
      titleBadge:
          logo != null
              ? SizedBox(width: 30, child: Image.network(logo!))
              : const SizedBox(),
      leadingPadding: isDense ? const EdgeInsets.only(right: 6) : null,
      padding: const EdgeInsets.all(6),
      leading:
          image != null
              ? ClipRRect(
                borderRadius: const BorderRadius.horizontal(
                  left: Radius.circular(Margins.medium),
                ),
                child: SizedBox(width: imageWidth, child: image),
              )
              : const SizedBox(),
      onTap:
          onTap ??
          (url != null
              ? () => URLRouter.instance.handleUrl(url, context: context)
              : null),
    );
  }
}
