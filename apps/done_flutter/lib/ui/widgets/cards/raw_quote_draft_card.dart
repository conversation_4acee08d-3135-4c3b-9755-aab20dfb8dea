import 'package:done_deductions/done_deductions.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_flutter/utils/dialogs/quote_dialogs.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

class RawQuoteDraftCard extends StatelessWidget {
  const RawQuoteDraftCard({
    super.key,
    required this.job,
    required this.rawQuote,
  });

  final Job job;
  final RawQuote rawQuote;

  @override
  Widget build(BuildContext context) {
    return _buildCard(context: context);
  }

  Widget _buildCard({required BuildContext context}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          TimeFormatter.getShortHumanReadableDate(
            rawQuote.createTime?.toDate(),
            context,
          ),
          style: Theme.of(context).textTheme.bodyLarge!.apply(
            color: context.doneColors.typographyMediumContrast,
          ),
        ),
        const VerticalMargin.verySmall(),
        ContentCard(
          title: _quoteTitle(context),
          subTitle: Text(
            S.of(context).draft,
            style: Theme.of(context).textTheme.bodyMedium!.apply(
              color: context.doneColors.uiOnPrimary,
            ),
          ),
          onTap: () {
            showQuoteGenerator(context: context, job: job, rawQuote: rawQuote);
          },
          backgroundColor: context.doneColors.uiBg2.withValues(alpha: 0.5),
          height: (rawQuote.lineItems.deductionTypes.length > 1) ? 88 : 74,
          leading: DeductionTypesBadge(
            deductionTypes: rawQuote.lineItems.deductionTypes,
          ),
        ),
      ],
    );
  }

  String _quoteTitle(BuildContext context) {
    return "${rawQuote.type == QuoteType.offer ? S.of(context).quote : S.of(context).additionalCosts} (${getPriceString(rawQuote.totalNetAfterTaxDeductions)})";
  }
}
