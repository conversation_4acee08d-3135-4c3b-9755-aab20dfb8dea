import 'dart:math';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:done_booking/done_booking.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_image/done_image.dart';
import 'package:done_models/done_models.dart';
import 'package:done_router/done_router.dart';
import 'package:done_ui/done_ui.dart';
import 'package:expandable/expandable.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

Color _themeColor(BuildContext context) => ServiceType.flooring.color(context);

class FloorSandingCalculatorPage extends StatefulWidget {
  const FloorSandingCalculatorPage({super.key});

  @override
  _FloorSandingCalculatorPageState createState() =>
      _FloorSandingCalculatorPageState();
}

class FloorSandingCalculatorBrain {
  FloorSandingCalculatorBrain({
    this.calculationValues,
    this.squareMeters,
    this.pigmentation,
    this.treatmentIndex,
    required this.floorTypeIndex,
  });

  FloorSandingCalculatorValues? calculationValues;
  double? squareMeters;
  bool? pigmentation;
  int? treatmentIndex;
  int floorTypeIndex;

  FloorSandingCalculatorBrainResult? get result {
    if (calculationValues == null ||
        this.squareMeters == null ||
        pigmentation == null ||
        treatmentIndex == null)
      return null;

    final squareMeters = this.squareMeters!.roundToDouble();

    final absoluteMinLaborCost = calculationValues!.minLaborCost;
    final absoluteMinUpperLaborCost = absoluteMinLaborCost * 2;

    final minBaseSqmPrice =
        calculationValues!.floorTypes[floorTypeIndex].minSqmPrice;
    final maxBaseSqmPrice =
        calculationValues!.floorTypes[floorTypeIndex].maxSqmPrice;

    final treatmentLaborFactor =
        calculationValues!.treatments[treatmentIndex!].priceModifier;

    final floorType = calculationValues!.floorTypes[floorTypeIndex].name;
    final pigmentationPriceModifier =
        pigmentation != true
            ? calculationValues!
                .pigmentationPriceModifiersPerFloorType[floorType]!
            : 1.0;

    final laborMinCost = max(
      squareMeters *
          minBaseSqmPrice *
          treatmentLaborFactor *
          pigmentationPriceModifier,
      absoluteMinLaborCost,
    );
    final laborMaxCost = max(
      squareMeters *
          maxBaseSqmPrice *
          treatmentLaborFactor *
          pigmentationPriceModifier,
      absoluteMinUpperLaborCost,
    );

    final materialMinCost =
        squareMeters *
        calculationValues!.treatments[treatmentIndex!].materialMinCost;
    final materialMaxCost =
        squareMeters *
        calculationValues!.treatments[treatmentIndex!].materialMaxCost;

    final minStartCost = calculationValues!.transportationCost;
    final maxStartCost = minStartCost * 2;

    final minTotal =
        laborMinCost * (1 - swedishRotDeductionRate) +
        materialMinCost +
        minStartCost;
    final maxTotal =
        laborMaxCost * (1 - swedishRotDeductionRate) +
        materialMaxCost +
        maxStartCost;

    return FloorSandingCalculatorBrainResult(
      laborMinCost: laborMinCost.toDouble(),
      laborMaxCost: laborMaxCost.toDouble(),
      materialMinCost: materialMinCost,
      materialMaxCost: materialMaxCost,
      minStartCost: minStartCost.toDouble(),
      maxStartCost: maxStartCost.toDouble(),
      minTaxDeduction: laborMinCost.toDouble() * swedishRotDeductionRate,
      maxTaxDeduction: laborMaxCost.toDouble() * swedishRotDeductionRate,
      minTotal: minTotal.toDouble(),
      maxTotal: maxTotal.toDouble(),
    );
  }
}

class FloorSandingCalculatorBrainResult {
  const FloorSandingCalculatorBrainResult({
    required this.laborMinCost,
    required this.laborMaxCost,
    required this.materialMinCost,
    required this.materialMaxCost,
    required this.minStartCost,
    required this.maxStartCost,
    required this.minTaxDeduction,
    required this.maxTaxDeduction,
    required this.minTotal,
    required this.maxTotal,
  });

  final double laborMinCost;
  final double laborMaxCost;
  final double materialMinCost;
  final double materialMaxCost;
  final double minStartCost;
  final double maxStartCost;
  final double minTaxDeduction;
  final double maxTaxDeduction;
  final double minTotal;
  final double maxTotal;
}

class _FloorSandingCalculatorPageState
    extends State<FloorSandingCalculatorPage> {
  final _brain = FloorSandingCalculatorBrain(
    squareMeters: 20,
    pigmentation: false,
    treatmentIndex: 0,
    floorTypeIndex: 0,
  );

  @override
  Widget build(BuildContext context) {
    return ChipTheme(
      data: ChipTheme.of(context).copyWith(
        secondarySelectedColor: _themeColor(context).withValues(alpha: 0.9),
        labelStyle: TextStyle(
          color: Theme.of(context).textTheme.titleMedium!.color,
          fontSize: 16,
        ),
        secondaryLabelStyle: const TextStyle(color: Colors.white, fontSize: 16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        labelPadding: const EdgeInsets.symmetric(vertical: 3, horizontal: 10),
      ),
      child: Scaffold(
        appBar: AppBar(
          systemOverlayStyle: SystemUiOverlayStyle.dark,
          backgroundColor: _themeColor(context),
          elevation: 0,
        ),
        body: StreamBuilder<DocumentSnapshot<Map<String, dynamic>>>(
          // TODO(ayman): refactor into model and extract to database
          stream:
              FirebaseFirestore.instance
                  .doc('calculators/floor-sanding')
                  .snapshots(),
          builder: (context, snapshot) {
            if (!snapshot.hasData) return CenteredProgressIndicator();
            final values = FloorSandingCalculatorValues.fromDocumentSnapshot(
              snapshot.data!,
            );
            _brain.calculationValues = values;

            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                const _FloorSandingCalculatorHeader(),
                Expanded(
                  child: Scrollbar(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: ListView(
                        children: [
                          const VerticalMargin.xlarge(),
                          Text(
                            S
                                .of(context)
                                .floorArea(
                                  _brain.squareMeters!.round().toString(),
                                ),
                            style: Theme.of(context).textTheme.titleLarge,
                          ),
                          Slider(
                            activeColor: _themeColor(context),
                            inactiveColor: _themeColor(
                              context,
                            ).withValues(alpha: 0.4),
                            value: _brain.squareMeters!,
                            min: values.minArea.toDouble(),
                            max: values.maxArea.toDouble(),
                            onChanged:
                                (double newValue) => setState(
                                  () => _brain.squareMeters = newValue,
                                ),
                          ),
                          Text(
                            S.of(context).floorSandingWoodType,
                            style: Theme.of(context).textTheme.titleLarge,
                          ),
                          Wrap(
                            spacing: 10,
                            children:
                                List<Widget>.generate(
                                  values.floorTypes.length,
                                  (int index) => ChoiceChip(
                                    label: Text(values.floorTypes[index].name),
                                    selected: _brain.floorTypeIndex == index,
                                    onSelected:
                                        (bool selected) => setState(() {
                                          _brain.floorTypeIndex = index;
                                        }),
                                  ),
                                ).toList(),
                          ),
                          const VerticalMargin.medium(),
                          Text(
                            S.of(context).floorSandingTreatment,
                            style: Theme.of(context).textTheme.titleLarge,
                          ),
                          Wrap(
                            spacing: 10,
                            children:
                                List<Widget>.generate(
                                  values.treatments.length,
                                  (int index) => ChoiceChip(
                                    label: Text(values.treatments[index].name),
                                    selected: _brain.treatmentIndex == index,
                                    onSelected:
                                        (bool selected) => setState(() {
                                          _brain.treatmentIndex = index;
                                        }),
                                  ),
                                ).toList(),
                          ),
                          const VerticalMargin.medium(),
                          Text(
                            S.of(context).floorSandingPigmentation,
                            style: Theme.of(context).textTheme.titleLarge,
                          ),
                          const VerticalMargin.medium(),
                          Wrap(
                            spacing: 10,
                            children: <Widget>[
                              ChoiceChip(
                                label: Text(S.of(context).yes),
                                selected: _brain.pigmentation ?? false,
                                onSelected:
                                    (bool selected) => setState(() {
                                      _brain.pigmentation = true;
                                    }),
                              ),
                              ChoiceChip(
                                label: Text(S.of(context).no),
                                selected: _brain.pigmentation == false,
                                onSelected:
                                    (bool selected) => setState(() {
                                      _brain.pigmentation = false;
                                    }),
                              ),
                            ],
                          ),
                          const VerticalMargin.medium(),
                          MultilineMarkdownBody(
                            data: values.treatmentHelpMarkdown,
                            onTapLink:
                                (_, url, __) => URLRouter.instance.handleUrl(
                                  url,
                                  context: context,
                                ),
                          ),
                          const VerticalMargin.medium(),
                          const Divider(),
                          CalculationResultsDisplay(
                            result: _brain.result,
                            outroText: values.outroText,
                          ),
                          const VerticalMargin.xlarge(),
                          DoneButton(
                            style: DoneButtonStyle.secondary,
                            title: Text(S.of(context).bookVideoCall),
                            image: Image.asset(
                              ImageAssets.videocall,
                              scale: 3,
                              color: context.doneColors.uiPrimary,
                            ),
                            onPressed:
                                () => bookVideoCall(
                                  context,
                                  source: 'floor_sanding_calculator',
                                ),
                          ),
                          const VerticalMargin.xlarge(),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }
}

class _FloorSandingCalculatorHeader extends StatelessWidget {
  const _FloorSandingCalculatorHeader();

  @override
  Widget build(BuildContext context) {
    return DecoratedBox(
      decoration: BoxDecoration(
        color: _themeColor(context),
        borderRadius: const BorderRadius.only(bottomLeft: Radius.circular(30)),
      ),
      child: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(20, 0, 20, 20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: <Widget>[
                Text(
                  S.of(context).floorSandingTitle,
                  style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                    fontSize: 24,
                    color: Colors.white,
                  ),
                ),
                Text(
                  S.of(context).floorSandingSubtitle,
                  style: const TextStyle(color: Colors.white70, fontSize: 20),
                ),
              ],
            ),
          ),
          Positioned(
            bottom: 0,
            right: 0,
            child: Image.asset(ImageAssets.illustrationFlooring, height: 80),
          ),
        ],
      ),
    );
  }
}

class CalculationResultsDisplay extends StatelessWidget {
  const CalculationResultsDisplay({
    super.key,
    required this.result,
    required this.outroText,
  });

  final FloorSandingCalculatorBrainResult? result;
  final String outroText;

  String _format(double value) {
    return getPriceString((value / 100).round() * 100);
  }

  @override
  Widget build(BuildContext context) {
    if (result == null) {
      return Text(
        S.of(context).cost,
        style: Theme.of(
          context,
        ).textTheme.titleLarge!.copyWith(color: Theme.of(context).primaryColor),
      );
    }

    return ExpandablePanel(
      key: const Key("ExpandablePanelCalculator"),
      theme: const ExpandableThemeData(
        headerAlignment: ExpandablePanelHeaderAlignment.center,
      ),
      header: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.baseline,
        textBaseline: TextBaseline.alphabetic,
        children: <Widget>[
          Text(
            S.of(context).cost,
            style: Theme.of(
              context,
            ).textTheme.titleLarge!.copyWith(color: _themeColor(context)),
          ),
          Text(
            S.of(context).seeMore,
            style: Theme.of(
              context,
            ).textTheme.bodyMedium!.copyWith(color: Colors.black87),
          ),
        ],
      ),
      collapsed: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Text(
            '${_format(result!.minTotal)} - ${_format(result!.maxTotal)}',
            style: Theme.of(context).textTheme.headlineMedium,
          ),
          const VerticalMargin.verySmall(),
          Text(outroText),
        ],
      ),
      expanded: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Text(
            '${_format(result!.minTotal)} - ${_format(result!.maxTotal)}',
            style: Theme.of(context).textTheme.headlineMedium,
          ),
          const VerticalMargin.medium(),
          DecoratedBox(
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CalculatorLabelValue(
                  S.of(context).costBreakdownLabor,
                  '${_format(result!.laborMinCost)} - ${_format(result!.laborMaxCost)}',
                ),
                CalculatorLabelValue(
                  S.of(context).costBreakdownROTDeduction,
                  '${_format(result!.minTaxDeduction)} - ${_format(result!.maxTaxDeduction)}',
                  negative: true,
                ),
                Container(color: Colors.grey, height: 0.5),
                CalculatorLabelValue(
                  S.of(context).costBreakdownMaterial,
                  '${_format(result!.materialMinCost)} - ${_format(result!.materialMaxCost)}',
                ),
                Container(color: Colors.grey, height: 0.5),
                CalculatorLabelValue(
                  S.of(context).costBreakdownTransport,
                  '${_format(result!.minStartCost)} - ${_format(result!.maxStartCost)}',
                ),
              ],
            ),
          ),
          const VerticalMargin.medium(),
          Text(outroText),
        ],
      ),
    );
  }
}

class CalculatorLabelValue extends StatelessWidget {
  const CalculatorLabelValue(
    this.label,
    this.value, {
    super.key,
    this.negative = false,
  });

  final String label;
  final String value;
  final bool negative;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Text(label),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyLarge!.copyWith(
              fontWeight: FontWeight.w600,
              color: negative ? Colors.red : null,
            ),
          ),
        ],
      ),
    );
  }
}
