import 'dart:math';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:done_booking/done_booking.dart';
import 'package:done_flutter/utils/extensions/models/painting_calculator_values.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_flutter/ui/pages/customer/calculators/floor_sanding_calculator.dart';
import 'package:done_image/done_image.dart';
import 'package:done_models/done_models.dart';
import 'package:done_router/done_router.dart';
import 'package:done_ui/done_ui.dart';
import 'package:expandable/expandable.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

Color _themeColor(BuildContext context) => ServiceType.painter.color(context);

class PaintingCalculatorPage extends StatefulWidget {
  const PaintingCalculatorPage({super.key});
  @override
  _PaintingCalculatorPageState createState() => _PaintingCalculatorPageState();
}

class PaintingCalculatorBrain {
  PaintingCalculatorBrain({
    this.calculationValues,
    this.squareMeters,
    this.priming,
    this.plastering,
    this.layersOfPaint,
  });

  PaintingCalculatorValues? calculationValues;
  double? squareMeters;
  bool? priming;
  int? plastering;
  int? layersOfPaint;

  PaintingCalculatorBrainResult? get result {
    if (calculationValues == null ||
        this.squareMeters == null ||
        priming == null ||
        plastering == null ||
        layersOfPaint == null)
      return null;

    final squareMeters = this.squareMeters!.roundToDouble();

    final priceMultiplier =
        (plastering == 0 && layersOfPaint == 0 && !priming!)
            ? 0
            : pow(2, plastering! + layersOfPaint! + (priming! ? 1 : 0) - 1);

    final minPerSqCost = calculationValues!.minPerSqCost.roundToDouble();
    final maxPerSqCost = calculationValues!.maxPerSqCost.roundToDouble();

    final materialMinCost = calculationValues!.materialMinCost..roundToDouble();
    final materialMaxCost = calculationValues!.materialMaxCost.roundToDouble();

    var laborMinCost = minPerSqCost * squareMeters * priceMultiplier;
    var laborMaxCost = maxPerSqCost * squareMeters * priceMultiplier;

    if (laborMinCost < calculationValues!.fixedMinPrice.roundToDouble()) {
      laborMinCost = calculationValues!.fixedMinPrice.roundToDouble();
      laborMaxCost = calculationValues!.fixedMaxPrice.roundToDouble();
    }

    final minStartCost = calculationValues!.transportationCost;
    final maxStartCost = minStartCost * 2;

    final minTotal =
        laborMinCost * (1 - swedishRotDeductionRate) +
        materialMinCost +
        minStartCost;
    final maxTotal =
        laborMaxCost * (1 - swedishRotDeductionRate) +
        materialMaxCost +
        maxStartCost;

    return PaintingCalculatorBrainResult(
      laborMinCost: laborMinCost,
      laborMaxCost: laborMaxCost,
      materialMinCost: materialMinCost.toDouble(),
      materialMaxCost: materialMaxCost.toDouble(),
      minStartCost: minStartCost.toDouble(),
      maxStartCost: maxStartCost.toDouble(),
      minTaxDeduction: laborMinCost * swedishRotDeductionRate,
      maxTaxDeduction: laborMaxCost * swedishRotDeductionRate,
      minTotal: minTotal,
      maxTotal: maxTotal,
    );
  }
}

class PaintingCalculatorBrainResult {
  const PaintingCalculatorBrainResult({
    required this.laborMinCost,
    required this.laborMaxCost,
    required this.materialMinCost,
    required this.materialMaxCost,
    required this.minStartCost,
    required this.maxStartCost,
    required this.minTaxDeduction,
    required this.maxTaxDeduction,
    required this.minTotal,
    required this.maxTotal,
  });

  final double laborMinCost;
  final double laborMaxCost;
  final double materialMinCost;
  final double materialMaxCost;
  final double minStartCost;
  final double maxStartCost;
  final double minTaxDeduction;
  final double maxTaxDeduction;
  final double minTotal;
  final double maxTotal;
}

class _PaintingCalculatorPageState extends State<PaintingCalculatorPage> {
  final _brain = PaintingCalculatorBrain(
    squareMeters: 40,
    plastering: 0,
    priming: false,
    layersOfPaint: 0,
  );

  @override
  Widget build(BuildContext context) {
    final locale = Localizations.localeOf(context);

    return ChipTheme(
      data: ChipTheme.of(context).copyWith(
        secondarySelectedColor: _themeColor(context).withValues(alpha: 0.9),
        labelStyle: TextStyle(
          color: Theme.of(context).textTheme.titleMedium!.color,
          fontSize: 16,
        ),
        secondaryLabelStyle: const TextStyle(color: Colors.white, fontSize: 16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        labelPadding: const EdgeInsets.symmetric(vertical: 3, horizontal: 10),
      ),
      child: Scaffold(
        appBar: AppBar(
          systemOverlayStyle: SystemUiOverlayStyle.dark,
          backgroundColor: _themeColor(context),
          elevation: 0,
        ),
        body: StreamBuilder<DocumentSnapshot<Map<String, dynamic>>>(
          // TODO(ayman): refactor into model and extract to database
          stream:
              FirebaseFirestore.instance
                  .doc('calculators/painting')
                  .snapshots(),
          builder: (context, snapshot) {
            if (!snapshot.hasData) return CenteredProgressIndicator();
            final values = PaintingCalculatorValues.fromDocumentSnapshot(
              snapshot.data!,
            );
            _brain.calculationValues = values;

            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                const _PaintingCalculatorHeader(),
                Expanded(
                  child: Scrollbar(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: ListView(
                        children: [
                          const VerticalMargin.xlarge(),
                          Text(
                            S
                                .of(context)
                                .wallArea(
                                  _brain.squareMeters!.round().toString(),
                                ),
                            style: Theme.of(context).textTheme.titleLarge,
                          ),
                          Slider(
                            activeColor: _themeColor(context),
                            inactiveColor: _themeColor(
                              context,
                            ).withValues(alpha: 0.4),
                            value: _brain.squareMeters!,
                            min: values.minArea.toDouble(),
                            max: values.maxArea.toDouble(),
                            onChanged:
                                (double newValue) => setState(
                                  () => _brain.squareMeters = newValue,
                                ),
                          ),
                          Text(
                            S.of(context).painterCalculatorPlastering,
                            style: Theme.of(context).textTheme.titleLarge,
                          ),
                          const VerticalMargin.small(),
                          MultilineMarkdownBody(
                            data: values.plasteringHelperText(locale),
                            onTapLink:
                                (_, url, __) => URLRouter.instance.handleUrl(
                                  url,
                                  context: context,
                                ),
                          ),
                          const VerticalMargin.small(),
                          Wrap(
                            spacing: 10,
                            children:
                                List<Widget>.generate(
                                  values.plastering.length,
                                  (int index) => ChoiceChip(
                                    label: Text(values.plastering[index]),
                                    selected: _brain.plastering == index,
                                    onSelected:
                                        (bool selected) => setState(() {
                                          _brain.plastering = index;
                                        }),
                                  ),
                                ).toList(),
                          ),
                          const Divider(),
                          const VerticalMargin.small(),
                          Text(
                            S.of(context).painterCalculatorPriming,
                            style: Theme.of(context).textTheme.titleLarge,
                          ),
                          const VerticalMargin.small(),
                          MultilineMarkdownBody(
                            data: values.primingHelperText(locale),
                            onTapLink:
                                (_, url, __) => URLRouter.instance.handleUrl(
                                  url,
                                  context: context,
                                ),
                          ),
                          const VerticalMargin.small(),
                          Wrap(
                            spacing: 10,
                            children: <Widget>[
                              ChoiceChip(
                                label: Text(S.of(context).yes),
                                selected: _brain.priming ?? false,
                                onSelected:
                                    (bool selected) => setState(() {
                                      _brain.priming = true;
                                    }),
                              ),
                              ChoiceChip(
                                label: Text(S.of(context).no),
                                selected: _brain.priming == false,
                                onSelected:
                                    (bool selected) => setState(() {
                                      _brain.priming = false;
                                    }),
                              ),
                            ],
                          ),
                          const Divider(),
                          const VerticalMargin.small(),
                          Text(
                            S.of(context).painterCalculatorColor,
                            style: Theme.of(context).textTheme.titleLarge,
                          ),
                          const VerticalMargin.small(),
                          MultilineMarkdownBody(
                            data: values.colorHelperText(locale),
                            onTapLink:
                                (_, url, __) => URLRouter.instance.handleUrl(
                                  url,
                                  context: context,
                                ),
                          ),
                          const VerticalMargin.small(),
                          Wrap(
                            spacing: 10,
                            children:
                                List<Widget>.generate(
                                  values.paintTypes.length,
                                  (int index) => ChoiceChip(
                                    label: Text(values.paintTypes[index]),
                                    selected: _brain.layersOfPaint == index,
                                    onSelected:
                                        (bool selected) => setState(() {
                                          _brain.layersOfPaint = index;
                                        }),
                                  ),
                                ).toList(),
                          ),
                          const VerticalMargin.verySmall(),
                          const Divider(),
                          PaintingCalculationResultsDisplay(
                            result: _brain.result,
                            outroText: values.outroText(locale),
                          ),
                          const VerticalMargin.xlarge(),
                          DoneButton(
                            style: DoneButtonStyle.secondary,
                            title: Text(S.of(context).bookVideoCall),
                            image: Image.asset(
                              ImageAssets.videocall,
                              scale: 3,
                              color: context.doneColors.uiPrimary,
                            ),
                            onPressed:
                                () => bookVideoCall(
                                  context,
                                  source: 'painting_calculator',
                                ),
                          ),
                          const VerticalMargin.xlarge(),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }
}

class _PaintingCalculatorHeader extends StatelessWidget {
  const _PaintingCalculatorHeader();

  @override
  Widget build(BuildContext context) {
    return DecoratedBox(
      decoration: BoxDecoration(
        color: _themeColor(context),
        borderRadius: const BorderRadius.only(bottomLeft: Radius.circular(30)),
      ),
      child: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(20, 0, 20, 20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: <Widget>[
                Text(
                  S.of(context).categoryPainting,
                  style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                    fontSize: 24,
                    color: Colors.white,
                  ),
                ),
                Text(
                  S.of(context).floorSandingSubtitle,
                  style: const TextStyle(color: Colors.white70, fontSize: 20),
                ),
              ],
            ),
          ),
          Positioned(
            bottom: 0,
            right: 0,
            child: Image.asset(ImageAssets.illustrationPainting, height: 80),
          ),
        ],
      ),
    );
  }
}

class PaintingCalculationResultsDisplay extends StatelessWidget {
  const PaintingCalculationResultsDisplay({
    super.key,
    required this.result,
    required this.outroText,
  });

  final PaintingCalculatorBrainResult? result;
  final String outroText;

  String _format(double value) {
    return getPriceString((value / 100).round() * 100);
  }

  @override
  Widget build(BuildContext context) {
    if (result == null) {
      return Text(
        S.of(context).cost,
        style: Theme.of(
          context,
        ).textTheme.titleLarge!.copyWith(color: context.doneColors.uiBlack),
      );
    }

    return ExpandablePanel(
      theme: const ExpandableThemeData(
        headerAlignment: ExpandablePanelHeaderAlignment.center,
      ),
      header: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.baseline,
        textBaseline: TextBaseline.alphabetic,
        children: <Widget>[
          Text(
            S.of(context).cost,
            style: Theme.of(
              context,
            ).textTheme.titleLarge!.copyWith(color: _themeColor(context)),
          ),
          Text(
            S.of(context).seeMore,
            style: Theme.of(
              context,
            ).textTheme.bodyMedium!.copyWith(color: Colors.black87),
          ),
        ],
      ),
      collapsed: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Text(
            '${_format(result!.minTotal)} - ${_format(result!.maxTotal)}',
            style: Theme.of(context).textTheme.headlineMedium,
          ),
          const VerticalMargin.verySmall(),
          Text(outroText),
        ],
      ),
      expanded: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Text(
            '${_format(result!.minTotal)} - ${_format(result!.maxTotal)}',
            style: Theme.of(context).textTheme.headlineMedium,
          ),
          const VerticalMargin.small(),
          DecoratedBox(
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                CalculatorLabelValue(
                  S.of(context).costBreakdownLabor,
                  '${_format(result!.laborMinCost)} - ${_format(result!.laborMaxCost)}',
                ),
                const Divider(),
                CalculatorLabelValue(
                  S.of(context).costBreakdownROTDeduction,
                  '${_format(result!.minTaxDeduction)} - ${_format(result!.maxTaxDeduction)}',
                  negative: true,
                ),
              ],
            ),
          ),
          const VerticalMargin.small(),
          Text(outroText),
        ],
      ),
    );
  }
}
