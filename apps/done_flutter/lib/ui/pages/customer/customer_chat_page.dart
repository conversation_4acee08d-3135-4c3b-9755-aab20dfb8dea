import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:done_auth/done_auth.dart';
import 'package:done_database/done_database.dart';
import 'package:done_flutter/core/blocs/master_detail_cubit/home_selection_cubit.dart';
import 'package:done_flutter/core/models/navigation_bar.dart';
import 'package:done_flutter/core/router/routes/project_routes.dart';
import 'package:done_flutter/core/router/routes/routes_definitions.dart';
import 'package:done_flutter/ui/pages/chat/chat_page.dart';
import 'package:done_flutter/ui/pages/chat/conversations_list_view.dart';
import 'package:done_flutter/ui/pages/customer/anonymous_changes_builder.dart';
import 'package:done_flutter/ui/pages/master_detail/layout_adaptive_page.dart';
import 'package:done_flutter/ui/widgets/buttons/log_in_action_button.dart';
import 'package:done_image/done_image.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

class CustomerChatPage extends StatelessWidget {
  const CustomerChatPage({super.key});
  static const keyIdentifier = 'customerChatPage';

  @override
  Widget build(BuildContext context) {
    return AnonymousChangesBuilder(
      builder: (context, isAnonymous, _) {
        return LayoutAdaptivePage<HomeSelectionCubit, Map<int, String?>>(
          stateExtractor: (state) => state[CustomerHomeTabs.chat.index],
          appBar: AppBar(
            centerTitle: false,
            title: Text(
              S.of(context).chats,
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            automaticallyImplyLeading: false,
            elevation: 0,
          ),
          masterViewBuilder:
              (newContext) => _CustomerChatView(
                onTap:
                    (id) => newContext
                        .read<HomeSelectionCubit>()
                        .updateSelectionFor(CustomerHomeTabs.chat, id),
              ),
          detailViewBuilder: (context, id) {
            if (id != null) {
              final job =
                  FirebaseFirestore.instance
                      .collection('jobs')
                      .doc(id)
                      .withJobConverter;
              return ChatPage(jobRef: job);
            }
            return DetailViewPlaceholder(asset: ImageAssets.chat);
          },
          child:
              isAnonymous
                  ? const _AnonymousUserChatView()
                  : _CustomerChatView(
                    onTap: (projectId) {
                      final params = ChatPageRouteParams(
                        projectId: projectId,
                        showProjectLink: true,
                      );
                      ChatPageRoute(params: params).navigate(context);
                    },
                  ),
        );
      },
    );
  }
}

class _CustomerChatView extends StatelessWidget {
  const _CustomerChatView({required this.onTap});
  final ValueChanged<String> onTap;

  @override
  Widget build(BuildContext context) {
    final userRef = context.authState.user?.documentReference;
    if (userRef == null) return CenteredProgressIndicator();
    return Padding(
      padding: const EdgeInsets.only(top: Margins.large),
      child: StreamBuilder(
        stream: GetIt.instance<ConversationsRepository>().userConversations(
          userRef.id,
        ),
        builder: (context, conversationsSnapshot) {
          if (!conversationsSnapshot.hasData)
            return CenteredProgressIndicator();
          final conversations = conversationsSnapshot.data!;
          if (conversations.messagePreviews.isEmpty) {
            return EmptyState(
              title: Text(S.of(context).customerChatEmptyStateTitle),
              description: Text(
                S.of(context).customerChatEmptyStateDescription,
              ),
              children: [
                DoneButton(
                  title: Text(S.of(context).getFreeQuote),
                  onPressed: () => const RootRoute().navigate(context),
                ),
              ],
            );
          }

          final tabIndex = CustomerHomeTabs.chat.index;

          return ConversationsListView(
            currentId: context.watch<HomeSelectionCubit>().state[tabIndex],
            messagesPreview: conversations.messagePreviews,
            onTap: onTap,
            storageKey: 'customerChatListView',
          );
        },
      ),
    );
  }
}

class _AnonymousUserChatView extends StatelessWidget {
  const _AnonymousUserChatView();

  @override
  Widget build(BuildContext context) {
    return EmptyState(
      title: Text(S.of(context).notLoggedIn),
      description: Text(S.of(context).anonymousChatEmptyStateDescription),
      children: const [LoginActionButton()],
    );
  }
}
