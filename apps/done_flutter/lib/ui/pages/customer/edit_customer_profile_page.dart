import 'package:done_flutter/ui/admin/admin_interface_switch.dart';
import 'package:done_flutter/ui/widgets/buttons/delete_account_button.dart';
import 'package:done_maps/done_maps.dart';
import 'package:done_models/done_models.dart';
import 'package:done_router/done_router.dart';
import 'package:flutter/services.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:get_it/get_it.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:done_database/done_database.dart';
import 'package:personnummer/personnummer.dart';

import 'package:done_localizations/done_localizations.dart';
import 'package:done_auth/done_auth.dart';
import 'package:done_flutter/ui/widgets/profile/profile_dialogs.dart';

/// Fetches sensitive customer data and propagates it to CustomerProfileForm
///
/// If the user is anonymously signed in an empty state prompting them to login will be shown.
///
class EditCustomerProfilePage extends StatelessWidget {
  const EditCustomerProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    final customer = context.authState.user;
    if (customer == null) return CenteredProgressIndicator();

    return StreamBuilder<SensitiveCustomerData>(
      stream: GetIt.instance<UserRepository>().sensitiveCustomerData(
        customer.documentReference.id,
      ),
      builder: (
        BuildContext context,
        AsyncSnapshot<SensitiveCustomerData> snapshot,
      ) {
        if (!snapshot.hasData) return const LoadingPage();

        customer.sensitiveCustomerData = snapshot.data;

        return CustomerProfileForm(customerWithSensitiveData: customer);
      },
    );
  }
}

class CustomerProfileForm extends StatefulWidget {
  const CustomerProfileForm({required this.customerWithSensitiveData});

  final User customerWithSensitiveData;

  @override
  State<CustomerProfileForm> createState() => _CustomerProfileFormState();
}

// TODO : Refactor the editing into a cubit so we don't rebuild the whole widget with each change.
class _CustomerProfileFormState extends State<CustomerProfileForm> {
  late User customer;
  bool _hasChangesOnCommonData = false;
  bool _hasChangesOnTaxDeductionData = false;
  late final GlobalKey<FormState> _personalInfoFormKey;
  late final GlobalKey<FormState> _rotInfoFormKey;

  bool get _hasChanges =>
      _hasChangesOnCommonData || _hasChangesOnTaxDeductionData;

  @override
  void initState() {
    super.initState();
    _personalInfoFormKey = GlobalKey<FormState>();
    _rotInfoFormKey = GlobalKey<FormState>();
    customer = widget.customerWithSensitiveData.clone();
    _createSensitiveDataIfNull();
  }

  @override
  Widget build(BuildContext context) {
    // FIXME: Rewrite to use PopScope instead
    // ignore: deprecated_member_use
    return WillPopScope(
      onWillPop: () {
        //* prevents going back if there are changes to data
        if (_hasChanges) {
          showYouHaveChangesDialog(context, _saveChanges);
          return Future.value(false);
        }

        FocusScope.of(context).requestFocus(FocusNode());

        return Future.value(true);
      },
      child: DoneModal(
        child: MediaQuery.removePadding(
          context: context,
          removeTop: true,
          child: Scaffold(
            appBar: _buildAppBar(),
            body: SingleChildScrollView(
              child: PageContent(
                child: Padding(
                  padding: const EdgeInsets.only(left: 20, right: 20),
                  child: _buildForm(),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildForm() {
    final isCurrentUser = context.authState.isCurrentUser(
      customer.documentReference,
    );
    final isSuperUser = context.authState.isSuperUser();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      key: const Key("profilePageListView"),
      children: <Widget>[
        Form(
          key: _personalInfoFormKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const VerticalMargin.xxlarge(),
              _buildPhoneNumber(),
              Text(
                S.of(context).editPersonalInfo,
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const VerticalMargin.large(),
              ...<Widget>[
                _buildFirstNameField(),
                _buildLastNameField(),
                _buildLocationSection(),
                _buildFloorField(),
                _buildDoorCodeField(),
                _buildEmailField(),
              ].separatedBy(() => const VerticalMargin.large()),
              const VerticalMargin.xxxlarge(),
            ],
          ),
        ),
        Text(
          S.of(context).customerDeductionFormHeader,
          style: Theme.of(context).textTheme.titleLarge,
        ),
        Form(
          key: _rotInfoFormKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const VerticalMargin.xxlarge(),
              _buildPersonalNumberField(),
              const VerticalMargin.xxlarge(),
              _buildPropertyTypeField(),
              const VerticalMargin.xxlarge(),
              _buildApartmentOwnerFields(),
              _buildHouseField(),
            ],
          ),
        ),
        if (isCurrentUser)
          if (isSuperUser)
            const AdminInterfaceSwitch()
          else
            const DeleteAccountButton(),
        const VerticalMargin.small(),
      ],
    );
  }

  AppBar _buildAppBar() {
    return AppBar(
      centerTitle: false,
      titleSpacing: -Margins.small,
      title: Text(
        S.of(context).editCompanyInfo,
        style: Theme.of(context).textTheme.headlineSmall,
      ),
      actions: <Widget>[
        Padding(
          padding: const EdgeInsets.only(
            right: Margins.medium,
            bottom: Margins.small,
            top: Margins.small,
          ),
          child: TextButton(
            key: const Key("profilePageSaveButton"),
            style: TextButton.styleFrom(
              foregroundColor: context.doneColors.uiPurple,
              disabledForegroundColor: context.doneColors.uiPurple.withValues(
                alpha: 0.3,
              ),
              textStyle: Theme.of(
                context,
              ).textTheme.labelMedium!.copyWith(fontWeight: FontWeight.bold),
            ),
            child: Text(S.of(context).save),
            onPressed: _hasChanges ? _saveChanges : null,
          ),
        ),
      ],
      elevation: 0,
    );
  }

  Widget _buildPhoneNumber() {
    return customer.phoneNumber != null
        ? Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Text(
              S.of(context).profilePhoneNumber,
              style: Theme.of(context).textTheme.labelMedium!.copyWith(
                color: context.doneColors.typographyLowContrast,
              ),
            ),
            const VerticalMargin.small(),
            Text(
              customer.phoneNumber!,
              style: Theme.of(context).textTheme.titleMedium!.copyWith(
                color: context.doneColors.typographyHightContrast,
              ),
            ),
            const VerticalMargin.xxlarge(),
          ],
        )
        : Container();
  }

  Widget _buildFirstNameField() {
    return DoneFormFieldHolder(
      title: S.of(context).firstName,
      widget: TextFormField(
        textCapitalization: TextCapitalization.words,
        style: Theme.of(
          context,
        ).textTheme.bodyMedium!.apply(color: context.doneColors.uiBlack),
        initialValue: customer.firstName,
        onChanged: (change) {
          if (change == customer.firstName) return;
          setState(() {
            customer.firstName = change;
            _hasChangesOnCommonData = true;
          });
        },
        validator: (value) => FormValidators.required(context, value),
        decoration: DoneFormFieldHolder.inputDecoration(
          textStyle: Theme.of(context).textTheme.bodyLarge,
          context: context,
        ),
      ),
    );
  }

  Widget _buildLastNameField() {
    return DoneFormFieldHolder(
      title: S.of(context).lastName,
      widget: TextFormField(
        textCapitalization: TextCapitalization.words,
        style: Theme.of(
          context,
        ).textTheme.bodyMedium!.apply(color: context.doneColors.uiBlack),
        decoration: DoneFormFieldHolder.inputDecoration(
          textStyle: Theme.of(context).textTheme.bodyLarge,
          context: context,
        ),
        initialValue: customer.lastName,
        onChanged: (change) {
          if (change == customer.lastName) return;
          setState(() {
            customer.lastName = change;
            _hasChangesOnCommonData = true;
          });
        },
      ),
    );
  }

  Widget _buildLocationSection() {
    final address = customer.sensitiveCustomerData!.address;
    final areAddressComponentsNull =
        address?.streetAddress == null ||
        address?.postalCode == null ||
        address?.city == null;
    final formattedAddress =
        areAddressComponentsNull
            ? ''
            : '${address!.streetAddress}, ${address.postalCode} '
                '${address.city}, Sweden';

    return UpdateLocationSection(
      location: customer.location,
      formattedAddress: formattedAddress,
      mapPadding: const EdgeInsets.only(bottom: Margins.medium),
      onLocationChanged: (location) {
        if (location == null) return;

        setState(() {
          customer.sensitiveCustomerData!.address!.streetAddress =
              location.streetAddress;
          customer.sensitiveCustomerData!.address!.postalCode =
              location.postalCode;
          customer.sensitiveCustomerData!.address!.city = location.postalTown;
          customer.sensitiveCustomerData!.address!.serviceArea =
              location.sublocality;

          customer.location?.addressLine = location.streetAddress;

          customer.location?.city = location.postalTown;
          customer.location?.coordinates = location.coordinates;
          customer.location?.subLocality = location.sublocality;

          _hasChangesOnCommonData = true;
        });
      },
    );
  }

  Widget _buildFloorField() {
    return DoneFormFieldHolder(
      title: S.of(context).floor,
      widget: TextFormField(
        style: Theme.of(
          context,
        ).textTheme.bodyMedium!.apply(color: context.doneColors.uiBlack),
        decoration: DoneFormFieldHolder.inputDecoration(
          textStyle: Theme.of(context).textTheme.bodyLarge,
          context: context,
        ),
        keyboardType: TextInputType.number,
        initialValue: customer.sensitiveCustomerData?.address?.floor,
        onChanged: (change) {
          if (change == customer.sensitiveCustomerData?.address?.floor) return;

          setState(() {
            _hasChangesOnCommonData = true;
            customer.sensitiveCustomerData!.address!.floor = change;
          });
        },
      ),
    );
  }

  Widget _buildDoorCodeField() {
    return DoneFormFieldHolder(
      title: S.of(context).doorCode,
      widget: TextFormField(
        style: Theme.of(
          context,
        ).textTheme.bodyMedium!.apply(color: context.doneColors.uiBlack),
        decoration: DoneFormFieldHolder.inputDecoration(
          textStyle: Theme.of(context).textTheme.bodyLarge,
          context: context,
        ),
        keyboardType: TextInputType.number,
        initialValue: customer.sensitiveCustomerData?.address?.doorCode,
        onChanged: (change) {
          if (change == customer.sensitiveCustomerData?.address?.doorCode)
            return;

          setState(() {
            customer.sensitiveCustomerData!.address!.doorCode = change;
            _hasChangesOnCommonData = true;
          });
        },
      ),
    );
  }

  Widget _buildEmailField() {
    return DoneFormFieldHolder(
      title: S.of(context).profileEmail,
      widget: TextFormField(
        key: const Key("emailField"),
        style: Theme.of(
          context,
        ).textTheme.bodyMedium!.apply(color: context.doneColors.uiBlack),
        decoration: DoneFormFieldHolder.inputDecoration(
          textStyle: Theme.of(context).textTheme.bodyLarge,
          context: context,
        ),
        keyboardType: TextInputType.emailAddress,
        initialValue: customer.email,
        onChanged: (change) {
          if (change == customer.email) return;

          setState(() {
            customer.email = change;
            _hasChangesOnCommonData = true;
          });
        },
        validator: FormValidators.compose(context, [
          FormValidators.required,
          FormValidators.email,
        ]),
      ),
    );
  }

  Widget _buildPersonalNumberField() {
    String? formattedInitialValue;
    final storedValue = customer.sensitiveCustomerData?.rotInfo?.personIdNumber;
    if (storedValue != null && storedValue.isNotEmpty) {
      formattedInitialValue = formatPersonalNumber(storedValue);
    }

    return DoneFormFieldHolder(
      title: S.of(context).personalNumber,
      widget: TextFormField(
        style: Theme.of(
          context,
        ).textTheme.bodyMedium!.apply(color: context.doneColors.uiBlack),
        decoration: DoneFormFieldHolder.inputDecoration(
          hintText: "ÅÅÅÅMMDD-XXXX",
          textStyle: Theme.of(context).textTheme.bodyLarge,
          context: context,
        ),
        keyboardType: TextInputType.number,
        initialValue: formattedInitialValue,
        inputFormatters: [
          FilteringTextInputFormatter.digitsOnly,
          LengthLimitingTextInputFormatter(12),
          TextInputFormatter.withFunction((oldValue, newValue) {
            return TextEditingValue(
              text: formatPersonalNumber(newValue.text),
              selection: TextSelection.collapsed(
                offset: formatPersonalNumber(newValue.text).length,
              ),
            );
          }),
        ],
        onChanged: (change) {
          final cleanValue = Personnummer(change).format(true);
          if (cleanValue ==
              customer.sensitiveCustomerData?.rotInfo?.personIdNumber)
            return;

          setState(() {
            _hasChangesOnTaxDeductionData = true;
            customer.sensitiveCustomerData!.rotInfo!.personIdNumber =
                cleanValue;
          });
        },
        validator: (value) {
          final propertyType =
              customer.sensitiveCustomerData?.rotInfo?.propertyType;
          if ((value == null || value.trim().isEmpty) &&
              (propertyType == null ||
                  propertyType == PropertyType.rentedApartment)) {
            return null;
          }

          return FormValidators.compose(context, [
            FormValidators.required,
            FormValidators.personNumber,
          ])(value);
        },
      ),
    );
  }

  Widget _buildPropertyTypeField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(
          S.of(context).typeOfHousing,
          style: Theme.of(context).textTheme.labelMedium!.copyWith(
            color: context.doneColors.typographyLowContrast,
          ),
        ),
        const VerticalMargin.medium(),
        Row(
          children: <Widget>[
            Radio(
              key: const Key("apartmentOwnerRadioButton"),
              value: PropertyType.apartmentOwner,
              groupValue: customer.sensitiveCustomerData?.rotInfo?.propertyType,
              onChanged: _handlePropertyTypeChange,
            ),
            Text(
              S.of(context).apartmentOwner,
              style: Theme.of(context).textTheme.bodyMedium!.apply(
                color: context.doneColors.uiOnPrimary,
              ),
            ),
          ],
        ),
        Row(
          children: <Widget>[
            Radio(
              key: const Key("houseRadioButton"),
              value: PropertyType.house,
              groupValue: customer.sensitiveCustomerData?.rotInfo?.propertyType,
              onChanged: _handlePropertyTypeChange,
            ),
            Text(
              S.of(context).house,
              style: Theme.of(context).textTheme.bodyMedium!.apply(
                color: context.doneColors.uiOnPrimary,
              ),
            ),
          ],
        ),
        Row(
          children: <Widget>[
            Radio(
              key: const Key("rentedApartmentRadioButton"),
              value: PropertyType.rentedApartment,
              groupValue: customer.sensitiveCustomerData?.rotInfo?.propertyType,
              onChanged: _handlePropertyTypeChange,
            ),
            Text(
              S.of(context).rentedApartment,
              style: Theme.of(context).textTheme.bodyMedium!.apply(
                color: context.doneColors.uiOnPrimary,
              ),
            ),
          ],
        ),
        if (customer.sensitiveCustomerData?.rotInfo?.propertyType ==
            PropertyType.rentedApartment)
          Column(
            children: <Widget>[
              Text(S.of(context).notEligibleForTaxDeductionNotice),
              const VerticalMargin(margin: 35),
            ],
          ),
      ],
    );
  }

  Widget _buildPropertyLessThanFiveYearsOld() {
    final isLessThanFiveYearsOld =
        customer.sensitiveCustomerData?.rotInfo?.isLessThanFiveYearsOld ??
        false;
    return Column(
      children: [
        InkWell(
          onTap: () {
            setState(() {
              customer.sensitiveCustomerData?.rotInfo?.isLessThanFiveYearsOld =
                  !isLessThanFiveYearsOld;
              _hasChangesOnTaxDeductionData = true;
            });
          },
          borderRadius: BorderRadius.circular(Margins.verySmall),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                S.of(context).thePropertyIsLessThanFiveYearsOld,
                style: Theme.of(context).textTheme.bodyMedium!.apply(
                  color: context.doneColors.uiOnPrimary,
                ),
              ),
              Switch(
                value: isLessThanFiveYearsOld,
                key: const Key("isLessThanFiveYearsOld"),
                onChanged: (change) {
                  if (change ==
                      customer
                          .sensitiveCustomerData
                          ?.rotInfo
                          ?.isLessThanFiveYearsOld)
                    return;

                  setState(() {
                    _hasChangesOnTaxDeductionData = true;
                    customer
                        .sensitiveCustomerData
                        ?.rotInfo
                        ?.isLessThanFiveYearsOld = change;
                  });
                },
              ),
            ],
          ),
        ),
        if (isLessThanFiveYearsOld)
          Column(
            children: [
              const VerticalMargin.large(),
              Row(
                children: [
                  Icon(
                    Icons.warning_rounded,
                    color: context.doneColors.uiBlack,
                  ),
                  const HorizontalMargin.large(),
                  Expanded(
                    child: Text(
                      S.of(context).thePropertyIsLessThanFiveYearsOldWarning,
                      style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
      ],
    );
  }

  Widget _buildApartmentOwnerFields() {
    final isApartmentOwner =
        customer.sensitiveCustomerData?.rotInfo?.propertyType ==
        PropertyType.apartmentOwner;
    return Visibility(
      visible: isApartmentOwner,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          DoneFormFieldHolder(
            title: S.of(context).housingCooperative,
            widget: TextFormField(
              key: const Key("housingCooperativeField"),
              style: Theme.of(
                context,
              ).textTheme.bodyMedium!.apply(color: context.doneColors.uiBlack),
              decoration: DoneFormFieldHolder.inputDecoration(
                hintText: "XXXXXX-XXXX",
                textStyle: Theme.of(context).textTheme.bodyLarge,
                context: context,
              ),
              initialValue:
                  customer.sensitiveCustomerData?.rotInfo?.housingCooperative,
              onChanged: (change) {
                if (change ==
                    customer.sensitiveCustomerData?.rotInfo?.housingCooperative)
                  return;

                setState(() {
                  _hasChangesOnTaxDeductionData = true;
                  customer.sensitiveCustomerData?.rotInfo?.housingCooperative =
                      change;
                });
              },
              validator:
                  (value) => AuthFormValidators.validateHousingCooperative(
                    context,
                    value,
                    customer,
                  ),
            ),
          ),
          const VerticalMargin.small(),
          _RotInfoMarkdown(data: S.of(context).customerProfileBRFTooltip),
          const VerticalMargin.xlarge(),
          DoneFormFieldHolder(
            title: S.of(context).apartmentNumber,
            widget: TextFormField(
              key: const Key("apartmentNumberField"),
              style: Theme.of(
                context,
              ).textTheme.bodyMedium!.apply(color: context.doneColors.uiBlack),
              decoration: DoneFormFieldHolder.inputDecoration(
                hintText: "1001",
                textStyle: Theme.of(context).textTheme.bodyLarge,
                context: context,
              ),
              inputFormatters: const [],
              keyboardType: TextInputType.number,
              validator: (value) => FormValidators.required(context, value),
              initialValue:
                  customer.sensitiveCustomerData?.rotInfo?.apartmentNumber,
              onChanged: (change) {
                if (change ==
                    customer.sensitiveCustomerData?.rotInfo?.apartmentNumber)
                  return;

                setState(() {
                  _hasChangesOnTaxDeductionData = true;
                  customer.sensitiveCustomerData?.rotInfo?.apartmentNumber =
                      change;
                });
              },
            ),
          ),
          const VerticalMargin.small(),
          Padding(
            padding: const EdgeInsets.only(right: 32),
            child: Text(
              S.of(context).customerProfileApartmentNumberTooltip,
              textHeightBehavior: const TextHeightBehavior(
                applyHeightToFirstAscent: false,
                applyHeightToLastDescent: false,
              ),
              style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                height: 1.6,
                color: context.doneColors.typographyHightContrast,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHouseField() {
    final isHouse =
        customer.sensitiveCustomerData?.rotInfo?.propertyType ==
        PropertyType.house;

    return Visibility(
      visible: isHouse,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          DoneFormFieldHolder(
            title: S.of(context).propertyDesignation,
            widget: TextFormField(
              key: const Key("propertyDesignationField"),
              textCapitalization: TextCapitalization.sentences,
              style: Theme.of(
                context,
              ).textTheme.bodyMedium!.apply(color: context.doneColors.uiBlack),
              decoration: DoneFormFieldHolder.inputDecoration(
                textStyle: Theme.of(context).textTheme.bodyLarge,
                context: context,
              ),
              initialValue:
                  customer.sensitiveCustomerData?.rotInfo?.propertyDesignation,
              onChanged: (change) {
                if (change ==
                    customer
                        .sensitiveCustomerData
                        ?.rotInfo
                        ?.propertyDesignation)
                  return;

                setState(() {
                  customer.sensitiveCustomerData?.rotInfo?.propertyDesignation =
                      change;
                  _hasChangesOnTaxDeductionData = true;
                });
              },
              validator:
                  (value) => AuthFormValidators.validatePropertyDesignation(
                    context,
                    value,
                    customer,
                  ),
            ),
          ),
          const VerticalMargin.small(),
          _RotInfoMarkdown(
            data: S.of(context).customerProfilePropertyDesignationTooltip,
          ),
          const VerticalMargin.xxlarge(),
          _buildPropertyLessThanFiveYearsOld(),
          const VerticalMargin.xxlarge(),
        ],
      ),
    );
  }

  Future<void> _saveChanges() async {
    if (_hasChangesOnCommonData &&
        !_personalInfoFormKey.currentState!.validate())
      return;
    if (_hasChangesOnTaxDeductionData &&
        !_rotInfoFormKey.currentState!.validate())
      return;

    // Saving data to user document by default
    await Mutations.instance
        .customer(customer.documentReference)
        .updateInfo(
          customer.toMap(),
          sensitiveCustomerData: customer.sensitiveCustomerData?.toMap(),
        );

    if (mounted) {
      setState(() {
        _hasChangesOnCommonData = false;
        _hasChangesOnTaxDeductionData = false;
      });
      Navigator.of(context).pop();
    }
  }

  void _handlePropertyTypeChange(PropertyType? change) {
    if (change == customer.sensitiveCustomerData?.rotInfo?.propertyType) return;
    setState(() {
      customer.sensitiveCustomerData?.rotInfo?.propertyType = change;
      _hasChangesOnTaxDeductionData = true;
    });
  }

  void _createSensitiveDataIfNull() {
    customer.sensitiveCustomerData ??= SensitiveCustomerData();
    customer.sensitiveCustomerData!.address ??= DoneAddress();
    customer.sensitiveCustomerData!.rotInfo ??= CustomerRotInfo();
  }
}

class _RotInfoMarkdown extends StatelessWidget {
  const _RotInfoMarkdown({required this.data});
  final String data;

  @override
  Widget build(BuildContext context) {
    return Markdown(
      data: data,
      shrinkWrap: true,
      styleSheet: MarkdownStyleSheet(
        p: Theme.of(context).textTheme.bodyMedium,
        a: Theme.of(
          context,
        ).textTheme.labelMedium!.apply(color: context.doneColors.purple),
      ),
      padding: const EdgeInsets.only(right: 32),
      onTapLink:
          (_, url, __) => URLRouter.instance.handleUrl(url, context: context),
    );
  }
}
