import 'package:done_auth/done_auth.dart';
import 'package:done_database/done_database.dart';
import 'package:done_flutter/core/router/routes/routes_definitions.dart';
import 'package:done_flutter/utils/extensions/enums/property_type.dart';
import 'package:done_maps/done_maps.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:done_localizations/done_localizations.dart';

class CustomerInfoPage extends StatelessWidget {
  const CustomerInfoPage({required this.customerId, this.customer});

  final String customerId;

  /// Cached customer data with `sensitiveCustomerData` included
  final User? customer;

  @override
  Widget build(BuildContext context) {
    final isCurrentUser = context.authState.user?.id == customerId;
    final title =
        isCurrentUser ? S.of(context).yourProfile : S.of(context).customer;
    return Scaffold(
      appBar: AppBar(
        titleSpacing: -8,
        centerTitle: false,
        title: Text(title),
        elevation: 0,
        actions:
            isCurrentUser
                ? [
                  DoneAppBarActionButton(
                    title: Text(
                      S.of(context).editCompanyInfo,
                      style: Theme.of(context).textTheme.bodyMedium!.apply(
                        color: context.doneColors.purple,
                      ),
                    ),
                    onPressed: () => const EditProfileRoute().navigate(context),
                  ),
                ]
                : null,
      ),
      body:
          (customer != null)
              // customer is not null and this is cached data with sensitive info, show directly
              ? _CustomerInfo(customer: customer!)
              // fetch sensitive customer data before showing live customer data from streams
              : _FetchCustomerInfo(customerId: customerId),
    );
  }
}

/// Tries to fetch customer sensitive data
class _FetchCustomerInfo extends StatelessWidget {
  const _FetchCustomerInfo({required this.customerId});

  final String customerId;

  @override
  Widget build(BuildContext context) {
    final userRepo = GetIt.instance<UserRepository>();

    return StreamBuilder<User>(
      stream: userRepo.user(customerId),
      builder: (context, userSnapshot) {
        final customer = userSnapshot.data;
        if (customer == null) return CenteredProgressIndicator();
        return StreamBuilder<SensitiveCustomerData>(
          stream: userRepo.sensitiveCustomerData(customer.id),
          builder: (context, sensitiveInfoSnapshot) {
            //* we get permission denied error if company isn't in trustedCompanies array
            if (!sensitiveInfoSnapshot.hasData &&
                !sensitiveInfoSnapshot.hasError)
              return CenteredProgressIndicator();

            if (sensitiveInfoSnapshot.hasData &&
                !sensitiveInfoSnapshot.hasError) {
              //* the company is in trustedCompanies array, we can extract sensitive data
              customer.sensitiveCustomerData = sensitiveInfoSnapshot.data;
            }

            return _CustomerInfo(customer: customer);
          },
        );
      },
    );
  }
}

/// displays public user info
class _CustomerInfo extends StatelessWidget {
  const _CustomerInfo({required this.customer});

  final User customer;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isUltraWideLayout =
            constraints.maxWidth > Layouts.ultraWideLayout;

        return SafeArea(
          child: Padding(
            padding: const EdgeInsets.only(top: 16),
            child: Scrollbar(
              child:
                  isUltraWideLayout
                      ? _buildUltraWideLayout(context)
                      : _buildNormalLayout(context),
            ),
          ),
        );
      },
    );
  }

  Widget _buildNormalLayout(BuildContext context) {
    return ListView(
      children: <Widget>[
        _buildUserInformationsColumn(context),
        const VerticalMargin(margin: 30),
      ],
    );
  }

  Widget _buildUltraWideLayout(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Expanded(
          child: _buildUserInformationsColumn(context, isUltraWideLayout: true),
        ),
        Expanded(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 50),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: _addressInfoSection(
                context: context,
                customer: customer,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildUserInformationsColumn(
    BuildContext context, {
    bool isUltraWideLayout = false,
  }) {
    final isCurrentUser = context.authState.user?.id == customer.id;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: <Widget>[
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: Margins.xlarge),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ..._basicInfoSection(context: context, customer: customer),
              if (isCurrentUser) const VerticalMargin.medium(),
              if (isCurrentUser)
                ..._rotInfoSection(context: context, customer: customer),
              const VerticalMargin.medium(),
              if (!isUltraWideLayout)
                ..._addressInfoSection(context: context, customer: customer),
            ],
          ),
        ),
      ].separatedBy(() => const VerticalMargin.large()),
    );
  }

  List<Widget> _basicInfoSection({
    required BuildContext context,
    required User customer,
  }) {
    return <Widget>[
      LabeledValue(
        label: S.of(context).profileName,
        value: customer.fullName,
        showCopyButton: true,
      ),
      LabeledValue(
        label: S.of(context).profilePhoneNumber,
        value: customer.phoneNumber,
        showCopyButton: true,
      ),
      LabeledValue(
        label: S.of(context).profileEmail,
        value: customer.email,
        showCopyButton: true,
      ),
    ].separatedBy(() => Divider(color: context.doneColors.uiBg1));
  }

  List<Widget> _rotInfoSection({
    required BuildContext context,
    required User customer,
  }) {
    return <Widget>[
      Text(
        S.of(context).customerDeductionFormHeader,
        style: Theme.of(context).textTheme.headlineSmall,
      ),
      const VerticalMargin.medium(),
      ...<Widget>[
        if (customer.sensitiveCustomerData?.rotInfo?.personIdNumber != null)
          LabeledValue(
            label: S.of(context).personalNumber,
            value: formatPersonalNumber(
              customer.sensitiveCustomerData!.rotInfo!.personIdNumber!,
            ),
            showCopyButton: true,
          ),
        if (customer.sensitiveCustomerData?.rotInfo?.propertyType != null)
          LabeledValue(
            label: S.of(context).typeOfHousing,
            value: customer.sensitiveCustomerData?.rotInfo?.propertyType?.title(
              context,
            ),
          ),
        if (customer.sensitiveCustomerData?.rotInfo?.housingCooperative != null)
          LabeledValue(
            label: S.of(context).housingCooperative,
            value: customer.sensitiveCustomerData?.rotInfo?.housingCooperative,
            showCopyButton: true,
          ),
        if (customer.sensitiveCustomerData?.rotInfo?.apartmentNumber != null)
          LabeledValue(
            label: S.of(context).apartmentNumber,
            value: customer.sensitiveCustomerData?.rotInfo?.apartmentNumber,
            showCopyButton: true,
          ),
        if (customer.sensitiveCustomerData?.rotInfo?.propertyDesignation !=
            null)
          LabeledValue(
            label: S.of(context).propertyDesignation,
            value: customer.sensitiveCustomerData?.rotInfo?.propertyDesignation,
            showCopyButton: true,
          ),
        if (customer.sensitiveCustomerData?.rotInfo?.isLessThanFiveYearsOld !=
                null &&
            customer.sensitiveCustomerData?.rotInfo?.propertyType ==
                PropertyType.house)
          LabeledValue(
            label: S.of(context).thePropertyIsLessThanFiveYearsOld,
            value:
                (customer
                            .sensitiveCustomerData
                            ?.rotInfo
                            ?.isLessThanFiveYearsOld ??
                        false)
                    ? S.of(context).yes
                    : S.of(context).no,
          ),
      ].separatedBy(() => Divider(color: context.doneColors.uiBg1)),
    ];
  }

  List<Widget> _addressInfoSection({
    required BuildContext context,
    required User customer,
  }) {
    return <Widget>[
      Text(
        S.of(context).address,
        style: Theme.of(context).textTheme.headlineSmall,
      ),
      const VerticalMargin.medium(),
      ...<Widget>[
        if ((customer.sensitiveCustomerData?.address?.streetAddress) != null)
          LabeledValue(
            label: S.of(context).streetAddress,
            value: customer.sensitiveCustomerData?.address?.streetAddress,
            showCopyButton: true,
          ),
        if (customer.sensitiveCustomerData?.address?.floor != null)
          LabeledValue(
            label: S.of(context).floor,
            value: customer.sensitiveCustomerData?.address?.floor,
          ),
        if (customer.sensitiveCustomerData?.address?.doorCode != null)
          LabeledValue(
            label: S.of(context).doorCode,
            value: customer.sensitiveCustomerData?.address?.doorCode,
          ),
        if (customer.sensitiveCustomerData?.address?.postalCode != null)
          LabeledValue(
            label: S.of(context).profilePostCode,
            value: customer.sensitiveCustomerData?.address?.postalCode,
          ),
        if ((customer.location?.city ??
                customer.sensitiveCustomerData?.address?.city) !=
            null)
          LabeledValue(
            label: S.of(context).postalTown,
            value:
                customer.location?.city ??
                customer.sensitiveCustomerData?.address?.city,
          ),
        if (customer.location != null)
          ClipRRect(
            borderRadius: BorderRadius.circular(Margins.medium),
            child: UserMap(height: 200, location: customer.location),
          ),
      ].separatedBy(() => Divider(color: context.doneColors.uiBg1)),
    ];
  }
}
