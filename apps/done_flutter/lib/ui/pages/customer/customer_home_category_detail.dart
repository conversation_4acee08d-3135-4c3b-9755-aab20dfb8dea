import 'package:auto_size_text/auto_size_text.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:done_flutter/core/blocs/admin_configuration_cubit/admin_configuration_cubit.dart';
import 'package:done_database/done_database.dart';

import 'package:done_flutter/ui/widgets/cards/company_review_card.dart';
import 'package:done_booking/done_booking.dart';
import 'package:done_flutter/utils/first_build_callback.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_flutter/ui/pages/customer/fixed_price/fixed_price_list_item.dart';
import 'package:done_flutter/ui/widgets/customer_content_cards.dart';

import 'package:done_flutter/utils/extensions/string_extensions.dart';
import 'package:done_models/done_models.dart';
import 'package:done_router/done_router.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:get_it/get_it.dart';
import 'package:logger/logger.dart';

class CustomerHomeCategoryDetail extends StatelessWidget {
  const CustomerHomeCategoryDetail({super.key, required this.service});

  final ServiceType service;

  @override
  Widget build(BuildContext context) {
    // TODO(ayman): Extract into database
    final serviceDocument = FirebaseFirestore.instance
        .collection('admin')
        .doc('customerHome')
        .collection('jobCategories')
        .doc(service.id);
    final bookingConfigCubit = BlocProvider.of<BookingConfigCubit>(context);

    return PopScope(
      onPopInvokedWithResult: (bool didPop, Object? result) async {
        if (didPop) {
          bookingConfigCubit.clear();
        }
      },
      child: FirstBuildCallback(
        onFirstBuild:
            () async => bookingConfigCubit.setConfig(
              BookingConfig(
                service: service,
                adminConfiguration:
                    BlocProvider.of<AdminConfigurationCubit>(context).state,
                onBookingSuccess: (context, jobId) {
                  bookingConfigCubit.clear();
                  // Go to project details page. By using `go`, we will also close the booking modal
                  GoRouter.of(context).go('/projects/$jobId');
                },
              ),
            ),
        child: Scaffold(
          body: Scrollbar(
            child: SafeArea(
              child: Column(
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Stack(
                        children: [
                          Align(
                            alignment: Alignment.bottomRight,
                            child: Padding(
                              padding: const EdgeInsets.only(top: 28),
                              child: Image.asset(
                                service.image,
                                alignment: Alignment.bottomRight,
                                height: 64,
                              ),
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 20,
                            ),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: <Widget>[
                                BackButton(
                                  color: context.doneColors.uiBlack,
                                  onPressed: () {
                                    Navigator.pop(context);
                                  },
                                ),
                                const HorizontalMargin.small(),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      AutoSizeText(
                                        service.title(context),
                                        style:
                                            Theme.of(
                                              context,
                                            ).textTheme.headlineSmall,
                                        maxLines: 1,
                                      ),
                                      const VerticalMargin.verySmall(),
                                      AutoSizeText(
                                        service.subtitle(context),
                                        style: Theme.of(
                                          context,
                                        ).textTheme.bodyMedium!.apply(
                                          color:
                                              context
                                                  .doneColors
                                                  .typographyMediumContrast,
                                        ),
                                        maxLines: 1,
                                      ),
                                    ],
                                  ),
                                ),
                                const HorizontalMargin(margin: 100),
                              ],
                            ),
                          ),
                        ],
                      ),
                      Container(height: 4, color: context.doneColors.purple),
                    ],
                  ),
                  Expanded(
                    child: PageContent(
                      child: ListView(
                        shrinkWrap: true,
                        children: [
                          StreamBuilder<DocumentSnapshot<Map<String, dynamic>>>(
                            // TODO(ayman): refactor into model
                            stream: serviceDocument.snapshots(),
                            builder: (context, snapshot) {
                              if (!snapshot.hasData)
                                return CenteredProgressIndicator();
                              if (!snapshot.data!.exists ||
                                  snapshot.data!.data() == null) {
                                GetIt.instance<Logger>().w(
                                  "Job category not found: ${service.id}",
                                );
                                return Container();
                              }
                              final data = snapshot.data!.data()!;

                              return Column(
                                children: [
                                  const VerticalMargin.large(),
                                  _buildBookVideoCall(context, data),
                                  _buildReviews(
                                    context,
                                    service.customerReviewIds,
                                  ),
                                  _buildFixedPriceList(context),
                                  Padding(
                                    padding: const EdgeInsets.all(20),
                                    child: ContentList(
                                      sections: data['sections'] as List,
                                    ),
                                  ),
                                ],
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildReviews(BuildContext context, List<String> services) {
    return StreamBuilder<List<CompanyReview>>(
      stream: GetIt.instance<BookingRepository>().servicesRatings(
        services: services,
        limit: 15,
      ),
      builder: (context, reviewsSnapshot) {
        final companyReviews = reviewsSnapshot.data ?? [];

        if (companyReviews.isEmpty) return const SizedBox();

        return Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          mainAxisSize: MainAxisSize.min,
          children: [
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
              child: Text(
                S.of(context).reviews,
                style: Theme.of(context).textTheme.titleLarge,
              ),
            ),
            const VerticalMargin.medium(),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  const HorizontalMargin(margin: Margins.xlarge),
                  ...companyReviews.map(
                    (review) => CompanyReviewCard(review: review),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildBookVideoCall(BuildContext context, Map<String, dynamic> data) {
    final languageCode =
        Localizations.localeOf(context).languageCode.capitalize();
    final config = GetIt.instance<BookingRemoteConfigService>().serviceConfig(
      service,
    );

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          MultilineMarkdownBody(
            data:
                config?.description ??
                data['bodyMarkdown$languageCode'] as String,
            styleSheet: MarkdownStyleSheet(
              p: Theme.of(context).textTheme.bodyLarge,
              strong: Theme.of(
                context,
              ).textTheme.labelMedium!.apply(color: context.doneColors.uiBlack),
              a: Theme.of(
                context,
              ).textTheme.bodyLarge!.apply(color: context.doneColors.purple),
            ),
          ),
          const VerticalMargin.xxlarge(),
          DoneButton(
            key: const Key('BookingStartCtaButton'),
            style: DoneButtonStyle.secondary,
            title: Text(service.ctaText(context)),
            image: service.ctaIcon(context),
            onPressed:
                () => {
                  bookVideoCall(context, source: 'app_category_${service.id}'),
                },
          ),
          const VerticalMargin.large(),
        ],
      ),
    );
  }

  Widget _buildFixedPriceList(BuildContext context) {
    return StreamBuilder<List<FixedPriceJob>>(
      stream: GetIt.instance<FixedPriceJobsRepository>().serviceFixedPriceJobs(
        service.id,
      ),
      builder: (context, jobs) {
        if (!jobs.hasData || jobs.data!.isEmpty) return Container();

        final List<Widget> fixedPriceJobsList =
            jobs.data!
                .map(
                  (job) => FixedPriceListItem(
                    fixedPriceJob: job,
                    onTap: (fixedPriceJob, _) {
                      final params = FixedPriceJobDetailsRouteParams(
                        id: fixedPriceJob.fixedPriceJobId,
                        showStartCost: true,
                      );
                      FixedPriceJobDetailsRoute(
                        params: params,
                      ).navigate(context);
                    },
                  ),
                )
                .toList();

        return Column(
          key: const Key("FixedPriceColumn"),
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
              child: Text(
                S.of(context).fixedPriceHeaderSubtitle,
                style: Theme.of(context).textTheme.titleLarge,
              ),
            ),
            const VerticalMargin.medium(),
            ...fixedPriceJobsList.separatedBy(
              () => const Divider(indent: 80, height: Margins.small),
            ),
            const VerticalMargin.large(),
            Center(
              child: Text(
                S.of(context).fixedPriceJobsListMoreTitle,
                style: Theme.of(context).textTheme.labelMedium,
              ),
            ),
            Center(child: Text(S.of(context).fixedPriceJobsListMoreMessage)),
            const VerticalMargin.large(),
          ],
        );
      },
    );
  }
}

void showBookingUnavailableDialog({required BuildContext context}) {
  showDialog<void>(
    context: context,
    builder:
        (BuildContext context) => DoneDialog(
          title: Text(
            S.of(context).videoCallBookingUnavailable,
            style: Theme.of(context).textTheme.bodyLarge,
          ),
          content: [
            const VerticalMargin.large(),
            DoneButton(
              style: DoneButtonStyle.neutral,
              title: Text(S.of(context).dismiss),
              onPressed: () async {
                Navigator.of(context).pop();
              },
            ),
          ],
        ),
  );
}
