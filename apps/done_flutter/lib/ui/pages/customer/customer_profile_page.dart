import 'package:done_auth/done_auth.dart';
import 'package:done_database/done_database.dart';
import 'package:done_deductions/done_deductions.dart';
import 'package:done_flutter/core/configuration/done_urls.dart';
import 'package:done_flutter/core/lib/logger_config.dart';
import 'package:done_flutter/core/router/routes/routes_definitions.dart';
import 'package:done_flutter/core/services/intercom_service.dart';
import 'package:done_flutter/ui/pages/customer/anonymous_changes_builder.dart';
import 'package:done_flutter/ui/pages/customer/profile/widgets/edit_profile_icon_button.dart';
import 'package:done_flutter/ui/widgets/app_version_display.dart';
import 'package:done_flutter/ui/widgets/buttons/log_in_action_button.dart';
import 'package:done_flutter/ui/widgets/buttons/sign_out_button.dart';
import 'package:done_flutter/ui/widgets/streamed_count_badge.dart';
import 'package:done_flutter/ui/widgets/user_avatar.dart';
import 'package:done_flutter/utils/helpers/launch_url.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:intercom_flutter/intercom_flutter.dart';

class CustomerProfilePage extends StatelessWidget {
  const CustomerProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: false,
        title: Text(
          S.of(context).profile,
          style: Theme.of(context).textTheme.headlineMedium,
        ),
        automaticallyImplyLeading: false,
        elevation: 0,
      ),
      extendBody: true,
      body: const Scrollbar(child: PageContent(child: _CustomerProfileView())),
    );
  }
}

class _CustomerProfileView extends StatelessWidget {
  const _CustomerProfileView();

  @override
  Widget build(BuildContext context) {
    final customer = context.authState.user!;
    final bottomPadding = MediaQuery.of(context).padding.bottom;

    return SingleChildScrollView(
      padding: EdgeInsets.only(
        bottom: kBottomNavigationBarHeight + bottomPadding,
      ),
      child: AnonymousChangesBuilder(
        builder: (context, isAnonymous, _) {
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: Margins.large),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                if (isAnonymous)
                  const _AnonymousUserProfileView()
                else
                  const _CustomerInfoTile(),
                _CustomerProfileSection(
                  title: Text(
                    S.of(context).taxDeduction,
                    style: Theme.of(context).textTheme.bodyMedium!.apply(
                      color: context.doneColors.uiOnPrimary,
                    ),
                  ),
                  children: [
                    const VerticalMargin.medium(),
                    _ProfileDeductionsCard(customer: customer),
                  ],
                ),
                _CustomerProfileSection(
                  title: Text(
                    S.of(context).profileSupportSectionTitle,
                    style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                      color: context.doneColors.uiOnPrimary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  children: [
                    _CustomerProfileSectionItem(
                      title: Text(
                        S.of(context).commonQuestions,
                        style: Theme.of(context).textTheme.bodyMedium!.apply(
                          color: context.doneColors.uiOnPrimary,
                        ),
                      ),
                      onTap: () => launchUrl(DoneUrls.faq),
                    ),
                    _CustomerProfileSectionItem(
                      title: Text(
                        S.of(context).helpCenter,
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                      trailingPrefix: StreamedCountBadge(
                        stream: GetIt.instance<IntercomHelper>().unreadCount,
                        tab: 3,
                      ),
                      onTap: () {
                        Intercom.instance.displayMessenger();
                      },
                    ),
                  ],
                ),
                _CustomerProfileSection(
                  title: Text(
                    S.of(context).profileLegalSectionTitle,
                    style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                      color: context.doneColors.uiOnPrimary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  children: [
                    _CustomerProfileSectionItem(
                      title: Text(
                        S.of(context).tosTitle,
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                      onTap: () => launchUrl(DoneUrls.termsOfUse),
                    ),
                    _CustomerProfileSectionItem(
                      title: Text(
                        S.of(context).privacyPolicy,
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                      onTap: () => launchUrl(DoneUrls.privacyPolicy),
                    ),
                  ],
                ),
                if (!isAnonymous) const SignOutButton(),
                // Only let super users share logs for now. Maybe can let more users do it later.
                if (!kIsWeb && context.authState.isSuperUser())
                  const DoneButton(
                    onPressed: shareLogFiles,
                    title: Text('Share Logs'),
                  ),
                const AppVersionDisplay(),
              ].separatedBy(() => const VerticalMargin.xxlarge()),
            ),
          );
        },
      ),
    );
  }
}

class _AnonymousUserProfileView extends StatelessWidget {
  const _AnonymousUserProfileView();

  @override
  Widget build(BuildContext context) {
    return EmptyState(
      isDecorated: true,
      padding: EdgeInsets.zero,
      title: Text(S.of(context).notLoggedIn),
      description: Text(S.of(context).customerProfileEmptyStateDescription),
      children: const [LoginActionButton()],
    );
  }
}

class _CustomerInfoTile extends StatelessWidget {
  const _CustomerInfoTile();

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthCubit, AuthState>(
      builder: (context, state) {
        final customer = state.user;
        if (customer == null) return const SizedBox.shrink();
        return ContentCard(
          title: customer.fullName,
          subTitle: Text(
            S.of(context).yourProfile,
            style: Theme.of(context).textTheme.bodyMedium!.apply(
              color: context.doneColors.uiOnPrimary,
            ),
          ),
          height: 60,
          icon: null,
          leading: NonFetchingUserAvatar(user: customer, radius: 22),
          trailingPrefix: const EditProfileIconButton(),
          iconColor: context.doneColors.uiBg3,
          onTap: () => const ViewCustomerProfileRoute().navigate(context),
        );
      },
    );
  }
}

class _ProfileDeductionsCard extends StatelessWidget {
  const _ProfileDeductionsCard({required this.customer});
  final User customer;
  @override
  Widget build(BuildContext context) {
    return StreamBuilder<SensitiveCustomerData>(
      stream: GetIt.instance<UserRepository>().sensitiveCustomerData(
        customer.documentReference.id,
      ),
      builder: (context, snapshot) {
        final currentBalance = snapshot.data?.deductionBalance?.current.balance;

        final hasBalance = currentBalance != null;
        final title =
            hasBalance
                ? S.of(context).repairsUnused
                : S.of(context).profileBalance;
        return ContentCard(
          title: title,
          titleStyle:
              hasBalance ? Theme.of(context).textTheme.bodyMedium : null,
          subTitle: _buildSubtitle(context, snapshot.data?.deductionBalance),
          height: 60,
          iconColor: context.doneColors.uiBg3,
          onTap: () => const CheckDeductionsBalanceRoute().push(context),
        );
      },
    );
  }

  Widget? _buildSubtitle(BuildContext context, DeductionBalance? balance) {
    final currentBalance = balance?.current.balance;

    if (balance?.lastUpdated == null || currentBalance == null) return null;
    final availableDeductions =
        currentBalance.availableRotRut + currentBalance.availableGreenTech;

    final formattedLastUpdated = TimeFormatter.toDayMonthHourMinuteFormat(
      balance!.lastUpdated.toDate(),
    );
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Text(
          getPriceString(availableDeductions),
          style: Theme.of(context).textTheme.headlineSmall,
        ),
        Text(
          S.of(context).repairsRetrieved(formattedLastUpdated),
          style: Theme.of(context).textTheme.bodySmall!.copyWith(
            color: context.doneColors.typographyLowContrast,
          ),
        ),
      ],
    );
  }
}

class _CustomerProfileSection extends StatelessWidget {
  const _CustomerProfileSection({required this.title, required this.children});
  final Widget title;
  final List<Widget> children;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        DefaultTextStyle(
          style: Theme.of(context).textTheme.titleLarge!,
          child: title,
        ),
        const VerticalMargin.verySmall(),
        ...children,
      ],
    );
  }
}

class _CustomerProfileSectionItem extends StatelessWidget {
  const _CustomerProfileSectionItem({
    required this.title,
    required this.onTap,
    this.trailingPrefix,
  });
  final Widget title;
  final VoidCallback onTap;
  final Widget? trailingPrefix;

  @override
  Widget build(BuildContext context) {
    return ListTile(
      contentPadding: EdgeInsets.zero,
      title: DefaultTextStyle(
        style: Theme.of(context).textTheme.titleMedium!,
        child: title,
      ),
      onTap: onTap,
      dense: true,
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (trailingPrefix != null) ...[
            trailingPrefix!,
            const HorizontalMargin.large(),
          ],
          Icon(Icons.keyboard_arrow_right, color: context.doneColors.uiBg3),
        ],
      ),
    );
  }
}
