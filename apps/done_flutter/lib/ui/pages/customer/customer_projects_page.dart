import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:done_auth/done_auth.dart';
import 'package:done_calls/done_calls.dart';
import 'package:done_database/done_database.dart';
import 'package:done_flutter/core/blocs/master_detail_cubit/home_selection_cubit.dart';
import 'package:done_flutter/core/blocs/session_configuration_cubit/session_configuration_cubit.dart';
import 'package:done_flutter/core/models/navigation_bar.dart';
import 'package:done_flutter/core/router/routes/routes_definitions.dart';
import 'package:done_flutter/ui/pages/call/call_permissions_dialog.dart';
import 'package:done_flutter/ui/pages/customer/anonymous_changes_builder.dart';
import 'package:done_flutter/ui/pages/job/job_detail_page.dart';
import 'package:done_flutter/ui/pages/master_detail/layout_adaptive_page.dart';
import 'package:done_flutter/ui/widgets/buttons/log_in_action_button.dart';
import 'package:done_flutter/ui/widgets/jobs/job_card.dart';
import 'package:done_flutter/ui/widgets/jobs/previous_project_card.dart';
import 'package:done_flutter/ui/widgets/progress/progress_customer_precheck.dart';
import 'package:done_image/done_image.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_models/done_models.dart';
import 'package:done_router/done_router.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

class CustomerProjectsPage extends StatelessWidget {
  const CustomerProjectsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return AnonymousChangesBuilder(
      builder: (context, isAnonymous, _) {
        return LayoutAdaptivePage<HomeSelectionCubit, Map<int, String?>>(
          stateExtractor: (state) => state[CustomerHomeTabs.projects.index],
          appBar: AppBar(
            centerTitle: false,
            title: Text(
              S.of(context).projectsTabTitle,
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            automaticallyImplyLeading: false,
            elevation: 0,
          ),
          masterViewBuilder:
              (newContext) => _CustomerProjectsView(
                onTap:
                    (id) => newContext
                        .read<HomeSelectionCubit>()
                        .updateSelectionFor(CustomerHomeTabs.projects, id),
              ),
          detailViewBuilder: (context, id) {
            if (id != null) {
              final job =
                  FirebaseFirestore.instance
                      .collection('jobs')
                      .doc(id)
                      .withJobConverter;
              return JobDetailPage(jobRef: job);
            }
            return DetailViewPlaceholder(asset: ImageAssets.projects);
          },
          child:
              isAnonymous
                  ? const _AnonymousUserProjectsView()
                  : _CustomerProjectsView(
                    onTap: (jobId) => GoRouter.of(context).go('./$jobId'),
                  ),
        );
      },
    );
  }
}

class _CustomerProjectsView extends StatelessWidget {
  const _CustomerProjectsView({required this.onTap});
  final ValueChanged<String> onTap;

  @override
  Widget build(BuildContext context) {
    return Scrollbar(
      child: SingleChildScrollView(
        child: Column(
          children: [
            CustomerOngoingProjectsSection(onTap: onTap),
            _CustomerPreviousProjectsSection(onTap: onTap),
          ].separatedBy(() => const VerticalMargin.large()),
        ),
      ),
    );
  }
}

class CustomerOngoingProjectsSection extends StatelessWidget {
  const CustomerOngoingProjectsSection({
    required this.onTap,
    this.header,
    this.showEmptyState = true,
    super.key,
  });

  final ValueChanged<String> onTap;
  final Widget? header;
  final bool showEmptyState;

  @override
  Widget build(BuildContext context) {
    final authState = context.authState;
    if (authState.isAnonymous)
      return const Padding(
        padding: EdgeInsets.symmetric(horizontal: Margins.large),
        child: _AnonymousUserProjectsView(),
      );
    final userRef = authState.user?.documentReference;
    if (userRef == null) return CenteredProgressIndicator();

    return StreamBuilder(
      stream: GetIt.instance<JobsRepository>().customerOngoingJobs(userRef.id),
      builder: (context, projectsSnapshot) {
        if (!projectsSnapshot.hasData) return CenteredProgressIndicator();
        final projects = projectsSnapshot.data!;
        if (projects.isEmpty) {
          if (!showEmptyState) return const SizedBox.shrink();
          return Padding(
            padding: const EdgeInsets.only(top: Margins.large),
            child: EmptyState(
              isDecorated: true,
              title: Text(S.of(context).customerProjectsEmptyStateTitle),
              description: Text(
                S.of(context).customerProjectsEmptyStateDescription,
              ),
              children: [
                DoneButton(
                  title: Text(S.of(context).getFreeQuote),
                  onPressed: () => const RootRoute().navigate(context),
                ),
              ],
            ),
          );
        }
        final precheckProjects = projects.where((p) => p.requiresPrechecks);
        final sessionCubit = BlocProvider.of<SessionConfigurationCubit>(
          context,
        );
        if (!sessionCubit.state.didPromptCriticalActionDialog) {
          sessionCubit.markCriticalActionDialogAsPrompted();
          _promptCriticalDialogs(context, precheckProjects);
        }

        return Padding(
          padding: const EdgeInsets.only(top: Margins.large),
          child: ListView.builder(
            shrinkWrap: true,
            padding: EdgeInsets.zero,
            physics: const ClampingScrollPhysics(),
            itemCount: projects.length + (header == null ? 0 : 1),
            itemBuilder: (context, index) {
              if (header != null && index == 0) return header;
              if (header != null && index > 0) {
                return JobCard(job: projects[index - 1], onTap: onTap);
              }
              return JobCard(job: projects[index], onTap: onTap);
            },
          ),
        );
      },
    );
  }

  Future<void> _promptCallsPermissions(BuildContext context) async {
    final areCallPermissionsGranted = await checkCallPermissions();
    if (!areCallPermissionsGranted) {
      final shouldHandlePermissions = await showCallPermissionsDialog(context);
      if (shouldHandlePermissions) await handleCallPermissions();
    }
  }

  Future<void> _promptChecks(
    BuildContext context,
    Iterable<Job> precheckProjects,
  ) async {
    for (final project in precheckProjects) {
      final shouldReport = await showConfirmationPopup(
        type: PopupType.dialog,
        context: context,
        title: Text(S.of(context).precheckEventTitle),
        message: Text(S.of(context).precheckEventWaitingDescription),
        actionTitle: Text(S.of(context).precheckButton),
        cancelTitle: Text(S.of(context).close),
      );
      if (!shouldReport || !context.mounted) continue;

      final reporterId = context.authState.user?.documentReference.id;
      if (reporterId == null) continue;

      final report = await GetIt.instance<JobReportsRepository>().latestDraft(
        jobId: project.id,
        reporterId: reporterId,
        type: InstallationReportType.precheck,
      );
      await reportPrechecks(context: context, job: project, report: report);
    }
  }

  Future<void> _promptCriticalDialogs(
    BuildContext context,
    Iterable<Job> precheckProjects,
  ) async {
    SchedulerBinding.instance.addPostFrameCallback((_) async {
      await _promptCallsPermissions(context);
      if (precheckProjects.isNotEmpty) {
        await _promptChecks(context, precheckProjects);
      }
    });
  }
}

class _CustomerPreviousProjectsSection extends StatelessWidget {
  const _CustomerPreviousProjectsSection({required this.onTap});
  final ValueChanged<String> onTap;

  @override
  Widget build(BuildContext context) {
    final userRef = context.authState.user?.documentReference;
    if (userRef == null) return CenteredProgressIndicator();
    return StreamBuilder(
      stream: GetIt.instance<JobsRepository>().customerClosedJobs(userRef.id),
      builder: (context, projectsSnapshot) {
        if (!projectsSnapshot.hasData) return CenteredProgressIndicator();
        final projects = projectsSnapshot.data!;
        if (projects.isEmpty) return const SizedBox.shrink();

        return Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Padding(
              padding: const EdgeInsets.only(left: Margins.large),
              child: Text(
                S.of(context).customerPreviousProjectsTitle,
                style: Theme.of(context).textTheme.titleLarge,
              ),
            ),
            ListView.separated(
              shrinkWrap: true,
              physics: const ClampingScrollPhysics(),
              itemCount: projects.length,
              itemBuilder: (context, index) {
                return PreviousProjectCard(job: projects[index], onTap: onTap);
              },
              separatorBuilder:
                  (context, index) => const VerticalMargin.small(),
            ),
          ].separatedBy(() => const VerticalMargin.large()),
        );
      },
    );
  }
}

class _AnonymousUserProjectsView extends StatelessWidget {
  const _AnonymousUserProjectsView();

  @override
  Widget build(BuildContext context) {
    return EmptyState(
      title: Text(S.of(context).notLoggedIn),
      description: Text(S.of(context).anonymousProjectsEmptyStateDescription),
      children: const [LoginActionButton()],
    );
  }
}
