import 'package:done_auth/done_auth.dart';
import 'package:done_booking/done_booking.dart';
import 'package:done_image/done_image.dart';

import 'package:done_localizations/done_localizations.dart';

import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

class FixedPriceListItem extends StatelessWidget {
  const FixedPriceListItem({
    super.key,
    required this.fixedPriceJob,
    this.isActiveForCompany = false,
    this.onToggle,
    this.enableTogglingActiveFixedPriceJobs = false,
    this.onTap,
  });

  final FixedPriceJob fixedPriceJob;
  final ValueChanged<bool?>? onToggle;
  final bool enableTogglingActiveFixedPriceJobs;
  final bool isActiveForCompany;
  final void Function(FixedPriceJob, BuildContext)? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: Margins.large,
          vertical: Margins.small,
        ),
        child: Row(
          children: [
            FixedPriceJobsImage(imageUrl: fixedPriceJob.imageUrl),
            const HorizontalMargin.large(),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Text(
                    fixedPriceJob.title,
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  if (fixedPriceJob.subtitle != null)
                    Padding(
                      padding: const EdgeInsets.only(top: Margins.verySmall),
                      child: Text(
                        fixedPriceJob.subtitle!,
                        style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                          color: context.doneColors.typographyLowContrast,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  const VerticalMargin.small(),
                  _price(
                    enableTogglingActiveFixedPriceJobs,
                    context,
                    fixedPriceJob,
                  ),
                ],
              ),
            ),
            const HorizontalMargin.medium(),
            if (enableTogglingActiveFixedPriceJobs)
              Checkbox(value: isActiveForCompany, onChanged: onToggle)
            else
              Icon(
                Icons.keyboard_arrow_right,
                color: context.doneColors.uiChevron,
              ),
          ],
        ),
      ),
      onTap: onTap != null ? () => onTap!(fixedPriceJob, context) : null,
    );
  }

  Widget _price(
    bool hasDetailButton,
    BuildContext context,
    FixedPriceJob fixedPriceJob,
  ) {
    return Row(
      children: <Widget>[
        Text(
          getPriceString(
            fixedPriceJob.priceBreakdown.totalCustomerPriceAfterDeduction,
          ),
          style: Theme.of(context).textTheme.titleMedium,
        ),
        DoneTinyBadge(
          value: fixedPriceJob.deductionType.shortTitle(context),
          shrink: fixedPriceJob.deductionType == DeductionType.greenTechnology,
        ),
        if (hasDetailButton)
          FittedBox(
            child: DoneChip(
              label: Text(S.of(context).seeMore),
              color: context.doneColors.typographyHightContrast,
              onTap: () {
                final params = FixedPriceJobDetailsRouteParams(
                  id: fixedPriceJob.documentReference.id,
                  isAlreadyBookedJob: context.authState.isUserCraftsman(),
                );
                FixedPriceJobDetailsRoute(params: params).navigate(context);
              },
            ),
          ),
      ].separatedBy((() => const HorizontalMargin.verySmall())),
    );
  }
}
