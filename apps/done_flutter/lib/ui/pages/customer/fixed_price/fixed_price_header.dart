import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

class FixedPriceHeader extends StatelessWidget {
  const FixedPriceHeader({
    super.key,
    required this.title,
    required this.subtitle,
    required this.image,
    this.padImage = false,
    required this.color,
  });

  final String title;
  final String subtitle;
  final bool padImage;
  final Widget image;
  final Color color;

  @override
  Widget build(BuildContext context) {
    return DecoratedBox(
      decoration: BoxDecoration(
        color: color,
        borderRadius: const BorderRadius.only(bottomLeft: Radius.circular(30)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: <Widget>[
          Expanded(
            child: Padding(
              padding: const EdgeInsets.fromLTRB(20, 0, 0, 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                      fontSize: 24,
                      color: Colors.white,
                    ),
                  ),
                  const VerticalMargin.small(),
                  Text(
                    subtitle,
                    style: Theme.of(
                      context,
                    ).textTheme.labelMedium!.apply(color: Colors.white70),
                  ),
                ],
              ),
            ),
          ),
          const HorizontalMargin(margin: 20),
          Padding(
            padding:
                padImage
                    ? const EdgeInsets.fromLTRB(0, 0, 12, 12)
                    : EdgeInsets.zero,
            child: image,
          ),
        ],
      ),
    );
  }
}
