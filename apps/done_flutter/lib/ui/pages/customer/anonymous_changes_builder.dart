import 'package:done_auth/done_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

/// A widget that rebuilds its child when the [AuthState] anonymity updates
class AnonymousChangesBuilder extends StatelessWidget {
  const AnonymousChangesBuilder({required this.builder, super.key, this.child});

  final ValueWidgetBuilder<bool> builder;

  final Widget? child;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthCubit, AuthState>(
      buildWhen:
          (previous, current) => previous.isAnonymous != current.isAnonymous,
      builder: (context, state) => builder(context, state.isAnonymous, child),
    );
  }
}
