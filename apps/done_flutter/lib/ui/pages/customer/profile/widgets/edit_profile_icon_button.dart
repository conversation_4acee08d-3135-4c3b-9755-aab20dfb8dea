import 'package:done_flutter/core/router/routes/routes_definitions.dart';
import 'package:done_image/done_image.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

class EditProfileIconButton extends StatelessWidget {
  const EditProfileIconButton({super.key});

  @override
  Widget build(BuildContext context) {
    return IconButton(
      onPressed: () => const EditProfileRoute().navigate(context),
      icon: Image.asset(
        ImageAssets.edit,
        color: context.doneColors.uiBg3,
        height: 18,
        width: 18,
      ),
    );
  }
}
