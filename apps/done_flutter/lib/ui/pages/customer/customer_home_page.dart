import 'package:done_analytics/done_analytics.dart';
import 'package:done_deductions/done_deductions.dart';
import 'package:done_flutter/core/blocs/admin_configuration_cubit/admin_configuration_cubit.dart';
import 'package:done_flutter/core/configuration/done_urls.dart';
import 'package:done_flutter/core/router/routes/project_routes.dart';
import 'package:done_flutter/ui/pages/customer/customer_projects_page.dart';
import 'package:done_flutter/utils/helpers/launch_url.dart';
import 'package:done_flutter/utils/version_check_util.dart';
import 'package:done_models/done_models.dart';
import 'package:done_flutter/ui/widgets/customer_content_cards.dart';
import 'package:done_flutter/ui/widgets/service_type_card.dart';
import 'package:done_image/done_image.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:done_auth/done_auth.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:share_plus/share_plus.dart';
import 'package:done_flutter/ui/widgets/buttons/log_in_action_button.dart';

class CustomerHomePage extends StatefulWidget {
  @override
  _CustomerHomePageState createState() => _CustomerHomePageState();
}

class _CustomerHomePageState extends State<CustomerHomePage> {
  int? numberOfJobs;

  @override
  Widget build(BuildContext context) {
    final user = context.watchAuthState.user!;
    final firstName = user.firstName?.toString();

    return Scaffold(
      key: const Key('customerHomePage'),
      appBar: AppBar(
        centerTitle: false,
        elevation: 0,
        actions:
            context.authState.isAnonymous
                ? const [LoginActionButton(noDecoration: true)]
                : null,
        title: Text(
          '${S.of(context).customerJobsHi}${firstName.isNullOrEmpty() ? '' : ' $firstName'}! 👋',
          style: Theme.of(
            context,
          ).textTheme.headlineSmall!.apply(color: context.doneColors.uiBlack),
        ),
      ),
      body: _buildBody(context),
    );
  }

  Widget _buildBody(BuildContext context) {
    final user = context.authState.user!;
    // If customerOf is null, we default to showing Done booking sections (anonynous users)
    final customerOf = user.customerOf ?? [donePartnerId];

    return Scrollbar(
      child: SingleChildScrollView(
        child: PageContent(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              if (kShowVersionUtils) const CollapsableSoftUpdateBanner(),
              _buildJobs(context),
              if (customerOf.isEmpty || customerOf.contains(donePartnerId)) ...[
                _buildCategorySection(context),
                _buildShareSection(context),
              ],
              const _HomeContentList(),
              _buildRotRutSection(context),
              if (context.authState.isAnonymous) _buildCraftsmanSignup(context),
              const VerticalMargin(margin: 126),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildJobs(BuildContext context) {
    if (context.authState.isAnonymous) return const SizedBox.shrink();
    return CustomerOngoingProjectsSection(
      showEmptyState: false,
      onTap: (projectId) {
        final params = ProjectDetailsRouteParams(projectId: projectId);
        ProjectDetailsRoute(params: params).navigate(context);
      },
      header: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: Margins.large,
        ).copyWith(bottom: Margins.small),
        child: Text(
          S.of(context).customerJobsOngoingProjects,
          style: Theme.of(context).textTheme.titleLarge,
          textAlign: TextAlign.start,
        ),
      ),
    );
  }

  Widget _buildCraftsmanSignup(BuildContext context) {
    return Column(
      children: <Widget>[
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            children: <Widget>[
              const VerticalMargin.large(),
              OutlinedButton(
                style: OutlinedButton.styleFrom(
                  foregroundColor: context.doneColors.typographyHightContrast,
                ),
                child: Text(S.of(context).landingProfessional),
                onPressed: () => launchUrl(DoneUrls.doneCraftsmanPage),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildCategorySection(BuildContext context) {
    return Column(
      children: [
        BlocBuilder<AdminConfigurationCubit, AdminConfiguration?>(
          builder: (context, adminConfig) {
            if (adminConfig == null) return CenteredProgressIndicator();
            final configuration = adminConfig;

            return Padding(
              padding: const EdgeInsets.all(Margins.large),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        S.of(context).ourServices,
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                    ],
                  ),
                  const VerticalMargin.verySmall(),
                  Text(
                    S.of(context).customerHomeCategoriesSubtitle,
                    style: Theme.of(context).textTheme.bodyMedium!.apply(
                      color: context.doneColors.typographyMediumContrast,
                    ),
                  ),
                  const VerticalMargin.medium(),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: visibleServices
                        .where(
                          (service) =>
                              configuration
                                  .serviceAvailability?[service]
                                  ?.active ??
                              false,
                        )
                        .map<Widget>(
                          (service) =>
                          // Container(color: Colors.red, height: 50, child: Text(service.id))
                          ServiceTypeCard(
                            serviceType: service,
                            contentSource: 'customer_jobs',
                          ),
                        )
                        .toList()
                        .separatedBy(() => const VerticalMargin.small()),
                  ),
                ],
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildRotRutSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: <Widget>[
        Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              Text(
                S.of(context).customerJobsBalance,
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const VerticalMargin.small(),
              DoneButton(
                title: Text(
                  S.of(context).customerJobsSeeBalance,
                  style: Theme.of(context).textTheme.bodyMedium!.apply(
                    color: context.doneColors.uiAlwaysPureWhite,
                  ),
                ),
                onPressed:
                    () => const CheckDeductionsBalanceRoute().navigate(context),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildShareSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: <Widget>[
        Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              Text(
                S.of(context).shareSectionTitle,
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const VerticalMargin(margin: 16),
              DoneButton(
                key: const Key("GiveAwayButton"),
                image: Image.asset(
                  ImageAssets.shareheart,
                  scale: 3,
                  color: context.doneColors.uiAlwaysPureWhite,
                ),
                title: Text(
                  S.of(context).shareButton,
                  style: Theme.of(context).textTheme.bodyMedium!.apply(
                    color: context.doneColors.uiAlwaysPureWhite,
                  ),
                ),
                onPressed: _shareApp,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Future<void> _shareApp() async {
    await EventLogger.instance.logEvent('shareApp', {'source': 'customerHome'});
    final locale = Localizations.localeOf(context);
    final share =
        await FirebaseFirestore.instance.collection('admin').doc('share').get();
    if (!share.exists || share.data() == null) return;
    // TODO : use the `languageCode` instead of `eng` on backend it should be `en`
    final data =
        share.data()![locale == SupportedLocales.swedish.locale ? 'sv' : 'eng']
            as String;
    await Share.share(data);
    return;
  }
}

class _HomeContentList extends StatelessWidget {
  const _HomeContentList();

  @override
  Widget build(BuildContext context) {
    // TODO(ayman): refactor into model and extract to database
    return StreamBuilder<DocumentSnapshot<Map<String, dynamic>>>(
      stream:
          FirebaseFirestore.instance
              .collection('admin')
              .doc('customerHome')
              .snapshots(),
      builder: (context, snapshot) {
        if (!snapshot.hasData || snapshot.data!.data() == null)
          return const SizedBox();
        final data = snapshot.data!.data()!;
        return Padding(
          padding: const EdgeInsets.all(16),
          child: ContentList(sections: data['sections'] as List),
        );
      },
    );
  }
}
