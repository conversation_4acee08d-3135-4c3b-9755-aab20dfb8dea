import 'package:done_database/done_database.dart';
import 'package:done_flutter/core/router/routes/project_routes.dart';
import 'package:done_image/done_image.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_flutter/ui/widgets/reviews/review_image_tile.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:camera/camera.dart';
import 'package:get_it/get_it.dart';

import 'package:logger/logger.dart';
import 'package:permission_handler/permission_handler.dart';

class CameraLayoutActions extends StatefulWidget {
  const CameraLayoutActions({
    super.key,
    required this.controller,
    required this.picturePaths,
    required this.jobId,
    required this.companyId,
  });

  final CameraController? controller;
  final ValueNotifier<List<String>> picturePaths;
  final String jobId;
  final String companyId;

  @override
  _CameraLayoutActionsState createState() => _CameraLayoutActionsState();
}

class _CameraLayoutActionsState extends State<CameraLayoutActions>
    with SingleTickerProviderStateMixin {
  late bool _imageOnDrag;
  late bool _areImagesUploading;

  bool _isImageListEmpty() => widget.picturePaths.value.isEmpty;

  @override
  void initState() {
    _imageOnDrag = false;
    _areImagesUploading = false;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: <Widget>[
          _buildTopActions(),
          _buildRemoveImageFromListDropZone(),
          if (_isImageListEmpty())
            _buildCameraInformation()
          else
            _buildImageHolder(),
          _buildBottomActions(),
        ],
      ),
    );
  }

  Widget _buildTopActions() {
    return Container(
      padding: const EdgeInsets.only(left: 16, top: 16, right: 16),
      child: Row(
        children: [
          CircleButton(
            onPressed:
                () => displayExitPopUp(context, widget.jobId, widget.companyId),
            icon: Icon(Icons.close, color: context.doneColors.uiBlack),
          ),
        ],
      ),
    );
  }

  Widget _buildRemoveImageFromListDropZone() {
    return _imageOnDrag
        ? Expanded(
          child: ColoredBox(
            color: Colors.transparent,
            child: DragTarget<String>(
              onAcceptWithDetails:
                  (path) => Future.delayed(
                    const Duration(milliseconds: 300),
                    () =>
                        setState(() => widget.picturePaths.value.remove(path)),
                  ),
              builder:
                  (context, candidateData, rejectedData) => const SizedBox(),
            ),
          ),
        )
        : const Spacer();
  }

  Widget _buildBottomActions() {
    return Container(
      color: Colors.black,
      padding: const EdgeInsets.all(16),
      child: Stack(
        children: <Widget>[
          _buildShutterButton(),
          Center(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildPhotoLibraryButton(),
                const Spacer(),
                if (_isImageListEmpty())
                  _buildSkipButton()
                else
                  _buildSaveAndNextButton(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildShutterButton() {
    return Center(
      child: ClipOval(
        child: Material(
          color: Colors.white,
          child: Container(
            height: 52,
            width: 52,
            margin: const EdgeInsets.all(2),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(width: 2),
            ),
            child: InkWell(
              onTap: () async {
                await widget.controller?.takePicture().then(
                  (value) => setState(
                    () =>
                        widget.picturePaths.value = [
                          value.path,
                          ...widget.picturePaths.value,
                        ],
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPhotoLibraryButton() {
    return AddImageButton(
      onImagePicked: (pickedImage) {
        setState(
          () =>
              widget.picturePaths.value = [
                pickedImage.path,
                ...widget.picturePaths.value,
              ],
        );
      },
      onImagePickedError: (error) {
        GetIt.instance<Logger>().e(
          "Gallery permission not given! Error: $error",
        );
        showLibraryPermissionNotGivenDialog();
      },
    );
  }

  Widget _buildSaveAndNextButton() {
    return _areImagesUploading
        ? const DoneAdaptiveLoadingIndicator()
        : CircleButton(
          onPressed: () async {
            setState(() => _areImagesUploading = true);
            final ref = GetIt.instance<JobsRepository>().ratingRef(
              widget.jobId,
              widget.companyId,
            );
            final imagesRef = await ImageUploader.instance.uploadReviewImages(
              reviewId: ref.id,
              companyId: widget.companyId,
              pathsToFiles: widget.picturePaths.value,
            );
            await Mutations.instance
                .companyReview(ref)
                .savePhotosReviews(imagesRef);

            setState(() => _areImagesUploading = false);
            goReviewPart();
          },
          icon: Icon(Icons.check, color: context.doneColors.green),
        );
  }

  Widget _buildSkipButton() {
    return TextButton(
      style: TextButton.styleFrom(
        foregroundColor: context.doneColors.uiAlwaysPureWhite,
      ),
      child: Text(
        S.of(context).skip,
        style: Theme.of(context).textTheme.titleLarge!.apply(
          color: context.doneColors.uiAlwaysPureWhite,
        ),
      ),
      onPressed: goReviewPart,
    );
  }

  Widget _buildCameraInformation() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      height: 100,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.black, Colors.transparent],
          begin: Alignment.bottomCenter,
          end: Alignment.topCenter,
        ),
      ),
      child: Center(
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              Text(
                S.of(context).companyReviewCameraInfoTitle,
                textAlign: TextAlign.left,
                style: Theme.of(context).textTheme.labelMedium!.apply(
                  color: context.doneColors.uiAlwaysPureWhite,
                ),
              ),
              const VerticalMargin.small(),
              Text(
                S.of(context).companyReviewCameraInfoDescription,
                textAlign: TextAlign.left,
                style: Theme.of(context).textTheme.bodyMedium!.apply(
                  color: context.doneColors.uiAlwaysPureWhite,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildImageHolder() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      height: 100,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.black, Colors.transparent],
          begin: Alignment.bottomCenter,
          end: Alignment.topCenter,
        ),
      ),
      child: ValueListenableBuilder<List<String>>(
        valueListenable: widget.picturePaths,
        builder:
            (context, pictures, _) => ListView(
              scrollDirection: Axis.horizontal,
              children:
                  pictures
                      .map(
                        (path) => Padding(
                          padding: const EdgeInsets.only(top: 8, right: 8),
                          child: Draggable(
                            data: path,
                            onDragStarted:
                                () => setState(() => _imageOnDrag = true),
                            onDragCompleted:
                                () => setState(() => _imageOnDrag = false),
                            childWhenDragging: const SizedBox(
                              width: 56,
                              height: 56,
                            ),
                            feedback: ReviewImageTile(path: path),
                            child: InkWell(
                              onTap: () {
                                final params = CompanyReviewPhotoRouteParams(
                                  projectId: widget.jobId,
                                  companyId: widget.companyId,
                                  path: path,
                                );

                                CompanyReviewPhotoRoute(
                                  params: params,
                                ).navigate(context);
                              },
                              child: ReviewImageTile(path: path),
                            ),
                          ),
                        ),
                      )
                      .toList(),
            ),
      ),
    );
  }

  void goReviewPart() {
    Navigator.pop(context);
    final params = CompanyReviewRouteParams(
      projectId: widget.jobId,
      companyId: widget.companyId,
    );
    CompanyReviewRoute(params: params).navigate(context);
  }

  void showLibraryPermissionNotGivenDialog() {
    showDialog<void>(
      context: context,
      builder:
          (BuildContext context) => DoneDialog(
            title: Text(S.of(context).libraryPermissonTitle),
            content: [
              const VerticalMargin.verySmall(),
              Text(S.of(context).libraryPermissonInfo),
              const VerticalMargin.xxlarge(),
              DoneButton(
                style: DoneButtonStyle.secondary,
                title: Text(S.of(context).openSettings),
                onPressed: openAppSettings,
              ),
              const VerticalMargin(margin: 6),
              DoneButton(
                style: DoneButtonStyle.neutral,
                title: Text(S.of(context).dismiss),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
            ],
          ),
    );
  }
}

void displayExitPopUp(BuildContext context, String jobId, String companyId) {
  showDialog<void>(
    context: context,
    builder:
        (BuildContext context) => DoneDialog(
          title: Text(S.of(context).companyRatingsExitRatingsPopUpMessage),
          content: [
            const VerticalMargin.medium(),
            DoneButton(
              style: DoneButtonStyle.secondary,
              title: Text(
                S.of(context).companyRatingsExitRatingsPopUpDiscardEdit,
              ),
              onPressed: () {
                Navigator.pop(context);
                Navigator.pop(context);
              },
            ),
            const VerticalMargin(margin: 6),
            DoneButton(
              style: DoneButtonStyle.neutral,
              title: Text(S.of(context).dismiss),
              onPressed: () {
                final ref = GetIt.instance<JobsRepository>().ratingRef(
                  jobId,
                  companyId,
                );
                Mutations.instance.companyReview(ref).resetPendingReview();
                Navigator.pop(context);
              },
            ),
          ],
        ),
  );
}
