import 'package:done_auth/done_auth.dart';
import 'package:done_database/done_database.dart';
import 'package:done_image/done_image.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_flutter/ui/pages/reviews/company/company_review_success_scene.dart';
import 'package:done_flutter/ui/widgets/reviews/company/company_review_rating_star.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';

class CompanyReviewSheet extends StatefulWidget {
  const CompanyReviewSheet({required this.jobId, required this.companyId});
  final String jobId;
  final String companyId;

  @override
  _CompanyReviewSheetState createState() => _CompanyReviewSheetState();
}

class _CompanyReviewSheetState extends State<CompanyReviewSheet>
    with TickerProviderStateMixin {
  late CompanyReview _review;
  late int _pageIndex;
  List<ProgressItem> _listOfProgress = [];

  late final AnimationController _animationController;

  bool isCancelStarted = false;
  bool _isLoading = false;

  @override
  void initState() {
    _pageIndex = 0;

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    )..forward(from: 0);

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<CompanyReview>(
      stream: GetIt.instance<JobsRepository>().rating(
        widget.jobId,
        widget.companyId,
      ),
      builder: (context, snapshot) {
        if (!snapshot.hasData) return const SizedBox();

        _review = snapshot.data!;

        _listOfProgress = createCompanyRatingProgressScenes();

        if (_pageIndex < 0) {
          return Container();
        }

        if ((_pageIndex + 1 > _listOfProgress.length) && _isLoading) {
          return CenteredProgressIndicator();
        }

        return BaseProgressFlowPage(
          displayWarningPopUp: true,
          animationController: _animationController,
          cancelProgressButtonTitle:
              S.of(context).companyRatingsExitRatingsPopUpDiscardEdit,
          cancelProgressTitle:
              S.of(context).companyRatingsExitRatingsPopUpMessage,
          onPrevPage: _prevPage,
          onCancelProgress: () {
            isCancelStarted = true;
            Mutations.instance
                .companyReview(_review.reference)
                .resetPendingReview();
          },
          pageIndex: _pageIndex,
          listOfProgress: _listOfProgress,
        );
      },
    );
  }

  List<ProgressItem> createCompanyRatingProgressScenes() {
    return [
      ProgressItem(
        id: "rating",
        metric: "review_rating_selected",
        title: S.of(context).companyRatingsReviewHint,
        subtitle: _review.company.name,
        widgetBuilder: (context, visible) => _buildStarRatings(),
      ),
      ProgressItem(
        id: "publicFeedBack",
        metric: "review_entered_public_feedback",
        title: S.of(context).companyRatingsReviewHint,
        subtitle: _review.company.name,
        widgetBuilder: (context, visible) => _buildPublicFeedback(visible),
      ),
      ProgressItem(
        id: "privateFeedBack",
        metric: "review_entered_private_feedback",
        title: S.of(context).companyRatingsReviewHint,
        subtitle: _review.company.name,
        widgetBuilder: (context, visible) => _buildPrivateFeedback(visible),
      ),
      ProgressItem(
        id: "recommendation",
        metric: "review_recommendation_selected",
        title: S.of(context).companyRatingsReviewHint,
        subtitle: _review.company.name,
        widgetBuilder: (context, visible) => _buildRecommendation(),
      ),
      if (_review.rating != null)
        ProgressItem(
          id: "final",
          metric: "review_submitted",
          hasCloseIcon: false,
          hasBackIcon: false,
          title: S.of(context).companyRatingsReviewHint,
          subtitle: _review.company.name,
          widgetBuilder: (context, visible) => _buildSuccess(),
        ),
    ];
  }

  Widget _buildStarRatings() {
    return CompanyRatingStar(
      review: _review,
      onContinue: (
        double ratingStar,
        List<ComplimentTypes> compliments,
        List<ComplaintTypes> complaints,
      ) async {
        await Mutations.instance
            .companyReview(_review.reference)
            .saveStarRating(ratingStar, compliments, complaints);
        _review
          ..rating = ratingStar
          ..compliments = compliments
          ..complaints = complaints;
        _nextPage();
      },
    );
  }

  Widget _buildPublicFeedback(bool visible) {
    return FlowSingleFieldScene(
      isVisible: visible,
      minLines: 5,
      initText: _review.publicFeedback,
      description: S.of(context).companyRatingsPublicReviewSubtitle,
      fieldText: S.of(context).companyRatingsPublicReviewTitle,
      hint: S.of(context).typeHere,
      mustFilled: false,
      onChange: (_) async => ValidationResult(result: true),
      onContinue: (value) async {
        if (value == null) return _nextPage();
        await Mutations.instance
            .companyReview(_review.reference)
            .savePublicFeedback(value);
        _review.publicFeedback = value;
        _nextPage();
      },
    );
  }

  Widget _buildPrivateFeedback(bool visible) {
    return FlowSingleFieldScene(
      isVisible: visible,
      initText: _review.privateFeedback,
      minLines: 5,
      onChange: (answer) async => ValidationResult(result: true),
      description: S.of(context).companyRatingsPrivateReviewSubtitle,
      fieldText: S.of(context).companyRatingsPrivateReviewTitle,
      hint: S.of(context).typeHere,
      mustFilled: false,
      onContinue: (value) async {
        if (value == null) return _nextPage();
        await Mutations.instance
            .companyReview(_review.reference)
            .savePrivateFeedback(value);
        _review.privateFeedback = value;
        _nextPage();
      },
    );
  }

  Widget _buildRecommendation() {
    final firstName = context.authState.user!.firstName;
    return FlowBaseScene(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: StreamBuilder<Partnership>(
          stream: GetIt.instance<PartnershipRepository>().jobNetworkPartnership(
            jobId: widget.jobId,
          ),
          builder: (context, partnerSnapshot) {
            if (!partnerSnapshot.hasData) return CenteredProgressIndicator();

            final partner = partnerSnapshot.data!;

            return Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                if (partner.logo != null)
                  CachedNetworkImage(
                    imageUrl: partner.logo!,
                    height: 40,
                    fit: BoxFit.contain,
                  ),
                const VerticalMargin(margin: 32),
                Text(
                  S.of(context).npsDescription(firstName, partner.name),
                  style: Theme.of(context).textTheme.labelMedium,
                ),
                const VerticalMargin.xlarge(),
                NpsView(
                  highestDescription: S.of(context).npsVeryLikely,
                  lowestDescription: S.of(context).npsNotLikely,
                  onValueChanged: (value) {
                    _review.netPromoterScore = value;
                  },
                ),
                const VerticalMargin.xxlarge(),
                DoneButton(
                  title: Text(S.of(context).submit),
                  style: DoneButtonStyle.secondary,
                  onPressed: _completeReview,
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildSuccess() {
    return CompanyReviewSuccessScene(review: _review);
  }

  Future<void> _completeReview() async {
    setState(() => _isLoading = true);
    await Mutations.instance
        .companyReview(_review.reference)
        .finalize(_review.netPromoterScore);
    await Mutations.instance.job(widget.jobId).markAsReviewed();
    _nextPage();
  }

  void _nextPage() {
    setState(() {
      _isLoading = false;
      _pageIndex++;
    });
  }

  void _prevPage() {
    setState(() {
      _pageIndex--;
    });
  }
}
