import 'package:done_localizations/done_localizations.dart';
import 'package:done_flutter/ui/widgets/company_avatar.dart';
import 'package:done_flutter/ui/widgets/user_avatar.dart';
import 'package:done_image/done_image.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

class CompanyReviewSuccessScene extends StatelessWidget {
  const CompanyReviewSuccessScene({super.key, required this.review});

  final CompanyReview review;

  @override
  Widget build(BuildContext context) {
    return FlowBaseScene(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if ((review.rating ?? 0) * 5 > 2) ...[
              Center(child: Image.asset(ImageAssets.ratingMedal)),
              const VerticalMargin(margin: 32),
            ],
            Text(
              S.of(context).companyRatingsCompleteScreenTitle,
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const VerticalMargin.large(),
            if ((review.rating ?? 0) * 5 > 2) ...[
              Text(
                S.of(context).companyRatingsCompleteScreenSubtitle,
                style: Theme.of(
                  context,
                ).textTheme.bodyLarge!.apply(color: context.doneColors.uiBlack),
              ),
              const VerticalMargin.large(),
            ],
            if ((review.rating ?? 0) * 5 <= 2)
              _buildCustomerServiceContactYouBanner(context),
            const VerticalMargin.large(),
            Row(
              children: <Widget>[
                CompanyAvatar(review.company.companyReference),
                const HorizontalMargin.medium(),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      Text(
                        S.of(context).companyRatingsCompleteScreenCompany,
                        style: Theme.of(context).textTheme.bodyMedium!.apply(
                          color: context.doneColors.uiBlack,
                        ),
                      ),
                      Text(
                        review.company.name,
                        style: Theme.of(context).textTheme.labelMedium!.apply(
                          color: context.doneColors.uiBlack,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const VerticalMargin.medium(),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                UserAvatar(review.author.reference),
                const HorizontalMargin.medium(),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      Text(
                        S.of(context).companyRatingsCompleteScreenContact,
                        style: Theme.of(context).textTheme.bodyMedium!.apply(
                          color: context.doneColors.uiBlack,
                        ),
                      ),
                      Text(
                        review.author.name,
                        style: Theme.of(context).textTheme.labelMedium!.apply(
                          color: context.doneColors.uiBlack,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const VerticalMargin(margin: 32),
            DoneButton(
              title: Text(S.of(context).dismiss),
              style: DoneButtonStyle.secondary,
              onPressed: () {
                Navigator.pop(context);
              },
            ),
          ],
        ),
      ),
    );
  }
}

Widget _buildCustomerServiceContactYouBanner(BuildContext context) {
  return Container(
    margin: const EdgeInsets.only(bottom: 16),
    padding: const EdgeInsets.all(16),
    decoration: BoxDecoration(
      color: context.doneColors.darkRed,
      borderRadius: const BorderRadius.all(Radius.circular(8)),
    ),
    child: Text(
      S.of(context).companyReviewLowStarRatingText,
      style: Theme.of(
        context,
      ).textTheme.labelMedium!.apply(color: Colors.white),
    ),
  );
}
