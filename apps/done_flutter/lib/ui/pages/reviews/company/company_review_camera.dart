import 'package:done_localizations/done_localizations.dart';
import 'package:done_flutter/ui/pages/reviews/company/company_review_camera_actions.dart';
import 'package:done_image/done_image.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:camera/camera.dart';
import 'package:get_it/get_it.dart';
import 'package:logger/logger.dart';
import 'package:permission_handler/permission_handler.dart';

class CameraPage extends StatefulWidget {
  const CameraPage({required this.jobId, required this.companyId});

  final String jobId;
  final String companyId;

  @override
  _CameraPageState createState() => _CameraPageState();
}

class _CameraPageState extends State<CameraPage> {
  CameraController? cameraController;
  final _picturePaths = ValueNotifier<List<String>>([]);
  bool _initializing = true;

  @override
  void initState() {
    super.initState();
    initializeCamera();
  }

  Future<void> initializeCamera() async {
    try {
      await Permission.camera.request();
      final cameras = await availableCameras();
      if (cameras.isEmpty) throw Exception('No cameras available');
      if (!mounted) return;
      cameraController = CameraController(cameras.first, ResolutionPreset.high);
    } catch (e) {
      GetIt.instance<Logger>().e(e);
    }
    setState(() => _initializing = false);
  }

  @override
  Widget build(BuildContext context) {
    return ColoredBox(
      color: Colors.black,
      child: SafeArea(
        child: Scaffold(
          backgroundColor: Colors.black,
          body: Stack(
            children: <Widget>[
              FutureBuilder<PermissionStatus>(
                future: Permission.camera.status,
                builder: (context, snapshot) {
                  if (!snapshot.hasData || _initializing) {
                    return CenteredProgressIndicator();
                  } else {
                    return snapshot.data!.isGranted && cameraController != null
                        ? CameraWidget(controller: cameraController!)
                        : _buildCameraNotAvailable();
                  }
                },
              ),
              CameraLayoutActions(
                controller: cameraController,
                picturePaths: _picturePaths,
                jobId: widget.jobId,
                companyId: widget.companyId,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCameraNotAvailable() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: <Widget>[
        Image.asset(ImageAssets.cameraOff, color: Colors.white, scale: 2.4),
        const VerticalMargin.large(),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 24),
          child: Center(
            child: Text(
              S.of(context).cameraPermissionInfo,
              style: Theme.of(context).textTheme.bodyMedium!.apply(
                color: context.doneColors.uiOnPrimary,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
        const VerticalMargin.large(),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 24),
          child: DoneButton(
            style: DoneButtonStyle.secondary,
            onPressed: openAppSettings,
            title: Text(S.of(context).openSettings),
          ),
        ),
      ],
    );
  }
}

class CameraWidget extends StatefulWidget {
  const CameraWidget({super.key, required this.controller});

  final CameraController controller;

  @override
  _CameraWidgetState createState() => _CameraWidgetState();
}

class _CameraWidgetState extends State<CameraWidget> {
  @override
  void initState() {
    super.initState();
    widget.controller.initialize().then((_) {
      if (!mounted) {
        return;
      }
      setState(() {});
    });
  }

  @override
  void dispose() {
    widget.controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.controller.value.isInitialized) {
      return CenteredProgressIndicator();
    }
    return CameraPreview(widget.controller);
  }
}
