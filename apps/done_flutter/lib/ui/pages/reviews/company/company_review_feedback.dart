import 'package:done_localizations/done_localizations.dart';

import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

class CompanyReviewFeedbackBase extends StatelessWidget {
  const CompanyReviewFeedbackBase({
    required this.title,
    required this.subTitle,
    this.onSave,
    this.savedText,
  });

  final String title;
  final String subTitle;
  final ValueChanged<String>? onSave;
  final String? savedText;

  @override
  Widget build(BuildContext context) {
    final controller = TextEditingController(text: savedText ?? "");

    return FlowBaseScene(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              subTitle,
              style: Theme.of(context).textTheme.bodyLarge!.apply(
                color: context.doneColors.typographyMediumContrast,
              ),
            ),
            const VerticalMargin.large(),
            DoneText<PERSON>ield(
              controller: controller,
              label: title,
              hint: S.of(context).companyRatingsReviewHint,
              minLines: 5,
            ),
            const VerticalMargin.large(),
            DoneButton(
              title: Text(S.of(context).genericContinue),
              style: DoneButtonStyle.secondary,
              onPressed: () {
                onSave!(controller.text);
              },
            ),
          ],
        ),
      ),
    );
  }
}
