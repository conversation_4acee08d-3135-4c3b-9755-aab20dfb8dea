import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:share_plus/share_plus.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:done_localizations/done_localizations.dart';
import 'package:path_provider/path_provider.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';

import 'package:universal_platform/universal_platform.dart';

class ViewPdfPage extends StatefulWidget {
  const ViewPdfPage({super.key, required this.url});

  final String url;

  @override
  _ViewPdfPageState createState() => _ViewPdfPageState();
}

class _ViewPdfPageState extends State<ViewPdfPage> {
  bool _isLoading = true;
  bool _hasError = false;
  File? _file;

  @override
  void initState() {
    super.initState();
    getFileFromUrl(widget.url);
  }

  @override
  Widget build(BuildContext context) {
    // FIXME: Rewrite to use PopScope instead
    // ignore: deprecated_member_use
    return WillPopScope(
      onWillPop: () {
        if (_file != null) _file!.delete();
        return Future.value(true);
      },
      child: Scaffold(appBar: _buildAppBar(), body: _buildBody()),
    );
  }

  Widget _buildBody() {
    if (_isLoading) return CenteredProgressIndicator();
    if (_hasError) return _buildErrorView();
    return PDFView(filePath: _file!.path);
  }

  AppBar _buildAppBar() {
    final fileName = _file?.path.split('/').last ?? '';
    return AppBar(
      titleSpacing: -8,
      title: DoneAppBarTitle(
        title: Text(S.of(context).file),
        subtitle: Text(fileName),
      ),
      centerTitle: false,
      elevation: 0,
      actions: [
        if (_file != null)
          Padding(
            padding: const EdgeInsets.all(8),
            child: IconButton(
              // Bug in linter. Wants to make Icon const but that fails compilation.
              // ignore: prefer_const_constructors
              icon: Icon(
                !kIsWeb && UniversalPlatform.isIOS
                    ? Icons.ios_share
                    : Icons.share,
              ),
              onPressed: () {
                try {
                  Share.shareXFiles([
                    XFile(_file!.path, mimeType: 'application/pdf'),
                  ]);
                } catch (e) {
                  final snackbar = SnackBar(
                    content: Text(
                      S.of(context).errorOccuredTryAgain,
                      style: const TextStyle(color: Colors.red),
                    ),
                  );
                  ScaffoldMessenger.of(context).showSnackBar(snackbar);
                }
              },
            ),
          ),
      ],
    );
  }

  Widget _buildErrorView() {
    return ErrorStateView(
      title: Text(S.of(context).errorOccuredTryAgain),
      body: DoneButton(
        style: DoneButtonStyle.secondary,
        title: Text(S.of(context).loginTryAgain),
        onPressed: () => getFileFromUrl(widget.url),
      ),
    );
  }

  Future<void> getFileFromUrl(String url) async {
    try {
      final uri = Uri.parse(url);
      final data = await http.get(uri);
      final bytes = data.bodyBytes;
      final dir = await getTemporaryDirectory();
      final fileName = Uri.parse(Uri.decodeFull(url)).pathSegments.last;
      final file = File("${dir.path}/$fileName");

      await file.writeAsBytes(bytes);

      setState(() {
        _file = file;
        _isLoading = false;
        _hasError = false;
      });
    } catch (e) {
      setState(() {
        _file = null;
        _isLoading = false;
        _hasError = true;
      });
      throw Exception("Error opening url file");
    }
  }
}
