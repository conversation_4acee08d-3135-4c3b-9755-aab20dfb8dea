import 'package:done_flutter/ui/pages/view_pdf/view_pdf_page.dart';
import 'package:done_flutter/ui/widgets/future_page_builder.dart';
import 'package:done_flutter/utils/first_build_callback.dart';
import 'package:done_router/done_router.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

/// A widget that handles routing for PDF files stored in Firebase Storage.
///
/// This widget takes a [pdfFilePath] pointing to a PDF file in Firebase Storage and a [title] widget.
/// It fetches the download URL for the PDF and either:
/// - On mobile/desktop: Shows the PDF in a [ViewPdfPage]
/// - On web: Opens the PDF in a new tab using [URLRouter] and closes the current page
class PdfRouterPage extends StatelessWidget {
  /// Creates a PdfRouterPage.
  ///
  /// The [pdfFilePath] must be a valid path to a PDF file in Firebase Storage.
  /// The [title] widget will be displayed in the app bar.
  const PdfRouterPage({
    super.key,
    required this.pdfFilePath,
    required this.title,
  });

  /// The path to the PDF file in Firebase Storage
  final String pdfFilePath;

  /// The widget to display as the title in the app bar
  final Widget title;

  @override
  Widget build(BuildContext context) {
    return FuturePageBuilder<String>(
      future:
          FirebaseStorage.instance.ref().child(pdfFilePath).getDownloadURL(),
      builder: (context, url) {
        if (!kIsWeb) {
          return ViewPdfPage(url: url);
        } else {
          return FirstBuildCallback(
            onFirstBuild: () async {
              await URLRouter.instance.handleUrl(url, context: context);
              Navigator.of(context).pop();
            },
            child: Scaffold(appBar: AppBar(title: title)),
          );
        }
      },
    );
  }
}
