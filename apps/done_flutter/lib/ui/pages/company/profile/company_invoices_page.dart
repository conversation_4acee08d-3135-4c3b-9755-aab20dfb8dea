import 'package:done_auth/done_auth.dart';
import 'package:done_database/done_database.dart';

import 'package:done_flutter/invoicing/pages/company_invoices_overview_page.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';

class CompanyInvoicesPage extends StatelessWidget {
  const CompanyInvoicesPage({super.key});

  @override
  Widget build(BuildContext context) {
    final companyRef = context.authState.getUserCompany()!;
    return StreamBuilder<List<CompanyInvoice>>(
      stream: GetIt.instance<InvoicesRepository>().companyInvoices(
        companyId: companyRef.id,
      ),
      builder: (BuildContext context, snapshot) {
        if (!snapshot.hasData) return const LoadingPage();

        final invoices = snapshot.data!;

        return CompanyInvoicesOverviewPage(invoices: invoices);
      },
    );
  }
}
