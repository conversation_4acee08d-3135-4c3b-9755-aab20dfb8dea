import 'package:auto_size_text/auto_size_text.dart';
import 'package:done_auth/done_auth.dart';

import 'package:done_database/done_database.dart';
import 'package:done_flutter/core/router/routes/routes_definitions.dart';

import 'package:done_flutter/utils/extensions/enums/social_url_types.dart';
import 'package:done_flutter/utils/text_field_utilities.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:done_localizations/done_localizations.dart' show S;
import 'package:get_it/get_it.dart';

class EditCompanyProfilePage extends StatefulWidget {
  const EditCompanyProfilePage();

  @override
  _EditCompanyProfilePageState createState() => _EditCompanyProfilePageState();
}

class _EditCompanyProfilePageState extends State<EditCompanyProfilePage> {
  late bool _hasCompanyDescriptionChanged;

  @override
  void initState() {
    _hasCompanyDescriptionChanged = false;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final companyRef = context.authState.user!.company!;
    return StreamBuilder<Company>(
      stream: GetIt.instance<CompanyRepository>().company(companyRef.id),
      builder: (context, snapshot) {
        if (!snapshot.hasData) return const LoadingPage();

        final company = snapshot.data!;
        return Scaffold(
          appBar: AppBar(
            elevation: 0,
            titleSpacing: -8,
            centerTitle: false,
            title: Row(
              children: [
                AutoSizeText(
                  company.name,
                  style: Theme.of(context).textTheme.headlineSmall!.apply(
                    color: context.doneColors.uiBlack,
                  ),
                ),
              ],
            ),
          ),
          body: Scrollbar(
            child: ListView(
              shrinkWrap: true,
              children: <Widget>[
                _buildEditCompanyDescription(context, company),
                const VerticalMargin.xxlarge(),
                _buildEditCompanySocialLinks(context, company),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildEditCompanyDescription(BuildContext context, Company company) {
    final companyInfoTextFieldController = txtCtrlFromValue(
      company.profileText,
    );
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Row(
            children: <Widget>[
              Expanded(
                child: Text(
                  S.of(context).editCompanyInfo,
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ),
              const HorizontalMargin.medium(),
              DoneButton(
                title: Text(S.of(context).seeAsCustomer),
                onPressed: () => const ProfileRoute().navigate(context),
                style: DoneButtonStyle.neutral,
              ),
            ],
          ),
          const VerticalMargin.medium(),
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: context.doneColors.uiBg1),
              borderRadius: BorderRadius.circular(6),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: TextField(
              controller: companyInfoTextFieldController,
              maxLines: null,
              minLines: 9,
              style: Theme.of(context).textTheme.bodyLarge,
              keyboardType: TextInputType.multiline,
              decoration: InputDecoration(
                border: InputBorder.none,
                disabledBorder: InputBorder.none,
                contentPadding: const EdgeInsets.only(
                  bottom: 16,
                  top: 16,
                  right: 8,
                ),
                hintText: S.of(context).addCompanyInfoHintText,
              ),
            ),
          ),
          const VerticalMargin.large(),
          if (_hasCompanyDescriptionChanged) ...[
            Text(
              S.of(context).publishedIndicator,
              style: Theme.of(
                context,
              ).textTheme.bodySmall!.apply(color: context.doneColors.green),
            ),
            const VerticalMargin.large(),
          ],
          DoneButton(
            title: Text(S.of(context).publishCompanyProfile),
            style: DoneButtonStyle.secondary,
            onPressed: () {
              setState(() {
                _hasCompanyDescriptionChanged = true;
              });
              company.updateProfileText(companyInfoTextFieldController.text);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildEditCompanySocialLinks(BuildContext context, Company company) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            S.of(context).editSocailLinks,
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const VerticalMargin.xxlarge(),
          ...SocialUrlTypes.values.map(
            (e) => EditSocialItems(
              url: company.socialUrls?[e.name] as String?,
              type: e,
              company: company,
            ),
          ),
        ],
      ),
    );
  }
}

class EditSocialItems extends StatefulWidget {
  const EditSocialItems({
    super.key,
    required this.company,
    required this.type,
    this.url,
  });

  final String? url;
  final SocialUrlTypes type;
  final Company company;

  @override
  _EdilSocialItemsState createState() => _EdilSocialItemsState();
}

class _EdilSocialItemsState extends State<EditSocialItems> {
  late final TextEditingController _socialMediaController;
  late bool _validationFailed;

  @override
  void initState() {
    super.initState();
    _socialMediaController = txtCtrlFromValue(widget.url);
    _validationFailed = false;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        CupertinoTextField(
          placeholder: S.of(context).enterUrl,
          controller: _socialMediaController,
        ),
        if (_validationFailed)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 4),
            child: Text(
              S.of(context).wrongUrlMessage,
              style: Theme.of(
                context,
              ).textTheme.bodySmall!.apply(color: context.doneColors.red),
            ),
          ),
        const VerticalMargin.medium(),
        if (widget.url.isNullOrEmpty())
          DoneButton(
            title: Text(
              S.of(context).addUrlButtonText(widget.type.title(context)),
            ),
            style: DoneButtonStyle.secondary,
            onPressed:
                () => validateAndUpdateSocialUrl(_socialMediaController.text),
          )
        else
          Row(
            children: [
              Expanded(
                child: DoneButton(
                  title: Text(S.of(context).update),
                  style: DoneButtonStyle.neutral,
                  onPressed:
                      () => validateAndUpdateSocialUrl(
                        _socialMediaController.text,
                      ),
                ),
              ),
              const HorizontalMargin.large(),
              Expanded(
                child: DoneButton(
                  title: Text(S.of(context).remove),
                  style: DoneButtonStyle.negative,
                  onPressed: () {
                    setState(() {
                      _socialMediaController.text = "";
                      widget.company.removeSocialUrl(widget.type);
                    });
                  },
                ),
              ),
            ],
          ),
        const VerticalMargin.large(),
      ],
    );
  }

  void validateAndUpdateSocialUrl(String url) {
    setState(() {
      if (validateUrl(url)) {
        widget.company.updateSocialUrl(
          _socialMediaController.text,
          widget.type,
        );
        _validationFailed = false;
      } else {
        _socialMediaController.text = widget.url ?? "";
        _validationFailed = true;
      }
    });
  }
}

bool validateUrl(String string) {
  const urlPattern =
      r"(https?|http)://([-A-Z0-9.]+)(/[-A-Z0-9+&@#/%=~_|!:,.;]*)?(\?[A-Z0-9+&@#/%=~_|!:‌​,.;]*)?";
  return RegExp(urlPattern, caseSensitive: false).hasMatch(string);
}
