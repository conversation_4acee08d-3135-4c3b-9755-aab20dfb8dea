import 'package:done_analytics/done_analytics.dart';
import 'package:done_auth/done_auth.dart';
import 'package:done_database/done_database.dart';
import 'package:done_flutter/core/configuration/done_urls.dart';
import 'package:done_flutter/core/router/routes/routes_definitions.dart';
import 'package:done_flutter/utils/helpers/launch_url.dart';
import 'package:done_flutter/ui/widgets/app_version_display.dart';

import 'package:done_localizations/done_localizations.dart';
import 'package:done_flutter/ui/widgets/buttons/sign_out_button.dart';
import 'package:done_flutter/ui/widgets/cards/company_card.dart';
import 'package:done_flutter/ui/widgets/user_avatar.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:intercom_flutter/intercom_flutter.dart';

class CrafstmanProfilePage extends StatelessWidget {
  const CrafstmanProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    final craftsman = context.authState.user!;
    final children = _children(context);
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  context.authState.getUserName(
                    context,
                    anonymousText: S.of(context).anonymous,
                  ),
                  maxLines: 2,
                  style: Theme.of(context).textTheme.headlineSmall!.apply(
                    color: context.doneColors.uiBlack,
                  ),
                ),
              ],
            ),
            const HorizontalMargin.small(),
            _buildAdminBadge(context),
            const Spacer(),
            const HorizontalMargin.medium(),
            NonFetchingUserAvatar(user: craftsman, editable: true),
          ],
        ),
      ),
      body: ListView.builder(
        key: const PageStorageKey('craftsmanProfileListView'),
        primary: true,
        shrinkWrap: true,
        padding: const EdgeInsets.all(Margins.large),
        itemCount: children.length,
        itemBuilder: (context, index) => children[index],
      ),
    );
  }

  List<Widget> _children(BuildContext context) {
    final company = context.authState.company!;
    final craftsman = context.authState.user!;
    return [
      const VerticalMargin.medium(),
      CompanyCard(
        type: CompanyCardType.edit,
        onTap: () => const EditProfileRoute().navigate(context),
        companyRef: craftsman.company,
      ),
      const VerticalMargin.verySmall(),
      Text(
        S.of(context).craftsmanProfileEditCompanyInformativeText,
        style: Theme.of(context).textTheme.bodySmall,
      ),
      const VerticalMargin.xxlarge(),
      LabeledValue(
        label: S.of(context).profilePhoneNumber,
        value: craftsman.phoneNumber ?? "-",
        valueStyle: Theme.of(
          context,
        ).textTheme.bodyMedium!.apply(color: context.doneColors.uiOnPrimary),
        showCopyButton: true,
      ),
      if (craftsman.email != null) ...[
        Divider(color: context.doneColors.uiBg1),
        LabeledValue(
          label: S.of(context).profileEmail,
          value: craftsman.email,
          valueStyle: Theme.of(
            context,
          ).textTheme.bodyMedium!.apply(color: context.doneColors.uiOnPrimary),
          showCopyButton: true,
        ),
      ],

      const VerticalMargin.xxlarge(),
      Divider(color: context.doneColors.uiBg1),
      ..._buildProfileListTiles(context, company),
      const VerticalMargin.xxlarge(),
      const SignOutButton(),
      const VerticalMargin.medium(),
      const AppVersionDisplay(),
      const VerticalMargin(margin: 90), // Compensate for chat bubble
    ];
  }

  List<Widget> _buildProfileListTiles(BuildContext context, Company company) {
    return [
      CraftsmanProfileListTile(
        title: Text(S.of(context).users),
        subtitle: Text(S.of(context).companyUsersExplanation),
        icon: const Text("👥", style: TextStyle(fontSize: 32)),
        onTap: () => const CompanyUsersRoute().push<void>(context),
      ),
      if (!company.isPlatformCompany)
        CraftsmanProfileListTile(
          title: Text(S.of(context).fixedPriceJobs),
          subtitle: Text(
            S.of(context).craftsmanProfileFixedPriceJobsInformativeText,
          ),
          icon: const Text("🛠", style: TextStyle(fontSize: 32)),
          onTap:
              () => FixedPriceJobsRoute(
                params: FixedPriceJobsRouteParams(
                  enableTogglingActiveFixedPriceJobs: true,
                ),
              ).push<void>(context),
        ),
      if (!company.isPlatformCompany)
        CraftsmanProfileListTile(
          title: Text(S.of(context).invoices),
          subtitle: Text(S.of(context).craftsmanProfileInvoicesInformativeText),
          icon: const Text("🧾", style: TextStyle(fontSize: 32)),
          onTap: () => const CompanyInvoicesRoute().push<void>(context),
        ),
      CraftsmanProfileListTile(
        title: Text(S.of(context).reviews),
        subtitle: Text(S.of(context).craftsmanProfileReviewsInformativeText),
        icon: const Text("⭐", style: TextStyle(fontSize: 32)),
        onTap: () => const CompanyReviewsRoute().navigate(context),
      ),
      CraftsmanProfileListTile(
        title: Text(S.of(context).settings),
        subtitle: Text(S.of(context).openSettings),
        icon: Text(
          "⚙️",
          style: TextStyle(fontSize: 32, color: context.doneColors.uiOnPrimary),
        ),
        onTap: () => const UserSettingsRoute().navigate(context),
      ),
      if (company.isEvInstaller && !company.isPlatformCompany) ...[
        CraftsmanProfileListTile(
          title: Text(S.of(context).installersHandbook),
          icon: const Text("📖", style: TextStyle(fontSize: 32)),
          onTap: () {
            launchUrl(DoneUrls.installersHandbook);
          },
        ),
      ],
      CraftsmanProfileListTile(
        title: Text(S.of(context).contactDone),
        icon: const Text("💬", style: TextStyle(fontSize: 32)),
        onTap: () {
          Intercom.instance.displayMessenger();
          EventLogger.instance.logEvent('showIntercom');
        },
      ),
      if (!company.isPlatformCompany)
        _TermsOfServiceTile(companyId: company.id),
    ].separatedBy(() => Divider(color: context.doneColors.uiBg1));
  }

  Widget _buildAdminBadge(BuildContext context) {
    return context.authState.isCompanyAdmin
        ? const DoneTinyBadge(value: 'ADMIN')
        : const SizedBox();
  }
}

class _TermsOfServiceTile extends StatelessWidget {
  const _TermsOfServiceTile({required this.companyId});
  final String companyId;

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future: GetIt.instance<CompanyRepository>().fetchTosAcceptance(companyId),
      builder: (context, snapshot) {
        if (snapshot.connectionState != ConnectionState.done ||
            snapshot.data == null) {
          return const SizedBox.shrink();
        }
        final tosAcceptance = snapshot.data!;
        final accepted = tosAcceptance.isAccepted;

        return CraftsmanProfileListTile(
          title: Text(S.of(context).tosTitle),
          subtitle: Text(
            accepted ? S.of(context).accepted : S.of(context).tosAcceptTOS,
            style: Theme.of(context).textTheme.bodyMedium!.apply(
              color: context.doneColors.typographyMediumContrast,
            ),
          ),
          trailingPrefix:
              accepted
                  ? null
                  : DoneBadge(value: '', color: context.doneColors.uiNegative),
          icon: const Text("📋", style: TextStyle(fontSize: 32)),
          onTap: () {
            final params = TermsOfServiceRouteParams(tosId: tosAcceptance.id);
            TermsOfServiceRoute(params: params).navigate(context);
          },
        );
      },
    );
  }
}

class CraftsmanProfileListTile extends StatelessWidget {
  const CraftsmanProfileListTile({
    super.key,
    required this.title,
    this.icon,
    this.onTap,
    this.subtitle,
    this.trailingPrefix,
  });

  final Widget title;
  final Widget? icon;
  final Widget? subtitle;
  final Widget? trailingPrefix;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return ListTile(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(Margins.small),
      ),
      dense: true,
      leading: Padding(padding: const EdgeInsets.only(left: 8), child: icon),
      title: DefaultTextStyle(
        child: title,
        style: Theme.of(
          context,
        ).textTheme.labelMedium!.apply(color: context.doneColors.uiOnPrimary),
      ),
      subtitle:
          (subtitle != null)
              ? DefaultTextStyle(
                child: subtitle!,
                style: Theme.of(context).textTheme.bodyMedium!.apply(
                  color: context.doneColors.uiOnPrimary,
                ),
              )
              : null,
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (trailingPrefix != null) ...[
            trailingPrefix!,
            const HorizontalMargin.small(),
          ],
          Icon(
            Icons.keyboard_arrow_right,
            color: context.doneColors.uiOnPrimary.withValues(alpha: 0.87),
          ),
        ],
      ),
      contentPadding: EdgeInsets.zero,
      onTap: onTap,
    );
  }
}
