import 'package:auto_size_text/auto_size_text.dart';
import 'package:collection/collection.dart';
import 'package:done_auth/done_auth.dart';
import 'package:done_database/done_database.dart';
import 'package:done_flutter/ui/widgets/cards/company_review_card.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_flutter/ui/widgets/buttons/done_social_button.dart';
import 'package:done_flutter/ui/widgets/company_avatar.dart';
import 'package:done_flutter/ui/widgets/company_reviews_summary.dart';
import 'package:done_flutter/ui/widgets/insured_by_row.dart';
import 'package:done_flutter/ui/widgets/user_avatar.dart';
import 'package:done_flutter/ui/widgets/reviews/compliment_tile.dart';
import 'package:done_image/done_image.dart';
import 'package:done_flutter/utils/extensions/chunck_list.dart';
import 'package:done_flutter/utils/extensions/enums/company.dart';
import 'package:done_flutter/utils/extensions/enums/social_url_types.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart' show DocumentReference;
import 'package:get_it/get_it.dart';

class CompanyProfilePage extends StatefulWidget {
  const CompanyProfilePage({required this.companyRef, this.company});

  final DocumentReference<Company> companyRef;
  final Company? company;

  @override
  _CompanyProfilePageState createState() => _CompanyProfilePageState();
}

class _CompanyProfilePageState extends State<CompanyProfilePage> {
  late final ScrollController _scrollController;

  @override
  void initState() {
    _scrollController = ScrollController();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<Company>(
      stream: GetIt.instance<CompanyRepository>().company(widget.companyRef.id),
      builder: (context, snapshot) {
        // Show cached data if widget.company is not null, otherwise, take company data from stream.
        // this is useful for invoicing where data shouldn't change
        final company = widget.company ?? snapshot.data;
        if (company == null) return const LoadingPage();
        const reviewsLimit = 10;

        return StreamBuilder<List<CompanyReview>>(
          stream: GetIt.instance<CompanyRepository>().companyRatings(
            companyId: widget.companyRef.id,
            limit: reviewsLimit,
          ),
          builder: (context, reviewsSnapshot) {
            final companyReviews = reviewsSnapshot.data ?? [];
            return Scaffold(
              extendBodyBehindAppBar: true,
              appBar: AppBar(
                titleSpacing: -8,
                title: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      S.of(context).company,
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    Text(
                      S.of(context).profile,
                      style: Theme.of(context).textTheme.bodyLarge,
                    ),
                  ],
                ),
                elevation: 0,
                centerTitle: false,
              ),
              body: Scrollbar(
                controller: _scrollController,
                child: ListView(
                  key: const PageStorageKey('companyProfileListView'),
                  controller: _scrollController,
                  shrinkWrap: true,
                  children: <Widget>[
                    _buildHeader(context, company, companyReviews),
                    if (company.profileText.isNullOrEmpty())
                      const CompanyProfileEmptySection(
                        type: CompanyProfileSectionType.about,
                      )
                    else
                      _CompanyProfileText(text: company.profileText!),
                    const VerticalMargin.medium(),
                    if (company.insuredBy != null) ...[
                      InsuredByRow(
                        title: Text(
                          S.of(context).pluralWorkInsuredBy.toUpperCase(),
                        ),
                        institution: company.insuredBy,
                      ),
                      const VerticalMargin.small(),
                    ],
                    const VerticalMargin.small(),
                    if (company.qualifications(context).isNotEmpty)
                      _buildQualificationsSection(context, company)
                    else
                      const CompanyProfileEmptySection(
                        type: CompanyProfileSectionType.certificates,
                      ),
                    if (company.usersV2.isNotEmpty)
                      _buildCraftsmanList(context, company),
                    const VerticalMargin.xlarge(),
                    if (companyReviews.isNotEmpty)
                      _buildCompanyReviews(
                        context,
                        widget.companyRef,
                        companyReviews,
                      )
                    else
                      const CompanyProfileEmptySection(
                        type: CompanyProfileSectionType.review,
                      ),
                    const VerticalMargin.xlarge(),
                    if (company.getAvailbleSocials().isNotEmpty)
                      _buildCompanySocialLinks(context, company)
                    else
                      const CompanyProfileEmptySection(
                        type: CompanyProfileSectionType.socials,
                      ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildHeader(
    BuildContext context,
    Company company,
    List<CompanyReview> companyReviews,
  ) {
    return Container(
      margin: const EdgeInsets.all(Margins.large),
      padding: const EdgeInsets.all(Margins.large),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CompanyAvatar(company.reference, radius: 32),
          const HorizontalMargin(margin: 20),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  AutoSizeText(
                    company.name,
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const HorizontalMargin.verySmall(),
                  Image.asset(ImageAssets.verified),
                ],
              ),
              const VerticalMargin.small(),
              // Show member since if it has been 1 month already
              if (isMoreThanOneMonthAgo(company.createTime.toDate()))
                DoneKeyValuePair(
                  label: S.of(context).membersSince,
                  value: TimeFormatter.getShortHumanReadableDate(
                    company.createTime.toDate(),
                    context,
                    asShortAsPossible: true,
                    includeYear: true,
                  ),
                  labelColor: context.doneColors.typographyLowContrast,
                  valueColor: context.doneColors.typographyMediumContrast,
                ),
              const VerticalMargin.verySmall(),
              DoneKeyValuePair(
                label: S.of(context).organisationNumber,
                value: company.orgNo,
                labelColor: context.doneColors.typographyLowContrast,
                valueColor: context.doneColors.typographyMediumContrast,
              ),
              const VerticalMargin.small(),
              // In case statistics are `null` for invoice's hard copy company data for example
              // Also show no reviews summary if company has no reviews
              if (company.statistics != null &&
                  (company.statistics?.numberOfReviews ?? 0) > 0)
                GestureDetector(
                  onTap: _scrollToBottom,
                  child: CompanyReviewsSummary(company: company),
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQualificationsSection(BuildContext context, Company company) {
    final qualifications = company.qualifications(context);
    final qualificationsInfo =
        qualifications
            .map<Widget>((r) => _QualificationInfoRow(label: Text(r)))
            .toList();
    // Split referencesInfo into pairs to be displayed in row
    final qualificationsPairs = qualificationsInfo.chunck(2);
    // We have to add a Widget so the table rows are balanced, otherwise UI error will be thrown
    if (qualificationsPairs.last.length.isOdd)
      qualificationsPairs.last.add(const SizedBox());

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: Margins.xlarge),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisSize: MainAxisSize.min,
        children: [
          if (qualifications.isNotEmpty)
            Table(
              children:
                  qualificationsPairs
                      .map((pair) => TableRow(children: pair))
                      .toList(),
            ),
          Wrap(
            alignment: WrapAlignment.center,
            spacing: Margins.small,
            runSpacing: Margins.small,
            children:
                company.certificates
                    .map((e) => e.image)
                    // We need to cast it to a set to get rid of duplicates in case of el for instance
                    .toSet()
                    .map(
                      (image) => Padding(
                        padding: const EdgeInsets.only(bottom: Margins.small),
                        child: Image.asset(image),
                      ),
                    )
                    .toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildCompanySocialLinks(BuildContext context, Company company) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: Margins.xlarge),
          child: Text(
            S.of(context).seeMoreAboutCompany,
            style: Theme.of(
              context,
            ).textTheme.bodySmall!.apply(color: context.doneColors.uiOnPrimary),
          ),
        ),
        const VerticalMargin.medium(),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: Margins.large),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children:
                company
                    .getAvailbleSocials()
                    .map(
                      (social) => DoneSocialButton(
                        url: social.url,
                        image: social.type.image,
                      ),
                    )
                    .toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildCraftsmanList(BuildContext context, Company company) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: Margins.xlarge),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            S.of(context).staff,
            style: Theme.of(context).textTheme.labelMedium,
          ),
          const VerticalMargin.large(),
          ...company.usersV2.values.map(
            (e) => Column(
              children: [
                UserAvatar(e.reference, displayName: true, radius: 16),
                const VerticalMargin.medium(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCompanyReviews(
    BuildContext context,
    DocumentReference company,
    List<CompanyReview> companyReviews,
  ) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: Margins.xlarge),
          child: Text(
            S.of(context).reviews,
            style: Theme.of(
              context,
            ).textTheme.bodyLarge!.apply(color: context.doneColors.uiOnPrimary),
          ),
        ),
        const VerticalMargin.large(),
        _buildCompliments(context, companyReviews),
        const VerticalMargin.xxlarge(),
        _buildReviews(context, companyReviews),
      ],
    );
  }

  Widget _buildCompliments(
    BuildContext context,
    List<CompanyReview> companyReviews,
  ) {
    final complimentsWithCount = <ComplimentTypes, int>{};
    for (final review in companyReviews) {
      for (final compliment in review.compliments ?? <ComplimentTypes>[]) {
        if (complimentsWithCount.containsKey(compliment)) {
          complimentsWithCount.update(compliment, (value) => value + 1);
        } else {
          complimentsWithCount.putIfAbsent(compliment, () => 1);
        }
      }
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const HorizontalMargin(margin: Margins.xlarge),
              ...complimentsWithCount.entries
                  .sorted((a, b) => a.value.compareTo(b.value))
                  .reversed // Sort by most received compliment first
                  .map(
                    (e) => ComplimentTiles(type: e.key, badgeCount: e.value),
                  ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildReviews(
    BuildContext context,
    List<CompanyReview> companyReviews,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      mainAxisSize: MainAxisSize.min,
      children: [
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: [
              const HorizontalMargin(margin: Margins.xlarge),
              ...companyReviews.map(
                (review) => CompanyReviewCard(review: review),
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _scrollToBottom() {
    _scrollController.animateTo(
      _scrollController.position.maxScrollExtent,
      duration: const Duration(milliseconds: 50),
      curve: Curves.fastOutSlowIn,
    );
  }
}

class _CompanyProfileText extends StatelessWidget {
  const _CompanyProfileText({required this.text});

  final String text;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: Margins.xlarge),
      child: SelectableText(
        text,
        style: Theme.of(
          context,
        ).textTheme.bodyMedium!.apply(color: context.doneColors.uiOnPrimary),
      ),
    );
  }
}

class CompanyProfileEmptySection extends StatelessWidget {
  const CompanyProfileEmptySection({super.key, required this.type});

  final CompanyProfileSectionType type;

  @override
  Widget build(BuildContext context) {
    return context.authState.isUserCraftsman()
        ? Padding(
          padding: const EdgeInsets.symmetric(horizontal: Margins.xlarge),
          child: Column(
            children: [
              const VerticalMargin.medium(),
              Text(type.text(context), textAlign: TextAlign.center),
              const VerticalMargin(margin: 48),
            ],
          ),
        )
        : const SizedBox();
  }
}

enum CompanyProfileSectionType { review, certificates, about, socials }

extension CompanyProfileSectionTypeValues on CompanyProfileSectionType {
  String text(BuildContext context) {
    switch (this) {
      case CompanyProfileSectionType.review:
        return S.of(context).companyRatingsEmptyStateInfrormation;
      case CompanyProfileSectionType.certificates:
        return S.of(context).companyCertificatesEmptyStateInfrormation;
      case CompanyProfileSectionType.about:
        return S.of(context).companyAboutEmptyStateInfrormation;
      case CompanyProfileSectionType.socials:
        return S.of(context).companySocialUrlsEmptyStateInfrormation;
    }
  }
}

class _QualificationInfoRow extends StatelessWidget {
  const _QualificationInfoRow({required this.label});

  final Widget label;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: Margins.small),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.check_circle_rounded,
            size: 24,
            color: context.doneColors.uiPositive,
          ),
          const HorizontalMargin.verySmall(),
          Flexible(
            child: DefaultTextStyle(
              style: Theme.of(context).textTheme.labelMedium!.copyWith(
                color: context.doneColors.typographyMediumContrast,
              ),
              child: label,
            ),
          ),
        ],
      ),
    );
  }
}
