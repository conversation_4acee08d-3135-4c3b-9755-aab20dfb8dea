import 'package:done_localizations/done_localizations.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

class SearchResultsView<T> extends StatelessWidget {
  const SearchResultsView({
    super.key,
    required this.isSearching,
    required this.searchCallback,
    required this.searchHintText,
    required this.resultsBuilder,
  });

  final bool isSearching;
  final String searchHintText;
  final Future<List<T>> searchCallback;
  final List<Widget> Function(List<T> results) resultsBuilder;

  @override
  Widget build(BuildContext context) {
    if (!isSearching)
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: Margins.large),
        child: Text(searchHintText),
      );

    return FutureBuilder<List<T>>(
      future: searchCallback,
      builder: (context, snapshot) {
        if (snapshot.data == null) return CenteredProgressIndicator();

        final searchResults = snapshot.data ?? [];

        return searchResults.isEmpty
            ? const EmptySearchResults()
            : Scrollbar(
              child: ListView(children: resultsBuilder(searchResults)),
            );
      },
    );
  }
}

class EmptySearchResults extends StatelessWidget {
  const EmptySearchResults({super.key});

  @override
  Widget build(BuildContext context) {
    return EmptyState(
      description: Column(
        children: [
          const Icon(Icons.search_off),
          Text(
            S.of(context).noAvailableSearchResults,
            style: Theme.of(context).textTheme.bodyMedium!.apply(
              color: context.doneColors.uiOnPrimary,
            ),
          ),
        ],
      ),
    );
  }
}
