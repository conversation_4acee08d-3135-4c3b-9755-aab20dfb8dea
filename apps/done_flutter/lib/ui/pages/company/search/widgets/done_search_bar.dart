import 'package:done_localizations/done_localizations.dart';
import 'package:done_image/done_image.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

class DoneSearchBar extends StatefulWidget {
  const DoneSearchBar({
    super.key,
    required this.onCancel,
    required this.isSearchActivated,
    required this.onFocus,
    required this.onQueryChanged,
  });

  final Function onCancel;
  final VoidCallback onFocus;
  final bool isSearchActivated;
  final ValueChanged<String> onQueryChanged;

  @override
  State<DoneSearchBar> createState() => _DoneSearchBarState();
}

class _DoneSearchBarState extends State<DoneSearchBar> {
  late final TextEditingController _searchController;
  late final FocusNode _focusNode;
  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController();
    _focusNode = FocusNode();
    if (widget.isSearchActivated) {
      _focusNode.requestFocus();
    }

    _focusNode.addListener(_onFocus);

    _searchController.addListener(_onQueryChanged);
  }

  void _onQueryChanged() {
    widget.onQueryChanged(_searchController.text.trim());
  }

  void _onFocus() {
    if (_focusNode.hasFocus) widget.onFocus();
    if (mounted) setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Expanded(
            child: SizedBox(
              height: 32,
              child: TextField(
                style: Theme.of(context).textTheme.bodyMedium,
                textCapitalization: TextCapitalization.sentences,
                enableSuggestions: false,
                focusNode: _focusNode,
                textInputAction: TextInputAction.search,
                controller: _searchController,
                decoration: InputDecoration(
                  filled: true,
                  isDense: true,
                  fillColor:
                      _focusNode.hasFocus
                          ? context.doneColors.uiPrimary
                          : context.doneColors.uiBg2WithOpacity,
                  prefixIcon: Image.asset(
                    ImageAssets.search,
                    scale: 2.5,
                    color: context.doneColors.typographyMediumContrast,
                  ),
                  contentPadding: EdgeInsets.zero,
                  border: const OutlineInputBorder(
                    borderRadius: BorderRadius.all(Radius.circular(8)),
                    borderSide: BorderSide.none,
                  ),
                  hintText: S.of(context).search,
                  suffixIcon:
                      _searchController.text.isNullOrEmpty()
                          ? null
                          : IconButton(
                            onPressed: () => _searchController.text = '',
                            icon: Icon(
                              Icons.close_rounded,
                              color: context.doneColors.typographyHightContrast,
                            ),
                          ),
                  hintStyle: Theme.of(context).textTheme.bodyMedium,
                ),
              ),
            ),
          ),
          if (widget.isSearchActivated) ...[
            const HorizontalMargin.medium(),
            SizedBox(
              height: 32,
              child: TextButton(
                child: Text(
                  S.of(context).cancel,
                  style: Theme.of(context).textTheme.labelMedium!.apply(
                    color: context.doneColors.purple,
                  ),
                ),
                onPressed: () => closeSearch(context),
              ),
            ),
          ],
        ],
      ),
    );
  }

  void closeSearch(BuildContext context) {
    widget.onCancel();
    _searchController.text = '';
    FocusScope.of(context).unfocus();
  }

  @override
  void dispose() {
    _searchController
      ..removeListener(_onQueryChanged)
      ..dispose();

    _focusNode
      ..removeListener(_onFocus)
      ..dispose();

    super.dispose();
  }
}
