import 'package:flutter/material.dart';

/// A mixin to provide searching properties and their update methods to any `StatefulWidget`
///
/// a `query` [String] and `isSearching` [bool] are the properties held by this mixin needed for searchable widgets
mixin SearchableMixin<T extends StatefulWidget> on State<T> {
  late String _query;
  late bool _isSearchActivated;

  @override
  void initState() {
    super.initState();
    _query = '';
    _isSearchActivated = false;
  }

  String get query => _query;
  bool get isSearching => _isSearchActivated;

  void updateQuery(String query) {
    setState(() => _query = query);
  }

  void setSearchingState(bool isSearching) {
    setState(() => _isSearchActivated = isSearching);
  }
}
