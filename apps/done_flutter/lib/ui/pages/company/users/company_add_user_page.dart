import 'package:cloud_functions/cloud_functions.dart';
import 'package:done_auth/done_auth.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_flutter/utils/extensions/models/company_user_role.dart';
import 'package:done_models/done_models.dart';
import 'package:done_router/done_router.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

class CompanyAddUserPage extends StatefulWidget {
  const CompanyAddUserPage({super.key});

  @override
  State<CompanyAddUserPage> createState() => _CompanyAddUserPageState();
}

class _CompanyAddUserPageState extends State<CompanyAddUserPage> {
  late final GlobalKey<FormState> _formKey;
  late final TextEditingController _firstNameController;
  late final TextEditingController _lastNameController;
  late final TextEditingController _phoneNumberController;
  late final TextEditingController _emailController;
  CompanyUserRole _role = CompanyUserRole.user;

  @override
  void initState() {
    _formKey = GlobalKey<FormState>();
    _firstNameController = TextEditingController();
    _lastNameController = TextEditingController();
    _phoneNumberController = TextEditingController();
    _emailController = TextEditingController();

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(S.of(context).addUser)),
      body: Scrollbar(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.symmetric(
              vertical: Margins.medium,
              horizontal: Margins.large,
            ),
            child: Form(
              key: _formKey,
              child: Column(
                children: [
                  ...<Widget>[
                    DoneFormFieldHolder(
                      title: S.of(context).firstName,
                      widget: TextFormField(
                        style: Theme.of(context).textTheme.bodyMedium!.apply(
                          color: context.doneColors.uiOnPrimary,
                        ),
                        controller: _firstNameController,
                        textCapitalization: TextCapitalization.words,
                        validator:
                            (value) => FormValidators.required(context, value),
                        decoration: DoneFormFieldHolder.inputDecoration(
                          textStyle: Theme.of(context).textTheme.bodyLarge,
                          context: context,
                        ),
                      ),
                    ),
                    DoneFormFieldHolder(
                      title: S.of(context).lastName,
                      widget: TextFormField(
                        style: Theme.of(context).textTheme.bodyMedium!.apply(
                          color: context.doneColors.uiOnPrimary,
                        ),
                        controller: _lastNameController,
                        textCapitalization: TextCapitalization.words,
                        validator:
                            (value) => FormValidators.required(context, value),
                        decoration: DoneFormFieldHolder.inputDecoration(
                          textStyle: Theme.of(context).textTheme.bodyLarge,
                          context: context,
                        ),
                      ),
                    ),
                    DoneFormFieldHolder(
                      title: S.of(context).profilePhoneNumber,
                      widget: TextFormField(
                        style: Theme.of(context).textTheme.bodyMedium!.apply(
                          color: context.doneColors.uiOnPrimary,
                        ),
                        controller: _phoneNumberController,
                        keyboardType: TextInputType.phone,
                        validator: FormValidators.compose(context, [
                          FormValidators.required,
                          _phoneValidator,
                        ]),
                        decoration: DoneFormFieldHolder.inputDecoration(
                          textStyle: Theme.of(context).textTheme.bodyLarge,
                          context: context,
                        ),
                      ),
                    ),
                    DoneFormFieldHolder(
                      title: S.of(context).profileEmail,
                      widget: TextFormField(
                        style: Theme.of(context).textTheme.bodyMedium!.apply(
                          color: context.doneColors.uiOnPrimary,
                        ),
                        controller: _emailController,
                        keyboardType: TextInputType.emailAddress,
                        validator: FormValidators.compose(context, [
                          FormValidators.required,
                          FormValidators.email,
                        ]),
                        decoration: DoneFormFieldHolder.inputDecoration(
                          textStyle: Theme.of(context).textTheme.bodyLarge,
                          context: context,
                        ),
                      ),
                    ),
                    Column(
                      children: [
                        DoneFormSingleSelection<CompanyUserRole>(
                          key: const Key("user-type"),
                          title: Text(S.of(context).userRole),
                          choices: const [
                            CompanyUserRole.admin,
                            CompanyUserRole.user,
                          ],
                          onChanged: ((value) {
                            setState(() {
                              _role = value;
                            });
                          }),
                          initialValue: CompanyUserRole.user,
                          choicesTitleConverter:
                              ((context, role) => role.localizedTitle(context)),
                          cancelText: S.of(context).cancel,
                        ),
                        const VerticalMargin.verySmall(),
                        Text(
                          S.of(context).userRoleDescription,
                          style: Theme.of(context).textTheme.bodyLarge,
                        ),
                      ],
                    ),
                  ].separatedBy(() => const VerticalMargin.medium()),
                  const VerticalMargin.xxxlarge(),
                  DoneAsyncAction(
                    action: _save,
                    builder:
                        (context, actionOrNull, isLoading) => DoneButton(
                          title:
                              isLoading
                                  ? const DoneAdaptiveLoadingIndicator(
                                    brightness: Brightness.dark,
                                  )
                                  : Text(
                                    S.of(context).save,
                                    style: Theme.of(
                                      context,
                                    ).textTheme.labelMedium!.apply(
                                      color: context.doneColors.uiPrimary,
                                    ),
                                  ),
                          onPressed: actionOrNull,
                        ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  String? _phoneValidator(BuildContext context, String? value) {
    return AuthFormValidators.isValidPhoneNumber(
      context,
      IsoCode.SE.name,
      value,
    );
  }

  Future<void> _save() async {
    if (!(_formKey.currentState?.validate() ?? false)) return;

    _formKey.currentState?.save();

    final user = AddUserToCompanyUserInput(
      firstName: _firstNameController.text.trim(),
      lastName: _lastNameController.text.trim(),
      phoneNumber:
          PhoneNumberFunctions.getPhoneNumber(
            _phoneNumberController.text,
          ).international,
      email: _emailController.text.trim(),
      companyId: context.authState.company!.id,
    );

    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final functions = CloudFunctions(
      functions: FirebaseFunctions.instanceFor(region: 'europe-west1'),
    );
    try {
      await functions.addUserToCompany(
        AddUserToCompanyParameters(user: user, role: _role),
      );
      if (mounted) context.router.pop();

      scaffoldMessenger.showSnackBar(
        SnackBar(
          backgroundColor: context.doneColors.uiPositive,
          content: Text(S.of(context).addUser),
        ),
      );
    } on FirebaseFunctionsException catch (e) {
      scaffoldMessenger.showSnackBar(
        SnackBar(
          backgroundColor: context.doneColors.uiNegative,
          content: Text(e.message ?? e.code),
        ),
      );
    }
  }
}
