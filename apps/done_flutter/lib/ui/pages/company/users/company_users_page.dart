import 'package:done_auth/done_auth.dart';
import 'package:done_flutter/core/router/routes/routes_definitions.dart';
import 'package:done_flutter/ui/widgets/user_avatar.dart';
import 'package:done_flutter/utils/extensions/models/company_user_role.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:done_localizations/done_localizations.dart';

class CompanyUsersPage extends StatelessWidget {
  const CompanyUsersPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(S.of(context).users),
        actions:
            (context.authState.isCompanyAdmin)
                ? [
                  IconButton(
                    onPressed: () {
                      const CompanyAddUserRoute().navigate(context);
                    },
                    icon: Icon(
                      Icons.add,
                      color: context.doneColors.uiOnPrimary,
                    ),
                  ),
                ]
                : null,
      ),
      body: FutureBuilder<List<_FetchedUserWithRole>>(
        future: context.authState.company?._sortedUsers() ?? Future.value([]),
        builder: (context, snapshot) {
          if (!snapshot.hasData && !snapshot.hasError)
            return CenteredProgressIndicator();
          if (snapshot.hasError)
            return Center(child: Text(S.of(context).errorOccuredTryAgain));

          return Scrollbar(
            child: PageContent(
              child: ListView(
                children:
                    snapshot.data!.map((e) {
                      return ListTile(
                        leading: SizedBox(
                          width: UserAvatar.standardRadius * 2,
                          height: UserAvatar.standardRadius * 2,
                          child: NonFetchingUserAvatar(user: e.user),
                        ),
                        title: Text(
                          e.user.fullNameWithFallback(context),
                          style: Theme.of(context).textTheme.bodyLarge,
                        ),
                        subtitle: Text(
                          e.user.phoneNumber ?? '',
                          style: Theme.of(context).textTheme.bodyMedium!.apply(
                            color: context.doneColors.uiOnPrimary,
                          ),
                        ),
                        trailing: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            DoneTinyBadge(
                              value: e.role.localizedTitle(context),
                            ),
                            const HorizontalMargin.small(),
                            Icon(
                              Icons.chevron_right,
                              color: context.doneColors.uiChevron,
                            ),
                          ],
                        ),
                        onTap: () {
                          final params = CompanyUserDetailRouteParams(
                            userId: e.user.documentReference.id,
                          );
                          CompanyUserDetailRoute(
                            params: params,
                          ).navigate(context);
                        },
                      );
                    }).toList(),
              ),
            ),
          );
        },
      ),
    );
  }
}

class _FetchedUserWithRole {
  _FetchedUserWithRole(this.user, this.role);

  final User user;
  final CompanyUserRole role;
}

extension SortedUsers on Company {
  Future<List<_FetchedUserWithRole>> _sortedUsers() async {
    final userEntries =
        (usersV2.values.toList()
          ..sort(((a, b) => a.role.index.compareTo(b.role.index))));

    // Fetch user data async. This gets into a FutureBuilder lower down.
    return Future.wait(
      userEntries.map(
        (entry) async => _FetchedUserWithRole(
          (await entry.reference.get()).data()!,
          entry.role,
        ),
      ),
    );
  }
}
