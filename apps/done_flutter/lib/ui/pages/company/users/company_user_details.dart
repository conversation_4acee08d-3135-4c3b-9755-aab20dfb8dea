import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:done_auth/done_auth.dart';
import 'package:done_database/done_database.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_flutter/utils/extensions/models/company_user_role.dart';
import 'package:done_models/done_models.dart';
import 'package:done_router/done_router.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:logger/logger.dart';

class CompanyUserDetailPage extends StatelessWidget {
  const CompanyUserDetailPage({super.key, required this.userRef});

  final DocumentReference<User> userRef;

  @override
  Widget build(BuildContext context) {
    final company = context.authState.company;
    if (company == null) {
      GetIt.instance<Logger>().e('No company in company user detail page');
      return const Scaffold();
    }

    return StreamBuilder<DocumentSnapshot<User>>(
      stream: userRef.snapshots(),
      builder: (context, snapshot) {
        if (!snapshot.hasData) return CenteredProgressIndicator();
        if (snapshot.data?.data() == null)
          return Center(child: Text(S.of(context).userIsRemoved));

        final user = snapshot.data!.data()!;
        final role = company.usersV2[user.documentReference.id]!.role;

        return Scaffold(
          appBar: AppBar(title: Text(user.fullNameWithFallback(context))),
          body: Scrollbar(
            child: PageContent(
              child: ListView(
                padding: const EdgeInsets.symmetric(
                  horizontal: Margins.large,
                  vertical: Margins.large,
                ),
                children: [
                  LabeledValue(
                    label: S.of(context).profilePhoneNumber,
                    value: user.phoneNumber,
                    showCopyButton: true,
                  ),
                  LabeledValue(
                    label: S.of(context).profileEmail,
                    value: user.email,
                    showCopyButton: true,
                  ),
                  // If user is admin or owner, show UI for changing role.
                  // Also, can't change role of owner(!)
                  if (isUserRoleEditingAllowed(
                    context.authState,
                    role,
                    user.documentReference.id,
                  )) ...[
                    Column(
                      children: [
                        DoneFormSingleSelection<CompanyUserRole>(
                          title: Text(S.of(context).userRole),
                          choices: const [
                            CompanyUserRole.admin,
                            CompanyUserRole.user,
                          ],
                          onChanged: ((newRole) {
                            _changeRole(context, newRole);
                          }),
                          initialValue: role,
                          choicesTitleConverter:
                              ((context, role) => role.localizedTitle(context)),
                          cancelText: S.of(context).cancel,
                        ),
                        const VerticalMargin.verySmall(),
                        Text(
                          S.of(context).userRoleDescription,
                          style: Theme.of(context).textTheme.bodyLarge,
                        ),
                      ],
                    ),
                    const VerticalMargin.medium(),
                    DoneButton(
                      title: Text(S.of(context).removeCompanyUserTitle),
                      style: DoneButtonStyle.negativeWithBackground,
                      onPressed: () async {
                        await removeUser(context, role);
                      },
                    ),
                  ]
                  // Else just show role as labeled value
                  else
                    LabeledValue(
                      label: S.of(context).userRole,
                      value: role.localizedTitle(context),
                    ),
                ].separatedBy(() => const VerticalMargin.large()),
              ),
            ),
          ),
        );
      },
    );
  }

  Future<void> removeUser(BuildContext context, CompanyUserRole role) async {
    final deleteConfirmed = await showConfirmationPopup(
      context: context,
      message: Text(S.of(context).removeCompanyUserConfirmationMessage),
      actionButtonStyle: DoneButtonStyle.negativeWithBackground,
      actionTitle: Text(S.of(context).removeCompanyUserTitle),
      cancelTitle: Text(S.of(context).cancel),
    );
    if (deleteConfirmed) {
      await Mutations.instance
          .company(context.authState.company!.reference)
          .removeUser(userRef.id);
      context.router.pop();
    }
  }

  Future<void> _changeRole(BuildContext context, CompanyUserRole role) async {
    await Mutations.instance
        .company(context.authState.company!.reference)
        .changeUserRole(userRef.id, role);
  }
}

bool isUserRoleEditingAllowed(
  AuthState authState,
  CompanyUserRole targetUserRole,
  String targetUserId,
) {
  if (targetUserId == authState.user?.documentReference.id)
    return false; // Should not be able to edit own role
  if (targetUserRole == CompanyUserRole.owner)
    return false; // Can't change role on or delete owner

  return [
    CompanyUserRole.owner,
    CompanyUserRole.admin,
  ].contains(authState.companyRole);
}
