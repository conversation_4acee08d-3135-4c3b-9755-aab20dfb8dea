import 'package:done_localizations/done_localizations.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

class DetailsSummarySection extends StatelessWidget {
  const DetailsSummarySection({
    super.key,
    required this.fullTaxDeduction,
    required this.amountIncludingVat,
    required this.totalNetAfterTaxDeductions,
    required this.centsRounding,
  });

  final int fullTaxDeduction;
  final double amountIncludingVat;
  final double totalNetAfterTaxDeductions;
  final double centsRounding;

  @override
  Widget build(BuildContext context) {
    return DoneDetailsSection(
      title: S.of(context).summary,
      titleStyle: Theme.of(context).textTheme.titleLarge!,
      children: [
        if (fullTaxDeduction > 0)
          ...[
            LabeledValue(
              label: S.of(context).totalIncludesTax,
              value: getPriceString(amountIncludingVat),
              direction: LabeledValueAxis.horizontal,
            ),
            LabeledValue(
              label: S.of(context).taxDeduction,
              value: "-${getPriceString(fullTaxDeduction)}",
              valueStyle: Theme.of(
                context,
              ).textTheme.labelMedium!.apply(color: context.doneColors.red),
              direction: LabeledValueAxis.horizontal,
            ),
            if (centsRounding != 0)
              LabeledValue(
                label: S.of(context).centsRounding,
                value: getPriceString(centsRounding),
                direction: LabeledValueAxis.horizontal,
              ),
            Container(height: 1, color: context.doneColors.uiBg2WithOpacity),
          ].separatedBy(() => const VerticalMargin.small()),
        LabeledValue(
          label: S.of(context).amountToPay,
          labelStyle: Theme.of(context).textTheme.labelMedium,
          value: getPriceString(totalNetAfterTaxDeductions),
          valueStyle: Theme.of(context).textTheme.titleLarge,
          direction: LabeledValueAxis.horizontal,
        ),
      ].separatedBy(() => const VerticalMargin.small()),
    );
  }
}
