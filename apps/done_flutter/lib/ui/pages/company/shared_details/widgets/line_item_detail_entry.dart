import 'package:done_localizations/done_localizations.dart';
import 'package:done_flutter/ui/pages/company/shared_generator/widgets/price_badge.dart';
import 'package:done_flutter/utils/extensions/enums/line_item.dart';
import 'package:done_flutter/utils/extensions/models/line_item.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

class LineItemDetailsEntry<L extends BaseLineItem> extends StatelessWidget {
  const LineItemDetailsEntry({
    super.key,
    required this.item,
    this.addVat = true,
    required this.options,
  });

  final L item;
  final bool
  addVat; // Quotes written excl VAT needs to add VAT before displaying to customer

  final CalculationOptions options;

  @override
  Widget build(BuildContext context) {
    final title = item.type.label(S.of(context));
    final subtitle = _subtitle(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        const VerticalMargin.small(),
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Expanded(
              child: Text(
                title,
                style: Theme.of(context).textTheme.bodyMedium!.apply(
                  color: context.doneColors.uiOnPrimary,
                ),
              ),
            ),
            if (item.shouldShowDeductionType &&
                item.deductionType != DeductionType.none) ...[
              DoneTinyBadge(value: item.deductionType.shortTitle(context)),
              const HorizontalMargin.medium(),
            ],
            _LineItemPriceSection(item: item, addVat: addVat, options: options),
          ],
        ),
        if (subtitle != null) ...[
          const VerticalMargin(margin: 6),
          Text(subtitle),
        ],
        const VerticalMargin.small(),
        Container(height: 1, color: context.doneColors.uiBg2WithOpacity),
      ],
    );
  }

  String? _subtitle(BuildContext context) =>
      item.description.isNullOrEmpty()
          ? item.article?.description
          : item.description;
}

class _LineItemPriceSection extends StatelessWidget {
  const _LineItemPriceSection({
    required this.item,
    required this.options,
    this.addVat = true,
  });

  final BaseLineItem item;
  final bool
  addVat; // Quotes written excl VAT needs to add VAT before displaying to customer

  final CalculationOptions options;
  @override
  Widget build(BuildContext context) {
    final shouldShowBadge = item.prepaid || item.isMissingPrice;
    if (shouldShowBadge) {
      final priceBadgeType =
          item.prepaid ? PriceBadgeType.prepaid : PriceBadgeType.missing;
      final unitDisplay = item.unitType.getTrimmedUnitDisplayName(
        S.of(context),
      );
      return Row(
        children: [
          PriceBadge(type: priceBadgeType),
          if (item.unitCount > 1)
            Text(
              " X ${item.unitCount.toInt()} $unitDisplay",
              style: Theme.of(context).textTheme.labelMedium,
            ),
        ],
      );
    }
    return Text(
      item.costBreakdownAfterDeductions(
        strings: S.of(context),
        addVAT: addVat,
        options: options,
      ),
      style: Theme.of(context).textTheme.labelMedium,
    );
  }
}
