import 'package:done_auth/done_auth.dart';

import 'package:done_database/done_database.dart';
import 'package:done_flutter/core/router/routes/project_routes.dart';

import 'package:done_localizations/done_localizations.dart';
import 'package:done_flutter/ui/widgets/reviews/rating_display.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';

class JobReviews extends StatelessWidget {
  const JobReviews({super.key});

  @override
  Widget build(BuildContext context) {
    final company = context.authState.user!.company!;
    return Scaffold(
      appBar: AppBar(
        titleSpacing: -8,
        title: Text(S.of(context).reviews),
        centerTitle: false,
        elevation: 0,
      ),
      body: Scrollbar(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: Margins.medium),
          child: StreamBuilder<List<CompanyReview>>(
            stream: GetIt.instance<CompanyRepository>().companyRatings(
              companyId: company.id,
              limit: 500,
            ),
            builder: (context, snapshot) {
              if (!snapshot.hasData) return CenteredProgressIndicator();
              if (snapshot.data!.isEmpty)
                return EmptyState(
                  title: Text(
                    S.of(context).companySettingsRatingsListEmptyStateTitle,
                  ),
                  description: Text(
                    S.of(context).companySettingsRatingsListEmptyStateBody,
                  ),
                );

              return ListView(
                primary: true,
                padding: const EdgeInsets.symmetric(vertical: 16),
                children: <Widget>[
                  ...snapshot.data!.map(
                    (review) => Column(
                      children: [
                        ListTile(
                          dense: true,
                          title: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                review.author.name,
                                style: Theme.of(context).textTheme.labelMedium,
                              ),
                              Text(
                                TimeFormatter.getShortHumanReadableDate(
                                  review.createTime.toDate(),
                                  context,
                                  asShortAsPossible: true,
                                ),
                                style: Theme.of(
                                  context,
                                ).textTheme.bodyMedium!.apply(
                                  color: context.doneColors.uiOnPrimary,
                                ),
                              ),
                            ],
                          ),
                          subtitle: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const VerticalMargin.small(),
                              if ((review.publicFeedback?.length ?? 0) > 0) ...[
                                Text(
                                  review.publicFeedback!,
                                  style: Theme.of(
                                    context,
                                  ).textTheme.bodyMedium!.apply(
                                    color: context.doneColors.uiOnPrimary,
                                  ),
                                ),
                                const VerticalMargin.small(),
                              ],
                              Row(
                                children: [
                                  RatingDisplay(
                                    rating: review.rating?.toDouble() ?? 0,
                                    size: 3,
                                  ),
                                  if ((review.images?.length ?? 0) > 0) ...[
                                    const HorizontalMargin.verySmall(),
                                    Icon(
                                      Icons.photo,
                                      color:
                                          Theme.of(context)
                                              .extension<DoneCustomColors>()!
                                              .typographyMediumContrast,
                                      size: 20,
                                    ),
                                  ],
                                ],
                              ),
                            ],
                          ),
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 8,
                          ),
                          trailing: Icon(
                            Icons.keyboard_arrow_right,
                            color: context.doneColors.uiChevron,
                          ),
                          onTap: () {
                            final params = ProjectDetailsRouteParams(
                              projectId: review.reference.id,
                            );
                            ProjectDetailsRoute(
                              params: params,
                            ).navigate(context);
                          },
                        ),
                        Divider(color: context.doneColors.uiBg1),
                      ],
                    ),
                  ),
                ],
              );
            },
          ),
        ),
      ),
    );
  }
}
