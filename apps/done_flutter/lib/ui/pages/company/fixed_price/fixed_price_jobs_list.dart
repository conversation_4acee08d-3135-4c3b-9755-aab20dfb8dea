import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:done_booking/done_booking.dart';
import 'package:done_database/done_database.dart';

import 'package:done_flutter/ui/pages/customer/fixed_price/fixed_price_list_item.dart';
import 'package:done_models/done_models.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';

class FixedPriceJobsListForService extends StatelessWidget {
  const FixedPriceJobsListForService({
    super.key,
    required this.serviceType,
    required this.fixedPriceJobs,
    required this.companyRef,
    required this.enableTogglingActiveFixedPriceJobs,
    required this.onListItemTap,
  });
  final ServiceType serviceType;
  final List<FixedPriceJob> fixedPriceJobs;
  final DocumentReference<Company> companyRef;
  final bool enableTogglingActiveFixedPriceJobs;
  final void Function(FixedPriceJob, BuildContext)? onListItemTap;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        titleSpacing: -8,
        elevation: 0,
        title: DoneAppBarTitle(
          title: Text(S.of(context).fixedPriceJobs),
          subtitle: Text(
            serviceType.title(context),
            style: Theme.of(context).textTheme.bodyMedium!.apply(
              color: context.doneColors.uiOnPrimary,
            ),
          ),
        ),
        centerTitle: false,
      ),
      body: StreamBuilder<Company>(
        stream: GetIt.instance<CompanyRepository>().company(companyRef.id),
        builder: (context, snapshot) {
          if (!snapshot.hasData) return CenteredProgressIndicator();
          final company = snapshot.data!;

          return Scrollbar(
            child: ListView.separated(
              primary: true,
              itemCount: fixedPriceJobs.length,
              itemBuilder:
                  (context, index) => FixedPriceListItem(
                    fixedPriceJob: fixedPriceJobs[index],
                    enableTogglingActiveFixedPriceJobs:
                        enableTogglingActiveFixedPriceJobs,
                    onTap: onListItemTap,
                    isActiveForCompany:
                        company.activeFixedPriceJobs?.contains(
                          fixedPriceJobs[index].fixedPriceJobId,
                        ) ??
                        false,
                    onToggle: (isChecked) {
                      if (isChecked ?? false) {
                        displayWarningAddToNewFixedPriceJob(
                          context,
                          company,
                          fixedPriceJobs[index].fixedPriceJobId,
                        );
                      } else {
                        displayWarningRemoveToFixedPriceJob(
                          context,
                          company,
                          fixedPriceJobs[index].fixedPriceJobId,
                          fixedPriceJobs[index].title,
                        );
                      }
                    },
                  ),
              separatorBuilder:
                  (_, __) => const Divider(indent: 80, height: Margins.large),
            ),
          );
        },
      ),
    );
  }

  void displayWarningRemoveToFixedPriceJob(
    BuildContext context,
    Company company,
    String fixedPriceJobId,
    String fixedPriceJobTitle,
  ) {
    showDialog<void>(
      barrierDismissible: false,
      context: context,
      builder:
          (BuildContext context) => DoneDialog(
            title: Text(
              S
                  .of(context)
                  .removeFixedPriceJobsCompanyProfileDialogTitle(
                    fixedPriceJobTitle,
                  ),
            ),
            content: [
              const VerticalMargin.medium(),
              DoneButton(
                style: DoneButtonStyle.secondary,
                title: Text(S.of(context).remove),
                onPressed: () {
                  company.removeAvailableFixedPriceList(fixedPriceJobId);
                  Navigator.pop(context);
                },
              ),
              const VerticalMargin(margin: 6),
              DoneButton(
                style: DoneButtonStyle.neutral,
                title: Text(S.of(context).cancel),
                onPressed: () => Navigator.pop(context),
              ),
            ],
          ),
    );
  }

  void displayWarningAddToNewFixedPriceJob(
    BuildContext context,
    Company company,
    String fixedPriceJobId,
  ) {
    showDialog<void>(
      barrierDismissible: false,
      context: context,
      builder:
          (BuildContext context) => DoneDialog(
            title: Text(S.of(context).addNewFixedPriceToCompanyTitle),
            content: [
              const VerticalMargin.medium(),
              DoneButton(
                style: DoneButtonStyle.secondary,
                title: Text(S.of(context).accept),
                onPressed: () {
                  company.addAvailableFixedPriceList(fixedPriceJobId);
                  Navigator.pop(context);
                },
              ),
              const VerticalMargin(margin: 6),
              DoneButton(
                style: DoneButtonStyle.neutral,
                title: Text(S.of(context).cancel),
                onPressed: () => Navigator.pop(context),
              ),
            ],
          ),
    );
  }
}
