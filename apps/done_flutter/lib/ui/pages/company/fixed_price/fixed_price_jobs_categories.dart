import 'package:done_auth/done_auth.dart';
import 'package:done_booking/done_booking.dart';

import 'package:done_database/done_database.dart';
import 'package:done_flutter/core/router/routes/routes_definitions.dart';

import 'package:done_localizations/done_localizations.dart';
import 'package:done_flutter/ui/pages/company/fixed_price/fixed_price_jobs_list.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';

class FixedPriceJobsCategoriesPage extends StatelessWidget {
  const FixedPriceJobsCategoriesPage({
    super.key,
    required this.enableTogglingActiveFixedPriceJobs,
    required this.onListItemTap,
    required this.onlyShowActiveForCompany,
  });

  final bool enableTogglingActiveFixedPriceJobs;
  final bool onlyShowActiveForCompany;
  final void Function(FixedPriceJob, BuildContext)? onListItemTap;

  @override
  Widget build(BuildContext context) {
    final companyRef = context.authState.user!.company!;
    return Scaffold(
      appBar: AppBar(
        titleSpacing: -8,
        elevation: 0,
        title: Text(S.of(context).fixedPriceJobs),
        centerTitle: false,
      ),
      body: StreamBuilder<Map<ServiceType, List<FixedPriceJob>>>(
        stream: GetIt.instance<FixedPriceJobsRepository>().fixedPriceJobs(
          forCompanyId: onlyShowActiveForCompany ? companyRef.id : null,
        ),
        builder: (context, snapshot) {
          if (!snapshot.hasData) return CenteredProgressIndicator();

          final activeJobs = snapshot.data!.entries.where(
            (element) => element.value.isNotEmpty,
          );

          if (activeJobs.isEmpty) {
            return EmptyState(
              description: Text(
                S.of(context).noActiveFixedPriceJobsForQuote,
                textAlign: TextAlign.center,
              ),
              children: [
                DoneButton(
                  title: Text(S.of(context).openSettings),
                  style: DoneButtonStyle.secondary,
                  onPressed: () {
                    final params = FixedPriceJobsRouteParams(
                      enableTogglingActiveFixedPriceJobs: true,
                    );
                    FixedPriceJobsRoute(params: params).navigate(context);
                  },
                ),
              ],
            );
          }

          return Scrollbar(
            child: ListView(
              children:
                  activeJobs.map<Widget>((entry) {
                    return _ServiceTypeListTile(
                      serviceType: entry.key,
                      onTap: () {
                        Navigator.of(context).push(
                          MaterialPageRoute<void>(
                            builder:
                                (context) => FixedPriceJobsListForService(
                                  serviceType: entry.key,
                                  onListItemTap: onListItemTap,
                                  enableTogglingActiveFixedPriceJobs:
                                      enableTogglingActiveFixedPriceJobs,
                                  companyRef: companyRef,
                                  fixedPriceJobs: entry.value,
                                ),
                          ),
                        );
                      },
                    );
                  }).toList(),
            ),
          );
        },
      ),
    );
  }
}

/// A tile that shows a clickable category tile with its title
class _ServiceTypeListTile extends StatelessWidget {
  const _ServiceTypeListTile({required this.serviceType, required this.onTap});
  final ServiceType serviceType;
  final VoidCallback? onTap;
  @override
  Widget build(BuildContext context) {
    final textStyle = Theme.of(context).textTheme.labelMedium!;
    final disabledTextStyle = textStyle.copyWith(
      color: Theme.of(context).disabledColor,
    );
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        ListTile(
          tileColor: context.doneColors.uiPrimary,
          title: Text(
            serviceType.title(context),
            style: onTap == null ? disabledTextStyle : textStyle,
          ),
          trailing: Icon(
            Icons.chevron_right,
            color: context.doneColors.uiChevron,
          ),
          onTap: onTap,
        ),
        // We only show categories that have fixed price jobs in this page
        // so, that's why the divider is added to Column instead of using ListView.separated
        Container(height: 1, color: context.doneColors.uiBg2),
      ],
    );
  }
}
