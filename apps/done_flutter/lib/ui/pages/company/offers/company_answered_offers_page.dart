import 'package:done_flutter/core/blocs/master_detail_cubit/index_selection_cubit.dart';
import 'package:done_database/done_database.dart';
import 'package:done_flutter/core/router/routes/routes_definitions.dart';

import 'package:done_localizations/done_localizations.dart';
import 'package:done_flutter/ui/pages/company/offers/company_offers_view.dart';
import 'package:done_flutter/ui/pages/company/offers/job_offer_detail.dart';
import 'package:done_flutter/ui/pages/master_detail/layout_adaptive_page.dart';
import 'package:done_image/done_image.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

class CompanyAnsweredOffersPage extends StatelessWidget {
  const CompanyAnsweredOffersPage({super.key});

  @override
  Widget build(BuildContext context) {
    final child = CompanyOffersView(
      onTap: (id, index) {
        final params = JobOfferPageRouteParams(
          jobOfferId: id,
          jobOfferType: OfferPageType.answered,
        );
        JobOfferPageRoute(params: params).navigate(context);
      },
      pageType: OfferPageType.answered,
    );
    final appBar = AppBar(
      title: Text(S.of(context).answeredRequests),
      titleSpacing: -8,
      elevation: 0,
      centerTitle: false,
    );

    return BlocProvider(
      create: (context) => IndexSelectionCubit(),
      child: LayoutAdaptivePage<IndexSelectionCubit, String?>(
        stateExtractor: (state) => state,
        appBar: appBar,
        masterViewBuilder: _masterViewBuilder,
        detailViewBuilder: _detailViewBuilder,
        child: child,
      ),
    );
  }

  Widget _masterViewBuilder(BuildContext context) {
    return CompanyOffersView<IndexSelectionCubit, String?>(
      onTap: (id, _) => _onSelectionTapped(context, id),
      stateExtractor: (state) => state,
      pageType: OfferPageType.answered,
    );
  }

  void _onSelectionTapped(BuildContext context, String id) {
    context.read<IndexSelectionCubit>().selectIndex(id);
  }

  Widget _detailViewBuilder(BuildContext context, String? id) {
    if (id != null) {
      return FutureBuilder<JobOffer>(
        key: PageStorageKey('Job offer details, id: $id'),
        future: GetIt.instance<JobOffersRepository>().fetchOffer(id),
        builder: (context, snapshot) {
          if (!snapshot.hasData) return const LoadingPage();
          return JobOfferDetail(jobOffer: snapshot.data!, showAppBar: true);
        },
      );
    }
    return DetailViewPlaceholder(asset: ImageAssets.request);
  }
}
