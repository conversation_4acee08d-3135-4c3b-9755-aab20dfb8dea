import 'package:done_flutter/core/blocs/master_detail_cubit/home_selection_cubit.dart';
import 'package:done_flutter/core/models/navigation_bar.dart';
import 'package:done_database/done_database.dart';
import 'package:done_flutter/core/router/routes/routes_definitions.dart';

import 'package:done_localizations/done_localizations.dart';
import 'package:done_flutter/ui/pages/company/offers/company_offers_view.dart';
import 'package:done_flutter/ui/pages/company/offers/job_offer_detail.dart';
import 'package:done_flutter/ui/pages/master_detail/layout_adaptive_page.dart';
import 'package:done_image/done_image.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

class CompanyPendingOffersPage extends StatelessWidget {
  const CompanyPendingOffersPage({super.key});

  @override
  Widget build(BuildContext context) {
    final child = CompanyOffersView(
      onTap: (id, index) {
        final params = JobOfferPageRouteParams(
          jobOfferId: id,
          jobOfferType: OfferPageType.pending,
        );
        JobOfferPageRoute(params: params).navigate(context);
      },
      pageType: OfferPageType.pending,
    );
    final appBar = AppBar(
      title: Text(S.of(context).jobOffers),
      actions: [
        DoneAppBarActionButton(
          title: Text(
            S.of(context).answeredRequests,
            style: Theme.of(
              context,
            ).textTheme.bodyMedium!.apply(color: context.doneColors.purple),
          ),
          onPressed: () => const AnsweredJobOffersRoute().navigate(context),
        ),
      ],
      elevation: 0,
    );

    return LayoutAdaptivePage<HomeSelectionCubit, Map<int, String?>>(
      stateExtractor: (state) => state[CompanyHomeTabs.offers.index],
      appBar: appBar,
      masterViewBuilder: _masterViewBuilder,
      detailViewBuilder: _detailViewBuilder,
      child: child,
    );
  }

  Widget _masterViewBuilder(BuildContext context) {
    return CompanyOffersView<HomeSelectionCubit, Map<int, String?>>(
      onTap: (id, _) => _onSelectionTapped(context, id),
      stateExtractor: (state) => state[CompanyHomeTabs.offers.index],
      pageType: OfferPageType.pending,
    );
  }

  void _onSelectionTapped(BuildContext context, String id) {
    context.read<HomeSelectionCubit>().updateSelectionFor(
      CompanyHomeTabs.offers,
      id,
    );
  }

  Widget _detailViewBuilder(BuildContext context, String? id) {
    if (id != null) {
      return FutureBuilder<JobOffer>(
        key: PageStorageKey('Job offer details, id: $id'),
        future: GetIt.instance<JobOffersRepository>().fetchOffer(id),
        builder: (context, snapshot) {
          if (!snapshot.hasData) return const LoadingPage();
          return JobOfferDetail(
            jobOffer: snapshot.data!,
            showAppBar: true,
            showControls: true,
          );
        },
      );
    }
    return DetailViewPlaceholder(asset: ImageAssets.request);
  }
}
