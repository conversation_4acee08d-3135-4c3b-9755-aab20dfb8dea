import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:done_booking/done_booking.dart';
import 'package:done_auth/done_auth.dart';
import 'package:done_flutter/core/router/routes/routes_definitions.dart';
import 'package:done_flutter/ui/pages/company/offers/job_offer_controls.dart';
import 'package:done_flutter/ui/pages/company/offers/job_offer_page.dart';
import 'package:done_flutter/ui/pages/job/widgets/craftsman_proceeds_view.dart';
import 'package:done_flutter/ui/pages/job/widgets/job_articles_section.dart';
import 'package:done_flutter/ui/pages/master_detail/layout_adaptive_app_bar.dart';
import 'package:done_flutter/ui/widgets/cards/rich_metadata_card.dart';
import 'package:done_flutter/ui/widgets/jobs/job_offer_map.dart';
import 'package:done_flutter/ui/widgets/jobs/job_offer_reply_chip.dart';
import 'package:done_flutter/utils/extensions/models/fixed_price_job_entries.dart';
import 'package:done_flutter/utils/extensions/models/job_offer_reply.dart';
import 'package:done_flutter/utils/first_build_callback.dart';
import 'package:done_image/done_image.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:done_database/done_database.dart';

class JobOfferDetail extends StatelessWidget {
  const JobOfferDetail({
    super.key,
    required this.jobOffer,
    this.showAppBar = false,
    this.showControls = false,
  });

  final JobOffer jobOffer;
  final bool showAppBar;
  final bool showControls;

  @override
  Widget build(BuildContext context) {
    final companyId = context.authState.user!.company!.id;
    final reply = jobOffer.replies[companyId];
    final jobOfferReply = reply?.answer ?? JobOfferReplyAnswer.unanswered;
    final proceeds = jobOffer.proceedsForCompany(companyId);

    return Scaffold(
      appBar:
          showAppBar
              ? LayoutAdaptiveAppBar(
                title: JobOfferAppBarTitle(jobOffer: jobOffer),
              )
              : null,
      extendBody: true,
      body: FirstBuildCallback(
        onFirstBuild: () => _updateLastSeenForJobOffer(context),
        child: Scrollbar(
          child: Opacity(
            opacity: jobOffer.status == JobOfferStatus.pending ? 1 : 0.4,
            child: PageContent(
              child: ListView(
                padding: EdgeInsets.zero,
                children: [
                  const VerticalMargin.large(),
                  if (proceeds != null && context.authState.isCompanyAdmin)
                    CraftsmanProceedsView(proceedsValue: proceeds.value! / 100),
                  _JobOfferHeader(jobOffer: jobOffer),
                  _JobOfferReplySection(
                    replyAnswer: jobOfferReply,
                    reply: reply,
                  ),
                  _JobOfferChips(jobOffer: jobOffer),
                  const VerticalMargin.medium(),
                  _JobOfferBookingDetails(jobOffer: jobOffer),
                  if (jobOffer.articles?.isNotEmpty ?? false)
                    JobArticlesSection(articles: jobOffer.articles!),
                  if (jobOffer.attachments?.isNotEmpty ?? false)
                    _JobOfferAttachments(attachments: jobOffer.attachments!),
                  if (jobOffer.location != null)
                    _JobOfferLocation(location: jobOffer.location!),
                  if (showControls) ...[
                    const VerticalMargin.large(),
                    JobOfferControls(jobOffer: jobOffer),
                  ],
                  const VerticalMargin(margin: 136),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// Updates last seen for the current company for the current job offer
  Future<void> _updateLastSeenForJobOffer(BuildContext context) async {
    final companyId = context.authState.user!.company!.id;

    // We only should update last seen in three cases
    // 1. lastSeenAt property of the job offer is null.
    // 2. The value (last seen at Timestamp) for the current company's id is null
    // 3. The last seen value for the current company's id is old (10 minutes have passed)
    final shouldUpdate =
        jobOffer.lastSeenAt == null ||
        jobOffer.lastSeenAt?[companyId] == null ||
        DateTime.now()
                .difference(jobOffer.lastSeenAt![companyId]!.toDate())
                .inMinutes >=
            10;

    if (shouldUpdate) {
      await jobOffer.documentReference.update({
        'lastSeenAt.$companyId': FieldValue.serverTimestamp(),
      });
    }
  }
}

class _JobOfferHeader extends StatelessWidget {
  const _JobOfferHeader({required this.jobOffer});
  final JobOffer jobOffer;
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: Margins.large),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ...[
            if (jobOffer.status == JobOfferStatus.resolved)
              HighlightedInfoView(
                text: Text(
                  S.of(context).alreadyMatched,
                  style: Theme.of(context).textTheme.bodyMedium!.apply(
                    color: context.doneColors.uiOnPrimary,
                  ),
                ),
                backgroundColor: context.doneColors.uiNegative,
              ),
            if (jobOffer.tags?.contains(JobTag.express) ?? false)
              const ExpressJobCard(),
            if (jobOffer.fixedPriceJobs?.isNotEmpty ?? false)
              ...jobOffer.fixedPriceJobs!.widgets,
          ].separatedBy(VerticalMargin.small),
          if (!jobOffer.description.isNullOrEmpty())
            Text(
              jobOffer.description!,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          if (jobOffer.images != null && jobOffer.images!.isNotEmpty)
            ImageList<UrlsPhotoDataSource>.network(
              networkImages: jobOffer.images,
              onTap: (source) {
                final params = JobOfferPhotoRouteParams(
                  jobOfferId: jobOffer.id,
                  initialIndex: source.initialIndex,
                );
                JobOfferPhotoRoute(
                  params: params,
                  extra: jobOffer.images,
                ).navigate(context);
              },
            ),
        ].separatedBy(() => const VerticalMargin.small()),
      ),
    );
  }
}

class _JobOfferReplySection extends StatelessWidget {
  const _JobOfferReplySection({required this.replyAnswer, required this.reply});

  final JobOfferReplyAnswer replyAnswer;
  final JobOfferReply? reply;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: Margins.large),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (replyAnswer != JobOfferReplyAnswer.unanswered)
            JobbOfferReplyChip(jobOfferReply: replyAnswer),
          if (replyAnswer.replyLabel(context) != null)
            Padding(
              padding: const EdgeInsets.only(bottom: Margins.small),
              child: DoneKeyValuePair(
                label: replyAnswer.replyLabel(context)!,
                value: replyAnswer.replyValue(context, reply!)!,
              ),
            ),
          const VerticalMargin.medium(),
        ],
      ),
    );
  }
}

class _JobOfferBookingDetails extends StatelessWidget {
  const _JobOfferBookingDetails({required this.jobOffer});
  final JobOffer jobOffer;
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: Margins.large),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          LabeledValue(
            label: S.of(context).submitted,
            value:
                TimeFormatter(jobOffer.createTime.toDate(), context).toHuman(),
          ),
          if (jobOffer.preferredCallTime != null)
            LabeledValue(
              label: S.of(context).chatTalkTime,
              value: jobOffer.preferredCallTime,
            ),
          if (jobOffer.preferredStartDate != null)
            LabeledValue(
              label: S.of(context).chatDesiredProjectStart,
              value: jobOffer.preferredStartDate,
            ),
          if (jobOffer.location?.subLocality != null)
            LabeledValue(
              label: S.of(context).area,
              value: jobOffer.location!.subLocality,
            ),
          if (jobOffer.budget != null)
            LabeledValue(
              label: S.of(context).customerBudget,
              value: jobOffer.budget,
            ),
          if (context.authState.isUserCraftsman())
            LabeledValue(
              label: S.of(context).jobsReferenceNumber,
              value: jobOffer.referenceNumber,
              showCopyButton: true,
            ),
        ].separatedBy(
          () => Divider(height: Margins.large, color: context.doneColors.uiBg1),
        ),
      ),
    );
  }
}

class _JobOfferChips extends StatelessWidget {
  const _JobOfferChips({required this.jobOffer});
  final JobOffer jobOffer;
  @override
  Widget build(BuildContext context) {
    final services = jobOffer.services ?? [];
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: Margins.large),
      child: Wrap(
        spacing: Margins.small,
        runSpacing: Margins.small,
        children: [
          if (jobOffer.merchant != null)
            _PartnershipLogoChip(merchantId: jobOffer.merchant!.id),
          ...services
              .map(serviceTypeFrom)
              .whereType<ServiceType>()
              .map<Widget>(
                (service) => Chip(
                  backgroundColor: context.doneColors.uiBg2,
                  label: Text(
                    service.title(context),
                    style: Theme.of(context).textTheme.labelMedium!.apply(
                      color: context.doneColors.uiOnPrimary,
                    ),
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(Margins.small),
                  ),
                  padding: EdgeInsets.zero,
                ),
              ),
        ],
      ),
    );
  }
}

class _PartnershipLogoChip extends StatelessWidget {
  const _PartnershipLogoChip({required this.merchantId});

  final String merchantId;

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<Partnership>(
      stream: GetIt.I<PartnershipRepository>().partnership(merchantId),
      builder: (context, snapshot) {
        if (!snapshot.hasData) return const SizedBox.shrink();

        final partnership = snapshot.data!;
        return Chip(
          backgroundColor: context.doneColors.uiBg2,
          label:
              partnership.logo != null
                  ? CachedNetworkImage(
                    imageUrl: partnership.logo!,
                    height: 20,
                    fit: BoxFit.contain,
                  )
                  : Text(
                    partnership.name,
                    style: Theme.of(context).textTheme.labelMedium,
                  ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(Margins.small),
          ),
          padding: EdgeInsets.zero,
        );
      },
    );
  }
}

class _JobOfferLocation extends StatelessWidget {
  const _JobOfferLocation({required this.location});
  final DoneLocation location;
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: Margins.medium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          const VerticalMargin.medium(),
          Card(
            clipBehavior: Clip.antiAlias,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(Margins.medium),
            ),
            elevation: 0,
            child: JobOfferMap(location: location, height: 160),
          ),
        ],
      ),
    );
  }
}

class _JobOfferAttachments extends StatelessWidget {
  const _JobOfferAttachments({required this.attachments});
  final List<RichMetadata> attachments;
  @override
  Widget build(BuildContext context) {
    final visibleAttachments = attachments.where(
      (attachment) => attachment.title != null,
    );
    if (visibleAttachments.isEmpty) return const SizedBox.shrink();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Padding(
          padding: EdgeInsets.symmetric(horizontal: Margins.large),
          child: Divider(),
        ),
        ColoredBox(
          color: context.doneColors.paleHighlightYellow,
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: Margins.large,
              vertical: Margins.medium,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  S.of(context).companyNotesTitle,
                  style: Theme.of(context).textTheme.bodyMedium!.apply(
                    color: context.doneColors.darkGoldenRod,
                  ),
                ),
                const VerticalMargin.small(),
                ...visibleAttachments.map(
                  (attachment) => RichMetadataCard(
                    borderColor: context.doneColors.yellow,
                    title: attachment.title!,
                    url: attachment.url,
                    description: attachment.description,
                    image: attachment.image,
                    logo: attachment.logo,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
