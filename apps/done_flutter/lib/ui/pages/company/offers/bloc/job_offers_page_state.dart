part of 'job_offers_page_cubit.dart';

enum JobOffersPageStatus { initial, empty, loaded }

@immutable
class JobOffersPageState {
  const JobOffersPageState({
    this.offerIndex = 0,
    required this.offerId,
    required this.type,
    required this.status,
    required this.offers,
  });

  final int offerIndex;
  final String offerId;
  final OfferPageType type;
  final JobOffersPageStatus status;

  final List<JobOffer> offers;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    final listEquals = const DeepCollectionEquality().equals;

    return other is JobOffersPageState &&
        other.offerIndex == offerIndex &&
        other.offerId == offerId &&
        other.type == type &&
        other.status == status &&
        listEquals(other.offers, offers);
  }

  @override
  int get hashCode {
    return offerIndex.hashCode ^
        offerId.hashCode ^
        type.hashCode ^
        status.hashCode ^
        offers.hashCode;
  }

  JobOffersPageState copyWith({
    String? offerId,
    int? offerIndex,
    List<JobOffer>? offers,
    OfferPageType? type,
    JobOffersPageStatus? status,
  }) {
    return JobOffersPageState(
      offerId: offerId ?? this.offerId,
      offers: offers ?? this.offers,
      offerIndex: offerIndex ?? this.offerIndex,
      type: type ?? this.type,
      status: status ?? this.status,
    );
  }
}
