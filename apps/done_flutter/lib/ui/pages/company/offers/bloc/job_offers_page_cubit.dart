import 'dart:async';

import 'package:collection/collection.dart';
import 'package:done_auth/done_auth.dart';
import 'package:done_database/done_database.dart';
import 'package:done_flutter/ui/pages/company/offers/company_offers_view.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

part 'job_offers_page_state.dart';

/// This cubit holds the state of the current job offer being viewed by the craftsman
/// For example, for chat page and project page it holds the job id for each tab
///
/// It exists to preserve this information throughout app rebuilds
class CurrentJobOfferCubit extends Cubit<JobOffersPageState> {
  CurrentJobOfferCubit({
    required BuildContext context,
    required JobOffersPageState initialState,
  }) : super(initialState) {
    _offersStream = GetIt.instance<JobOffersRepository>()
        .companyOffers(context.authState.user!.company!.id)
        .listen((offers) {
          updateOffers(context, offers);
        });
  }
  late final StreamSubscription<List<JobOffer>> _offersStream;

  void updateOffers(BuildContext context, List<JobOffer> offers) {
    final currentJobOffer = state.offerId;
    final jobOffers = CompanyOffersView.filterJobs(context, offers, state.type);

    if (jobOffers.isEmpty)
      return emit(state.copyWith(status: JobOffersPageStatus.empty));
    final isCurrentOfferAvailable = _checkCurrentOfferAvailability(
      context,
      currentJobOffer,
      jobOffers,
    );
    _updateCurrentState(currentJobOffer, jobOffers, isCurrentOfferAvailable);
  }

  void updateIndex(int index) {
    final currentId = state.offers[index].id;
    emit(state.copyWith(offerIndex: index, offerId: currentId));
  }

  /// This method checks if the offer the user was looking at still exists
  ///
  /// It should be used with offers live data to check on every new list of job offers
  /// If job offer is not available, it notifies the user accordingly
  /// It returns the availability status of current job
  bool _checkCurrentOfferAvailability(
    BuildContext context,
    String? currentOfferId,
    List<JobOffer> jobOffers,
  ) {
    if (currentOfferId == null) return false;

    final isCurrentJobOfferAvailable =
        jobOffers.firstWhereOrNull((offer) => offer.id == currentOfferId) !=
        null;

    if (!isCurrentJobOfferAvailable) {
      SchedulerBinding.instance.addPostFrameCallback((_) async {
        // The updated job offer that got removed
        final updatedOffer = await GetIt.instance<JobOffersRepository>()
            .fetchOffer(currentOfferId);

        final currentCompany = context.authState.getUserCompany();

        // Checking if the company looking at the offer is not the one that accepted it
        // Otherwise notify the user that the job offer is no longer available
        if (updatedOffer.answerfromCompany(currentCompany!.id) == null &&
            context.mounted) {
          await showDialog<void>(
            builder:
                (context) => DoneDialog(
                  title: Text(S.of(context).requestNoLongerAvailable),
                  content: [
                    DoneButton(
                      style: DoneButtonStyle.secondary,
                      title: Text(S.of(context).dismiss),
                      onPressed: () => Navigator.of(context).pop(),
                    ),
                  ],
                ),
            context: context,
          );
        }
      });
    }
    return isCurrentJobOfferAvailable;
  }

  /// This method updates the current cubit state
  ///
  /// It should be used with job offers live data to update index on every new list of job offers
  /// There are three cases for updating index
  /// 1.  An initial job offer id (index) from deep-linking.
  /// 2. Job offers stream changes we want to keep the user on the same job offer (related issue: https://github.com/flutter/flutter/issues/58959)
  /// 3. The user is on the last item and it gets removed
  /// (PageController handles last case natively but we have to update the[ValueNotifier] as it doesn't get updated).
  ///  TODO : Extract this logic into a mixin or reusable component and test it well
  void _updateCurrentState(
    String? currentOfferId,
    List<JobOffer> jobOffers,
    bool isCurrentJobAvailable,
  ) {
    var updatedPageIndex = state.offerIndex;

    // The next code block is used to check if the current job offer (that the user is looking at)  still exists
    // if it does, [_currentPageNotifier] updates to its index
    if (isCurrentJobAvailable) {
      final offerIndex = jobOffers.indexWhere(
        (element) => element.id == currentOfferId,
      );
      if (offerIndex >= 0) {
        updatedPageIndex = offerIndex;
      }
    }
    // Check if currentPage is greater than list size
    // if so, we use the last job offer index and update value notifier accordingly
    updatedPageIndex =
        updatedPageIndex >= jobOffers.length
            ? jobOffers.length - 1
            : updatedPageIndex;
    emit(
      JobOffersPageState(
        offerId: currentOfferId ?? '',
        offers: jobOffers,
        offerIndex: updatedPageIndex,
        type: state.type,
        status: JobOffersPageStatus.loaded,
      ),
    );
  }

  @override
  Future<void> close() {
    _offersStream.cancel();
    return super.close();
  }
}
