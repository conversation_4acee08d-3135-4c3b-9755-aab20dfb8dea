import 'package:collection/collection.dart';
import 'package:done_auth/done_auth.dart';
import 'package:done_database/done_database.dart';
import 'package:done_flutter/utils/company_tos_checker_util.dart';

import 'package:done_localizations/done_localizations.dart';
import 'package:done_flutter/ui/widgets/jobs/job_offer_row.dart';
import 'package:done_flutter/utils/version_check_util.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

class CompanyOffersView<B extends BlocBase<T>, T extends Object?>
    extends StatelessWidget {
  const CompanyOffersView({
    super.key,
    required this.pageType,
    required this.onTap,
    this.stateExtractor,
  });
  final OfferPageType pageType;
  final void Function(String, int) onTap;

  /// A function to extract the job offer id from the [B] bloc's state, used to update UI on state changes
  final String? Function(T)? stateExtractor;

  @override
  Widget build(BuildContext context) {
    final user = context.authState.user;
    final offersRepo = GetIt.instance<JobOffersRepository>();
    return StreamBuilder<List<JobOffer>>(
      stream: offersRepo.companyOffers(user!.company!.id),
      builder: (context, jobsSnapshot) {
        if (!jobsSnapshot.hasData) return CenteredProgressIndicator();
        if (jobsSnapshot.data!.isEmpty)
          return EmptyState(
            title: Text(pageType.emptyStateTitle(context)),
            description: Text(pageType.emptyStateBody(context)),
          );

        final offerList =
            filterJobs(
              context,
              jobsSnapshot.data!,
              pageType,
            ).mapIndexed<Widget>((index, offer) {
              final extractor = stateExtractor;
              if (extractor == null)
                return _jobOfferRowBuilder(offer: offer, index: index);

              return BlocBuilder<B, T>(
                builder: (context, state) {
                  final currentId = extractor(state);
                  final isSelected = currentId == offer.id;
                  return _jobOfferRowBuilder(
                    offer: offer,
                    index: index,
                    isSelected: isSelected,
                  );
                },
              );
            }).toList();

        return (offerList.isEmpty)
            ? EmptyState(
              title: Text(pageType.emptyStateTitle(context)),
              description: Text(pageType.emptyStateBody(context)),
            )
            : Scrollbar(
              child: ListView(
                primary: true,
                key: const PageStorageKey('companyOffersListView'),
                children: <Widget>[
                  const VerticalMargin.small(),
                  if (kShowVersionUtils) const CollapsableSoftUpdateBanner(),
                  const NewTermsOfServiceWarningBanner(),
                  ...offerList,
                  const VerticalMargin(
                    margin: 90,
                  ), // Compensate for chat bubble
                ],
              ),
            );
      },
    );
  }

  Widget _jobOfferRowBuilder({
    required JobOffer offer,
    required int index,
    bool isSelected = false,
  }) {
    return JobOfferRow(
      onTap: onTap,
      jobOffer: offer,
      jobOfferType: pageType,
      index: index,
      isSelected: isSelected,
    );
  }

  static List<JobOffer> filterJobs(
    BuildContext context,
    List<JobOffer> offers,
    OfferPageType pageType,
  ) {
    final user = context.authState.user;

    return offers.where((offer) {
      if (offer.answerfromCompany(user!.company!.id) != null)
        return pageType == OfferPageType.answered;

      return pageType != OfferPageType.answered;
    }).toList();
  }
}

enum OfferPageType { pending, answered }

extension JobsTabValues on OfferPageType {
  String emptyStateTitle(BuildContext context) {
    switch (this) {
      case OfferPageType.pending:
        return S.of(context).companyJobsTabIncomingTitle;
      case OfferPageType.answered:
        return S.of(context).noAnsweredJobOffersTitle;
    }
  }

  String emptyStateBody(BuildContext context) {
    switch (this) {
      case OfferPageType.pending:
        return S.of(context).companyJobsTabIncomingBody;
      case OfferPageType.answered:
        return "";
    }
  }
}
