import 'package:collection/collection.dart';
import 'package:done_flutter/ui/pages/company/offers/company_offers_view.dart';
import 'package:done_flutter/ui/pages/company/offers/bloc/job_offers_page_cubit.dart';
import 'package:done_flutter/ui/pages/company/offers/job_offer_controls.dart';
import 'package:done_flutter/ui/pages/company/offers/job_offer_detail.dart';
import 'package:done_flutter/utils/first_build_callback.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class JobOfferPage extends StatelessWidget {
  const JobOfferPage({required this.offerType, required this.offerId});

  final OfferPageType offerType;
  final String offerId;

  @override
  Widget build(BuildContext context) {
    return BlocProvider<CurrentJobOfferCubit>(
      create:
          (context) => CurrentJobOfferCubit(
            context: context,
            initialState: JobOffersPageState(
              offerId: offerId,
              type: offerType,
              status: JobOffersPageStatus.initial,
              offers: const [],
            ),
          ),
      child: const _JobOffersPageView(),
    );
  }
}

class _JobOfferPageEmptyState extends StatelessWidget {
  const _JobOfferPageEmptyState();

  @override
  Widget build(BuildContext context) {
    return EmptyState(
      title: Text(OfferPageType.pending.emptyStateTitle(context)),
      description: Text(OfferPageType.pending.emptyStateBody(context)),
    );
  }
}

class _JobOffersPageView extends StatefulWidget {
  const _JobOffersPageView();

  @override
  State<_JobOffersPageView> createState() => _JobOffersPageViewState();
}

class _JobOffersPageViewState extends State<_JobOffersPageView> {
  late final PageController _controller;

  @override
  void initState() {
    super.initState();
    _controller = PageController();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<CurrentJobOfferCubit, JobOffersPageState>(
      listenWhen:
          (previous, current) =>
              previous.offers.length != current.offers.length,
      listener: (context, state) {
        final index = state.offerIndex;

        SchedulerBinding.instance.addPostFrameCallback((timeStamp) {
          if (_controller.hasClients) _controller.jumpToPage(index);
        });
      },
      child: BlocBuilder<CurrentJobOfferCubit, JobOffersPageState>(
        buildWhen: (previous, current) {
          final listEquals = const DeepCollectionEquality().equals;
          return previous.status != current.status ||
              !listEquals(previous.offers, current.offers);
        },
        builder: (context, state) {
          if (state.status == JobOffersPageStatus.initial)
            return CenteredProgressIndicator();
          final jobOffers = state.offers;
          final isEmpty = state.status == JobOffersPageStatus.empty;
          return Scaffold(
            resizeToAvoidBottomInset: false,
            appBar: AppBar(
              centerTitle: false,
              elevation: 0,
              titleSpacing: -8,
              title:
                  isEmpty
                      ? const SizedBox()
                      : BlocBuilder<CurrentJobOfferCubit, JobOffersPageState>(
                        builder: (context, state) {
                          final currentOffer = state.offers[state.offerIndex];
                          return JobOfferAppBarTitle(jobOffer: currentOffer);
                        },
                      ),
            ),
            body: SafeArea(
              child:
                  isEmpty
                      ? FirstBuildCallback(
                        onFirstBuild: () async {
                          final navigator = Navigator.of(context);
                          if (navigator.canPop()) navigator.pop();
                        },
                        child: const _JobOfferPageEmptyState(),
                      )
                      : Stack(
                        children: <Widget>[
                          Padding(
                            padding: const EdgeInsets.only(bottom: 20),
                            child: PageView.builder(
                              key: const PageStorageKey(
                                'companyOfferPagesPageView',
                              ),
                              itemCount: jobOffers.length,
                              itemBuilder:
                                  (context, index) => JobOfferDetail(
                                    jobOffer: jobOffers[index],
                                  ),
                              onPageChanged: (index) {
                                context
                                    .read<CurrentJobOfferCubit>()
                                    .updateIndex(index);
                              },
                              controller: _controller,
                            ),
                          ),
                          if (jobOffers.isNotEmpty) ...[
                            Align(
                              alignment: Alignment.bottomCenter,
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                  vertical: Margins.small,
                                ),
                                child: _PageViewCircleIndicator(
                                  itemCount: jobOffers.length,
                                ),
                              ),
                            ),
                            BlocBuilder<
                              CurrentJobOfferCubit,
                              JobOffersPageState
                            >(
                              builder: (context, state) {
                                final currentOffer =
                                    state.offers[state.offerIndex];
                                return Align(
                                  alignment: Alignment.bottomCenter,
                                  child: Padding(
                                    padding: const EdgeInsets.only(
                                      bottom: Margins.large,
                                    ),
                                    child: JobOfferControls(
                                      jobOffer: currentOffer,
                                    ),
                                  ),
                                );
                              },
                            ),
                          ],
                        ],
                      ),
            ),
          );
        },
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}

class JobOfferAppBarTitle extends StatelessWidget {
  const JobOfferAppBarTitle({super.key, required this.jobOffer});
  final JobOffer jobOffer;
  @override
  Widget build(BuildContext context) {
    return DoneAppBarTitle(
      title: Text(S.of(context).jobOfferTitle),
      subtitle:
          (jobOffer.locationString != null)
              ? Text(
                jobOffer.locationString!,
                style: Theme.of(context).textTheme.bodyMedium!.apply(
                  color: context.doneColors.uiOnPrimary,
                ),
              )
              : null,
    );
  }
}

class _PageViewCircleIndicator extends StatelessWidget {
  const _PageViewCircleIndicator({required this.itemCount});
  final int itemCount;
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CurrentJobOfferCubit, JobOffersPageState>(
      buildWhen: (prev, current) => prev.offerIndex != current.offerIndex,
      builder: (context, state) {
        return PageViewDotIndicator(
          count: itemCount,
          currentItem: state.offerIndex,
          unselectedColor: context.doneColors.uiBg2,
          selectedColor: context.doneColors.uiBlack,
        );
      },
    );
  }
}
