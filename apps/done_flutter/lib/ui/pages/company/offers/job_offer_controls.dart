import 'package:done_analytics/done_analytics.dart';
import 'package:done_auth/done_auth.dart';
import 'package:done_booking/done_booking.dart';
import 'package:done_database/done_database.dart';
import 'package:done_flutter/core/router/routes/project_routes.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_flutter/ui/widgets/accept_decline_controls.dart';
import 'package:done_flutter/utils/ask_reason.dart';
import 'package:done_flutter/utils/extensions/enums/job_offer.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:overlay_support/overlay_support.dart';

class JobOfferControls extends StatelessWidget {
  const JobOfferControls({super.key, required this.jobOffer});

  final JobOffer jobOffer;
  @override
  Widget build(BuildContext context) {
    return Visibility(
      maintainSize: true,
      maintainState: true,
      maintainAnimation: true,
      visible: jobOffer.status == JobOfferStatus.pending,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: AcceptDeclineControls(
          onAccept: () => _displayApproveAcceptDialog(context),
          onConditionallyAccept: () => _conditionallyAccept(context),
          onDecline: () => _askDeclineReason(context),
        ),
      ),
    );
  }

  Future<void> _displayApproveAcceptDialog(BuildContext context) {
    if (jobOffer.tags?.contains(JobTag.express) ?? false) {
      return showDialog(
        context: context,
        builder:
            (context) => ExpressInfoDialog(
              onConfirm: () => _accept(context),
              confirmTitle: S.of(context).accept,
            ),
      );
    }

    return showAdaptivePopup(
      context: context,
      builder:
          (BuildContext context) => DonePopup(
            content: <Widget>[
              DoneButton(
                style: DoneButtonStyle.secondary,
                title: Text(S.of(context).acceptAndOpen),
                onPressed: () => _accept(context, shouldOpenChat: true),
              ),
              DoneButton(
                style: DoneButtonStyle.positive,
                title: Text(S.of(context).accept),
                onPressed: () => _accept(context),
              ),
              DoneButton(
                style: DoneButtonStyle.neutral,
                title: Text(S.of(context).cancel),
                onPressed: () async {
                  Navigator.of(context).pop();
                },
              ),
            ].separatedBy(() => const VerticalMargin.small()),
          ),
    );
  }

  Future<void> _accept(
    BuildContext context, {
    bool shouldOpenChat = false,
  }) async {
    final user = context.authState.user;
    await EventLogger.instance.logEvent('job_offer_accepted', {
      'open_chat': shouldOpenChat.toString(),
    });
    await Mutations.instance.jobOffer(jobOffer.documentReference).accept(user!);

    showOverlayNotification(
      (context) => DoneNotification(
        backgroundColor: context.doneColors.uiPositive,
        title: Text(S.of(context).jobOfferAcceptedTitle),
        body: Text(S.of(context).jobOfferAcceptedMessage),
        onDismissed:
            () => OverlaySupportEntry.of(context)!.dismiss(animate: false),
      ),
      duration: const Duration(seconds: 4),
    );

    if (shouldOpenChat) {
      final params = ChatPageRouteParams(projectId: jobOffer.id);
      ChatPageRoute(params: params).navigate(context);
    }

    Navigator.of(context).pop();
  }

  Future<void> _conditionallyAccept(BuildContext context) async {
    final result = await askReason<JobOfferAcceptCondition>(
      context: context,
      reasons: JobOfferAcceptCondition.values,
      title: Text(S.of(context).conditionallyAccept),
      reasonTitleBuilder: (context, reason) => Text(reason.title(context)),
      actionTitle: S.of(context).offerSend,
    );
    if (result == null) return;
    await _sendConditionallyAcceptReply(
      context,
      result.reason,
      result.otherText,
    );
  }

  Future<void> _sendConditionallyAcceptReply(
    BuildContext context,
    JobOfferAcceptCondition condition, [
    String? otherReason,
  ]) async {
    final user = context.authState.user;

    await Mutations.instance
        .jobOffer(jobOffer.documentReference)
        .acceptConditionally(user!, condition, otherText: otherReason);

    await showDialog<void>(
      context: context,
      builder:
          (BuildContext context) => DoneDialog(
            title: Text(S.of(context).conditionallyAccepted),
            content: [
              Text(S.of(context).conditionallyAcceptPostInfoText),
              const VerticalMargin.medium(),
              DoneButton(
                style: DoneButtonStyle.secondary,
                title: Text(S.of(context).dismiss),
                onPressed: () async {
                  Navigator.of(context).pop();
                },
              ),
            ],
          ),
    );
  }

  Future<void> _askDeclineReason(BuildContext context) async {
    final result = await askReason<JobOfferDeclineReason>(
      context: context,
      reasons: JobOfferDeclineReason.values,
      title: Text(S.of(context).jobOfferDeclinedTitle),
      reasonTitleBuilder: (context, reason) => Text(reason.title(context)),
      actionTitle: S.of(context).offerSend,
    );
    if (result == null) return;
    final user = context.authState.user;
    await Mutations.instance
        .jobOffer(jobOffer.documentReference)
        .decline(user!, result.reason, otherText: result.otherText);
  }
}
