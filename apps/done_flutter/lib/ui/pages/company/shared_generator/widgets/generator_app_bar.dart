import 'package:done_localizations/done_localizations.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

typedef PriceInclVatSettingsChange = ValueChanged<bool>;

/// Prevents exiting and shows a dialog if there were changes made in the form
class GeneratorAppBar extends StatelessWidget implements PreferredSizeWidget {
  const GeneratorAppBar({
    super.key,
    required this.onCancel,
    required this.title,
    this.backgroundColor,
  });

  final VoidCallback onCancel;
  final String title;
  final Color? backgroundColor;

  @override
  Widget build(BuildContext context) {
    final doneColors = context.doneColors;
    final brightness = ThemeData.estimateBrightnessForColor(
      backgroundColor ?? doneColors.uiPrimary,
    );
    final textColor =
        brightness == Brightness.light
            ? doneColors.uiAlwaysPureBlack
            : doneColors.uiAlwaysPureWhite;
    return Container(
      height: kToolbarHeight,
      color: backgroundColor ?? context.doneColors.uiPrimary,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Padding(
            padding: const EdgeInsets.only(left: Margins.small),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: onCancel,
                splashColor: context.doneColors.purple,
                borderRadius: BorderRadius.circular(Margins.verySmall),
                child: Container(
                  padding: const EdgeInsets.all(Margins.verySmall),
                  child: Text(
                    S.of(context).cancel,
                    style: Theme.of(
                      context,
                    ).textTheme.titleMedium!.apply(color: textColor),
                  ),
                ),
              ),
            ),
          ),
          Text(
            title,
            style: Theme.of(
              context,
            ).textTheme.titleLarge!.apply(color: textColor),
          ),
          const SizedBox(width: 70), // Poor man's centering
        ],
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
