import 'package:done_localizations/done_localizations.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

class CostsBottomSheetHeader extends StatelessWidget {
  const CostsBottomSheetHeader({
    super.key,
    required this.onContinuePressed,
    this.isUltraWideLayout = false,
    required this.continueEnabled,
    required this.amountToBePaid,
    this.onTap,
  });

  final VoidCallback onContinuePressed;
  final bool isUltraWideLayout;
  final VoidCallback? onTap;
  final bool continueEnabled;
  final double amountToBePaid;

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final bottomSheetHeaderHeight = screenHeight / 7;
    return Stack(
      children: [
        GestureDetector(
          onTap: onTap,
          child: Container(
            padding: EdgeInsets.only(
              bottom: MediaQuery.of(context).padding.bottom,
              top: 20,
            ),
            decoration:
                !isUltraWideLayout
                    ? BoxDecoration(
                      color: context.doneColors.uiBgSheet,
                      border: Border.all(color: context.doneColors.uiBg1),
                      borderRadius: const BorderRadius.vertical(
                        top: Radius.circular(20),
                      ),
                    )
                    : null,
            height: bottomSheetHeaderHeight,
            child: Container(
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: context.doneColors.uiBgSheet,
                    width: 5,
                  ),
                ),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  _TotalCostsDisplay(amountToBePaid: amountToBePaid),
                  DoneButton(
                    key: const Key("ContinueButton"),
                    title: Text(S.of(context).genericContinue),
                    style: DoneButtonStyle.secondary,
                    onPressed: continueEnabled ? onContinuePressed : null,
                  ),
                ],
              ),
            ),
          ),
        ),
        if (!isUltraWideLayout) const _HeaderHandle(),
      ],
    );
  }
}

class _TotalCostsDisplay extends StatelessWidget {
  const _TotalCostsDisplay({required this.amountToBePaid});
  final double? amountToBePaid;
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          getPriceString(amountToBePaid),
          style: Theme.of(context).textTheme.headlineSmall,
        ),
        Text(S.of(context).toPayForCustomer.toLowerCase()),
      ],
    );
  }
}

class _HeaderHandle extends StatelessWidget {
  const _HeaderHandle();

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.topCenter,
      child: Column(
        children: [
          const VerticalMargin.small(),
          Container(
            decoration: BoxDecoration(
              color: context.doneColors.uiBg3,
              borderRadius: const BorderRadius.all(Radius.circular(20)),
            ),
            width: 45,
            height: 4,
          ),
        ],
      ),
    );
  }
}
