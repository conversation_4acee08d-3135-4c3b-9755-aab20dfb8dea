import 'package:done_localizations/done_localizations.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

class GeneratorVatInfo extends StatelessWidget {
  const GeneratorVatInfo({
    this.pricesIncludeVat = false,
    super.key,
    this.isCentered = false,
  });

  final bool pricesIncludeVat;
  final bool isCentered;

  @override
  Widget build(BuildContext context) {
    final vatText =
        pricesIncludeVat ? S.of(context).inclVat : S.of(context).exclVat;
    return Row(
      mainAxisAlignment:
          isCentered ? MainAxisAlignment.center : MainAxisAlignment.start,
      children: [
        Text(S.of(context).allPricesAreWritten),
        const HorizontalMargin(margin: 3),
        Text(
          vatText,
          style: Theme.of(context).textTheme.bodyMedium!.copyWith(
            color: context.doneColors.uiOnPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }
}
