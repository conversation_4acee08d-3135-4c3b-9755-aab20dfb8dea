import 'package:done_localizations/done_localizations.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

enum LeavingGeneratorSelection { deleteDraft, saveDraft, cancel }

Future<LeavingGeneratorSelection> showShouldDeleteDraftDialog(
  BuildContext context,
) async {
  final selection = await showDialog<LeavingGeneratorSelection>(
    context: context,
    builder:
        (BuildContext context) => DoneDialog(
          content: [
            DoneButton(
              title: Text(S.of(context).saveDraft),
              style: DoneButtonStyle.secondary,
              onPressed: () {
                Navigator.of(context).pop(LeavingGeneratorSelection.saveDraft);
              },
            ),
            const VerticalMargin(margin: 6),
            Done<PERSON><PERSON>on(
              title: Text(S.of(context).deleteDraft),
              style: DoneButtonStyle.negative,
              onPressed: () {
                Navigator.of(
                  context,
                ).pop(LeavingGeneratorSelection.deleteDraft);
              },
            ),
            const VerticalMargin(margin: 6),
            Done<PERSON><PERSON>on(
              title: Text(S.of(context).cancel),
              style: DoneButtonStyle.neutral,
              onPressed: () {
                Navigator.of(context).pop(LeavingGeneratorSelection.cancel);
              },
            ),
          ],
        ),
  );

  return selection ?? LeavingGeneratorSelection.cancel;
}

Future<bool> showDeleteDraftConfirmDialog(BuildContext context) async {
  final result = await showAdaptivePopup<bool>(
    context: context,
    builder:
        (BuildContext context) => DonePopup(
          content: [
            DoneButton(
              title: Text(S.of(context).deleteDraft),
              style: DoneButtonStyle.negative,
              onPressed: () {
                Navigator.of(context).pop(true);
              },
            ),
            const VerticalMargin(margin: 6),
            DoneButton(
              title: Text(S.of(context).cancel),
              style: DoneButtonStyle.neutral,
              onPressed: () {
                Navigator.of(context).pop(false);
              },
            ),
          ],
        ),
  );

  return result ?? false;
}
