import 'package:done_localizations/done_localizations.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

class GeneratorMultilineField extends StatelessWidget {
  const GeneratorMultilineField({
    super.key,
    required this.label,
    required this.onChange,
    this.isRequired = false,
    this.minLines = 3,
    this.initialValue,
  });
  final ValueChanged<String> onChange;
  final String label;
  final bool isRequired;
  final int minLines;
  final String? initialValue;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Row(
            children: [
              if (isRequired)
                Text(
                  "*",
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge!.copyWith(color: Colors.red),
                ),
              Text(label, style: Theme.of(context).textTheme.titleLarge),
            ],
          ),
          const VerticalMargin.medium(),
          DoneMultiLineTextField(
            minLines: minLines,
            onChange: onChange,
            initialValue: initialValue,
            hintText: S.of(context).typeHere,
          ),
        ],
      ),
    );
  }
}
