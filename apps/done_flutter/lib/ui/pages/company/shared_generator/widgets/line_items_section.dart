import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:done_flutter/core/blocs/line_item_cubit/expanded_line_item_cubit.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_flutter/ui/pages/company/quote_generator/widgets/articles_modal.dart';
import 'package:done_flutter/ui/pages/company/shared_generator/widgets/animated_line_item.dart';
import 'package:done_flutter/ui/pages/company/shared_generator/widgets/delete_line_item_dialog.dart';
import 'package:done_flutter/utils/extensions/enums/line_item.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

typedef LineItemBuilder<L extends BaseLineItem> =
    L Function({LineItemType? type, Article? article});

const _kAnimationDuration = Duration(milliseconds: 250);

class LineItemsSection<L extends BaseLineItem> extends StatefulWidget {
  const LineItemsSection({
    super.key,
    required this.lineItems,
    required this.defaultQuoteSettings,
    required this.onChange,
    required this.lineItemBuilder,
    this.articleGroupRef,
  });

  final List<L> lineItems;
  final LineItemBuilder<L> lineItemBuilder;
  final VoidCallback onChange;
  final DefaultQuoteSettings defaultQuoteSettings;
  final DocumentReference<ArticleGroup>? articleGroupRef;
  @override
  State<LineItemsSection<L>> createState() => _LineItemsSectionState<L>();
}

class _LineItemsSectionState<L extends BaseLineItem>
    extends State<LineItemsSection<L>> {
  final GlobalKey<AnimatedListState> lineItemsListKey =
      GlobalKey<AnimatedListState>();

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AutoMargins(
          child: Text(
            S.of(context).quoteGeneratorLineItems,
            style: Theme.of(context).textTheme.titleLarge,
          ),
        ),
        BlocProvider(
          create: (_) => ExpandedLineItemCubit(),
          child: AnimatedList(
            shrinkWrap: true,
            physics: const ClampingScrollPhysics(),
            key: lineItemsListKey,
            initialItemCount: widget.lineItems.length,
            itemBuilder: (context, index, animation) {
              final lineItem = widget.lineItems[index];
              return AnimatedLineItem<L>(
                margin: EdgeInsets.only(
                  bottom: index == widget.lineItems.length - 1 ? 0 : 12,
                ),
                itemKey: Key(lineItem.uid),
                animation: animation,
                defaultQuoteSettings: widget.defaultQuoteSettings,
                isDeductionEligible: widget.lineItems.deductionTypes.isNotEmpty,
                onLineItemChanged:
                    (lineItem) => _onLineItemChanged(lineItem, index),
                lineItem: lineItem,
                onLineItemRemoved: (bool swiped) async {
                  final shouldDelete =
                      swiped || await showDeleteLineItemDialog(context);

                  if (shouldDelete) {
                    _onLineItemRemoved(lineItem.type, index);
                  }
                },
              );
            },
          ),
        ),
        const VerticalMargin.medium(),
        AutoMargins(
          child: _AddLineItemButton(
            title:
                widget.articleGroupRef != null
                    ? S.of(context).addArticlesButtonTitle
                    : S.of(context).quoteGeneratorAddLine,
            onPressed: () => _addLineItems(context),
          ),
        ),
      ],
    );
  }

  void _onLineItemChanged(L lineItem, int index) {
    widget.lineItems[index] = lineItem;
    widget.onChange.call();
  }

  void _onLineItemRemoved(LineItemType itemType, int index) {
    final removedItem = widget.lineItems.removeAt(index);
    widget.onChange.call();

    AnimatedLineItem removeItemBuilder(
      BuildContext context,
      Animation<double> animation,
    ) => AnimatedLineItem(
      margin: EdgeInsets.only(
        top: index == widget.lineItems.length - 1 ? 0 : 12,
      ),
      animation: animation,
      lineItem: removedItem,
      itemKey: Key(removedItem.uid),
      defaultQuoteSettings: widget.defaultQuoteSettings,
      isDeductionEligible: widget.lineItems.deductionTypes.isNotEmpty,
      onLineItemChanged: (lineItem) {},
      onLineItemRemoved: (swiped) {},
    );

    lineItemsListKey.currentState!.removeItem(
      index,
      removeItemBuilder,
      duration: _kAnimationDuration,
    );
  }

  Future<void> _addLineItems(BuildContext context) async {
    if (widget.articleGroupRef != null) {
      await _addLineItemsFromArticles(context);
    } else {
      await _addLineItemFromTypes(context);
    }
  }

  Future<void> _addLineItemFromTypes(BuildContext context) async {
    final result = await pickAmongChoices<LineItemType>(
      context: context,
      choices: LineItemType.values,
      choiceTitleBuilder:
          (context, LineItemType l) => Text(l.label(S.of(context))),
      cancelButtonText: Text(S.of(context).cancel),
    );
    if (result == null) return;

    final lineItem = widget.lineItemBuilder(type: result.choice);
    _onLineItemAdded(lineItem);
  }

  Future<void> _addLineItemsFromArticles(BuildContext context) async {
    final articles = await showArticlesModal(
      context,
      articleGroupRef: widget.articleGroupRef!,
    );
    if (articles == null) return;
    for (final article in articles) {
      final lineItem = widget.lineItemBuilder(article: article);
      _onLineItemAdded(lineItem);
    }
  }

  void _onLineItemAdded(L lineItem) {
    final index = widget.lineItems.insertionIndexFor(lineItem);

    widget.lineItems.insert(index, lineItem);

    lineItemsListKey.currentState!.insertItem(
      index,
      duration: _kAnimationDuration,
    );
    widget.onChange.call();
  }
}

class _AddLineItemButton extends StatelessWidget {
  const _AddLineItemButton({required this.title, this.onPressed});

  final VoidCallback? onPressed;
  final String title;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        const Spacer(),
        OutlinedButton(
          style: OutlinedButton.styleFrom(
            side: BorderSide(color: context.doneColors.purple),
            foregroundColor: context.doneColors.purple,
            textStyle: Theme.of(context).textTheme.labelMedium,
            padding: const EdgeInsets.only(
              left: 12, // Compensate for Icons.add rendering whitespace
              right: 16,
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              const Icon(Icons.add, size: 18),
              const HorizontalMargin(margin: 2),
              Text(title),
            ],
          ),
          onPressed: onPressed,
        ),
      ],
    );
  }
}
