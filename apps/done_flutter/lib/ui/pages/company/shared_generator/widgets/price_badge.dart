import 'package:done_localizations/done_localizations.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

enum PriceBadgeType { prepaid, missing }

class PriceBadge extends StatelessWidget {
  const PriceBadge({super.key, required this.type});

  final PriceBadgeType type;

  @override
  Widget build(BuildContext context) {
    return DoneTinyBadge(
      value: type.title(context),
      color: type.color(context),
    );
  }
}

extension PriceBadgeTypeUIValues on PriceBadgeType {
  String title(BuildContext context) {
    switch (this) {
      case PriceBadgeType.prepaid:
        return S.of(context).prepaid;
      case PriceBadgeType.missing:
        return S.of(context).missingPrice;
    }
  }

  Color color(BuildContext context) {
    switch (this) {
      case PriceBadgeType.prepaid:
        return context.doneColors.uiPositive;
      case PriceBadgeType.missing:
        return context.doneColors.uiNeutral;
    }
  }
}
