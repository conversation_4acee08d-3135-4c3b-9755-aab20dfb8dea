import 'package:done_ui/done_ui.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:flutter/material.dart';

Future<bool> showSendToCustomerConfirmationPopup(
  BuildContext context,
  String title,
  String customerName,
  double amountToPay,
) async {
  return showConfirmationPopup(
    context: context,
    title: Text(title),
    message: Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Text("${S.of(context).customer}: $customerName"),
        const VerticalMargin.medium(),
        Text(
          "${S.of(context).toPayForCustomer}: ${getPriceString(amountToPay)}",
        ),
      ],
    ),
    actionTitle: Text(S.of(context).send),
    cancelTitle: Text(S.of(context).cancel),
  );
}
