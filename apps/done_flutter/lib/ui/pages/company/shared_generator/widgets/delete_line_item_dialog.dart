import 'package:done_localizations/done_localizations.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

Future<bool> showDeleteLineItemDialog(BuildContext context) async {
  final result = await showAdaptivePopup<bool>(
    context: context,
    builder:
        (BuildContext context) => DonePopup(
          content: [
            DoneButton(
              title: Text(S.of(context).deleteLine),
              style: DoneButtonStyle.negative,
              onPressed: () {
                Navigator.of(context).pop(true);
              },
            ),
            const VerticalMargin.verySmall(),
            DoneButton(
              title: Text(S.of(context).cancel),
              style: DoneButtonStyle.neutral,
              onPressed: () {
                Navigator.of(context).pop(false);
              },
            ),
          ],
        ),
  );
  return result ?? false;
}
