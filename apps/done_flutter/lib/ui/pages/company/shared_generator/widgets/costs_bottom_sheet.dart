import 'package:done_localizations/done_localizations.dart';
import 'package:done_flutter/ui/pages/company/shared_generator/widgets/costs_bottom_sheet_item.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

class CostsBottomSheet extends StatelessWidget {
  const CostsBottomSheet({
    super.key,
    required this.lineItems,
    required this.pricesIncludeVat,
    required this.calculationOptions,
  });

  final bool pricesIncludeVat;
  final CalculationOptions calculationOptions;
  final List<BaseLineItem> lineItems;

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final bottomSheetMaxHeight = screenHeight / 2;

    final centsRounding = lineItems.centsRounding(
      pricesIncludeVat: pricesIncludeVat,
      options: calculationOptions,
    );

    return Container(
      padding: EdgeInsets.only(
        left: 20,
        right: 20,
        bottom: 12 + MediaQuery.of(context).padding.bottom,
        top: 12,
      ),
      color: context.doneColors.uiBgSheet,
      constraints: BoxConstraints(maxHeight: bottomSheetMaxHeight),
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            if (lineItems.laborCost > 0)
              CostsBottomSheetItem(
                title: S.of(context).labor,
                price: lineItems.laborCost,
              ),
            if (lineItems.materialCost > 0)
              CostsBottomSheetItem(
                title: S.of(context).material,
                price: lineItems.materialCost,
              ),
            if (lineItems.travelCost > 0)
              CostsBottomSheetItem(
                title: S.of(context).travel,
                price: lineItems.travelCost,
              ),
            if (lineItems.otherCost > 0)
              CostsBottomSheetItem(
                title: S.of(context).otherCosts,
                price: lineItems.otherCost,
              ),
            Divider(color: context.doneColors.uiBg1, thickness: 1),
            CostsBottomSheetItem(
              title: S.of(context).totalGross, //"Totalt (brutto)",
              price: lineItems.totalGross,
              isLargeDisplay: true,
            ),
            if (!pricesIncludeVat)
              CostsBottomSheetItem(
                title: S.of(context).vat,
                price: lineItems.vat,
              ),
            ..._buildDeductions(context),
            if (centsRounding != 0)
              CostsBottomSheetItem(
                title: S.of(context).centsRounding,
                price: centsRounding,
              ),
            CostsBottomSheetItem(
              title: S.of(context).toPayForCustomer,
              price: lineItems.toPay(
                pricesIncludeVat: pricesIncludeVat,
                options: calculationOptions,
              ),
              isLargeDisplay: true,
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildDeductions(BuildContext context) {
    return lineItems.deductionTypes
        // Show only deductions that have value
        .where(
          (deductionType) =>
              lineItems
                  .taxDeduction(
                    deductionType: deductionType,
                    pricesIncludeVat: pricesIncludeVat,
                    options: calculationOptions,
                  )
                  .toDouble() >
              0,
        )
        .map(
          (deductionType) => CostsBottomSheetItem(
            title: deductionType.title(context),
            price:
                lineItems
                    .taxDeduction(
                      deductionType: deductionType,
                      pricesIncludeVat: pricesIncludeVat,
                      options: calculationOptions,
                    )
                    .toDouble(),
            isDeductionPrice: true,
          ),
        )
        .toList();
  }
}
