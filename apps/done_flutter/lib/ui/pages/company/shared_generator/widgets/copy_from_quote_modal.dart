import 'package:done_flutter/ui/pages/company/shared_generator/generator_type.dart';
import 'package:done_flutter/ui/pages/company/shared_generator/show_generator_for.dart';
import 'package:done_flutter/utils/extensions/models/job.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_flutter/ui/widgets/cards/quote_card.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

/// Used to let the user choose which quote to copy from or replace based on the [generatorType]
class CopyFromQuoteModal extends StatelessWidget {
  const CopyFromQuoteModal({
    super.key,
    required this.job,
    required this.quotes,
    required this.generatorType,
  });

  final Job job;
  final List<Quote> quotes;
  final GeneratorType generatorType;

  @override
  Widget build(BuildContext context) {
    final title =
        generatorType == GeneratorType.invoice
            ? job.createInvoiceLabel(context)
            : S.of(context).replaceQuote;
    return DonePopup(
      title: Text(title),
      content: [
        Column(
          children:
              quotes
                  .map<Widget>(
                    (quote) => QuoteCard(
                      quote: quote,
                      jobId: job.id,
                      isChatItem: true,
                      onTap: () {
                        Navigator.of(context).pop();
                        showGeneratorFor(
                          type: generatorType,
                          context: context,
                          job: job,
                          quote: quote,
                        );
                      },
                    ),
                  )
                  .toList(),
        ),
        const VerticalMargin.medium(),
        DoneButton(
          title: Text(S.of(context).cancel),
          style: DoneButtonStyle.neutral,
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
      ],
    );
  }
}
