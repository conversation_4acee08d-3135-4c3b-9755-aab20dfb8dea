import 'package:done_auth/done_auth.dart';
import 'package:done_database/done_database.dart';
import 'package:done_flutter/ui/pages/company/shared_generator/show_generator_for.dart';
import 'package:done_flutter/ui/pages/company/shared_generator/widgets/copy_from_quote_modal.dart';
import 'package:done_flutter/ui/pages/company/shared_generator/generator_type.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';

/// A button to copy an invoice or another quote from a quote if quotes exist on the [job].
/// If no quotes exist, the button is disabled.
///
/// If the [generatorType] is [GeneratorType.invoice], the button will open a [CopyFromQuoteModal] to generate an invoice.
/// If the [generatorType] is [GeneratorType.quote], the button will open a [CopyFromQuoteModal] to replace the quote picked.
class CopyFromQuoteButton extends StatelessWidget {
  const CopyFromQuoteButton({
    required this.originalContext,
    required this.job,
    required this.generatorType,
  });

  final BuildContext originalContext;
  final Job job;
  final GeneratorType generatorType;

  @override
  Widget build(BuildContext context) {
    final companyRef = context.authState.user!.company!;
    return StreamBuilder<List<Quote>>(
      stream: GetIt.instance<QuotesRepository>().companyQuotes(
        jobId: job.id,
        companyId: companyRef.id,
      ),
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return _buildButton(originalContext, const [], job);
        }

        // Only generated quotes
        final quotes =
            snapshot.data!.where((quote) => quote.rawQuote != null).toList();

        return _buildButton(originalContext, quotes, job);
      },
    );
  }

  Widget _buildButton(BuildContext context, List<Quote> quotes, Job job) {
    final isInvoicing = generatorType == GeneratorType.invoice;
    final title =
        isInvoicing
            ? Text(S.of(context).copyFromQuote)
            : Text(S.of(context).replaceQuote);
    return DoneButton(
      style: isInvoicing ? DoneButtonStyle.secondary : DoneButtonStyle.negative,
      title: title,
      onPressed:
          quotes.isNotEmpty
              ? () async {
                Navigator.of(context).pop();

                if (quotes.length == 1) {
                  return showGeneratorFor(
                    type: generatorType,
                    context: originalContext,
                    job: job,
                    quote: quotes.first,
                  );
                }

                final copyFromQuoteModal = CopyFromQuoteModal(
                  job: job,
                  quotes: quotes,
                  generatorType: generatorType,
                );

                await showAdaptivePopup<void>(
                  builder: (context) => copyFromQuoteModal,
                  context: originalContext,
                );
              }
              : null,
    );
  }
}
