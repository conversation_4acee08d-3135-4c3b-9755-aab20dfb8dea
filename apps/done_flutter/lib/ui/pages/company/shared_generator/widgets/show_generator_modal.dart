import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

Future<T?> showGeneratorModal<T extends Object>({
  required BuildContext context,
  required Widget child,
}) {
  return showModalBottomSheet<T>(
    context: context,
    isScrollControlled: true,
    isDismissible: false,
    enableDrag: false,
    backgroundColor: Colors.transparent,
    builder: (_) => DoneModal(child: child, barrierColor: Colors.transparent),
  );
}
