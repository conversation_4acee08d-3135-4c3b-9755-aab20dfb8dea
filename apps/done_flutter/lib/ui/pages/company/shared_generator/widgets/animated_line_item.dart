import 'package:done_image/done_image.dart';
import 'package:done_flutter/ui/pages/company/shared_generator/widgets/line_item_holder.dart';
import 'package:done_models/done_models.dart';
import 'package:flutter/material.dart';
import 'package:flutter_swipe_action_cell/flutter_swipe_action_cell.dart';

/// Wraps the LineItemHolder widget with SlideTransition used for animating left-right
/// when adding/removing LineItems from the animated list
class AnimatedLineItem<L extends BaseLineItem> extends StatelessWidget {
  const AnimatedLineItem({
    required this.animation,
    required this.onLineItemRemoved,
    required this.onLineItemChanged,
    required this.lineItem,
    required this.itemKey,
    required this.margin,
    required this.defaultQuoteSettings,
    required this.isDeductionEligible,
  }) : super(key: itemKey);

  final Animation<double> animation;
  final void Function(bool swiped) onLineItemRemoved;
  final ValueChanged<L> onLineItemChanged;
  final L lineItem;
  final Key itemKey;
  final EdgeInsets margin;
  final DefaultQuoteSettings defaultQuoteSettings;

  /// Is the current quote/invoice - to which the [lineItem] belongs - eligible to any type of deduction
  final bool isDeductionEligible;

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: Tween<double>(
        begin: 0,
        end: 1,
      ).animate(CurvedAnimation(parent: animation, curve: Curves.easeInCirc)),
      child: SlideTransition(
        key: itemKey,
        position: Tween<Offset>(
          begin: const Offset(0, -1),
          end: Offset.zero,
        ).animate(animation),
        child: SwipeActionCell(
          key: key!,
          trailingActions: [
            SwipeAction(
              onTap: (_) {
                onLineItemRemoved(true);
              },
              widthSpace: 44,
              color: Colors.transparent,
              content: Padding(
                padding: const EdgeInsets.only(top: 8, right: 20),
                child: Image.asset(ImageAssets.minusOnCircle, scale: 2),
              ),
            ),
          ],
          child: Container(
            margin: margin,
            child: LineItemHolder(
              key: itemKey,
              defaultQuoteSettings: defaultQuoteSettings,
              onLineItemRemoved: () {
                onLineItemRemoved(false);
              },
              isDeductionEligible: isDeductionEligible,
              onLineItemChanged: onLineItemChanged,
              lineItem: lineItem,
            ),
          ),
        ),
      ),
    );
  }
}
