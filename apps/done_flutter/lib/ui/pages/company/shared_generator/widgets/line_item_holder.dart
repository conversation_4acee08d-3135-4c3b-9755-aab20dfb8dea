import 'package:done_flutter/core/blocs/line_item_cubit/expanded_line_item_cubit.dart';
import 'package:done_flutter/ui/pages/company/shared_generator/widgets/price_badge.dart';
import 'package:done_image/done_image.dart';
import 'package:done_flutter/ui/widgets/deduction_type_selector.dart';
import 'package:done_flutter/utils/extensions/enums/detailed_deduction_type.dart';
import 'package:done_flutter/utils/extensions/enums/line_item.dart';
import 'package:done_flutter/utils/extensions/models/line_item.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_flutter/utils/text_field_utilities.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:logger/logger.dart';

/// Holder for LineItems
/// Animates between [_ContractedLineItem] and [_ExpandedLineItem]
///
/// Requires an ancestor [ExpandedLineItemCubit] in the widget tree to manage expansion state
class LineItemHolder<L extends BaseLineItem> extends StatefulWidget {
  const LineItemHolder({
    super.key,
    required this.onLineItemChanged,
    required this.onLineItemRemoved,
    required this.lineItem,
    required this.defaultQuoteSettings,
    required this.isDeductionEligible,
    this.validator,
  });

  final FormFieldValidator<L>? validator;
  final L lineItem;
  final ValueChanged<L> onLineItemChanged;
  final VoidCallback onLineItemRemoved;
  final DefaultQuoteSettings defaultQuoteSettings;

  /// Is the current quote/invoice - to which the [lineItem] belongs - eligible to any type of deduction
  final bool isDeductionEligible;

  @override
  _LineItemHolderState<L> createState() => _LineItemHolderState<L>();
}

class _LineItemHolderState<L extends BaseLineItem>
    extends State<LineItemHolder<L>> {
  late L _lineItem;
  bool _showExpanded = false;

  @override
  void initState() {
    super.initState();
    _lineItem = widget.lineItem;
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<ExpandedLineItemCubit, Key?>(
      listener: (BuildContext context, key) {
        _updateExpandedState(key);
      },
      child: MouseRegion(
        cursor: _showExpanded ? MouseCursor.defer : SystemMouseCursors.click,
        child: GestureDetector(
          onTap: () {
            context.read<ExpandedLineItemCubit>().updateExpanded(widget.key);
          },
          child: AnimatedCrossFade(
            duration: const Duration(milliseconds: 200),
            crossFadeState:
                _showExpanded
                    ? CrossFadeState.showSecond
                    : CrossFadeState.showFirst,
            firstChild: AutoMargins(
              child: _ContractedLineItem<L>(
                lineItem: _lineItem,
                isDeductionEligible: widget.isDeductionEligible,
              ),
            ),
            secondChild: _ExpandedLineItem<L>(
              lineItem: _lineItem,
              isDeductionEligible: widget.isDeductionEligible,
              onLineItemRemoved: widget.onLineItemRemoved,
              defaultQuoteSettings: widget.defaultQuoteSettings,
              onLineItemChanged: (lineItem) {
                widget.onLineItemChanged(lineItem);
                setState(() {
                  _lineItem = lineItem;
                });
              },
            ),
          ),
        ),
      ),
    );
  }

  void _updateExpandedState(Key? key) {
    if (key == widget.key) {
      setState(() {
        _showExpanded = true;
      });
    } else if (_showExpanded) {
      setState(() {
        _showExpanded = false;
        FocusScope.of(context).unfocus();
      });
    }
  }
}

/// Used in [native quote generator] for displaying the expanded state of a line item form field
/// Can be used for labor cost, material cost or other cost
/// Handles input and validation of LineItem
class _ExpandedLineItem<L extends BaseLineItem> extends StatefulWidget {
  const _ExpandedLineItem({
    super.key,
    required this.lineItem,
    required this.onLineItemChanged,
    required this.onLineItemRemoved,
    required this.defaultQuoteSettings,
    required this.isDeductionEligible,
  });

  final L lineItem;
  final ValueChanged<L> onLineItemChanged;
  final VoidCallback onLineItemRemoved;
  final DefaultQuoteSettings defaultQuoteSettings;
  final bool isDeductionEligible;

  @override
  _ExpandedLineItemState<L> createState() => _ExpandedLineItemState<L>();
}

class _ExpandedLineItemState<L extends BaseLineItem>
    extends State<_ExpandedLineItem<L>> {
  late L _lineItem;
  bool _isWorkedHoursValid = true;
  late final GlobalKey<FormState> _formKey;
  late final TextEditingController _unitCountController;
  late final TextEditingController _workedHoursController;
  late final TextEditingController _editorUnitPriceController;
  late final TextEditingController _descriptionController;
  late double _initialUnitCount;

  @override
  void initState() {
    super.initState();
    _lineItem = widget.lineItem;
    _formKey = GlobalKey<FormState>();
    _initialUnitCount = _lineItem.unitCount;
    _unitCountController = txtCtrlFromValue(_lineItem.unitCount.toString());
    _editorUnitPriceController = txtCtrlFromValue(
      _lineItem.editorUnitPrice.toString(),
    );

    _descriptionController = txtCtrlFromValue(_lineItem.description ?? '');

    final initialWorkedHours =
        _lineItem.unitType == LineItemUnitType.hours
            ? double.tryParse(_unitCountController.text)?.round()
            : _lineItem.workedHours;

    _workedHoursController = txtCtrlFromValue(
      initialWorkedHours?.toString() ?? "",
    );
    _unitCountController.addListener(_updateWorkedHoursListener);
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        AutoMargins(child: _buildLineItemContainer()),
        _buildDeleteLineItemButton(),
      ],
    );
  }

  DoneLineItemContainer _buildLineItemContainer() {
    final shouldShowDetailedDeductionType = _lineItem
        .shouldShowDetailedDeductionType(
          isDeductionEligible: widget.isDeductionEligible,
        );
    return DoneLineItemContainer(
      label: widget.lineItem.label ?? widget.lineItem.type.label(S.of(context)),
      isFocused: true,
      displayError:
          !widget.lineItem.isValid(
            isDeductionEligible: widget.isDeductionEligible,
          ),
      lineItemForm: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (_lineItem.allowDeductionSelection)
              ..._buildTaxDeductionSwitch(
                _lineItem.type,
                _lineItem.deductionType,
              ),
            _buildPriceSection(),
            const VerticalMargin.medium(),
            _buildUnitCountSection(),
            const VerticalMargin.medium(),
            if (_lineItem.allowDescriptionEditing) _buildItemDescriptionInput(),
            if (shouldShowDetailedDeductionType ||
                _lineItem.shouldShowWorkHours)
              _CustomValidationInputRow(
                validator: FormValidators.compose(context, [
                  FormValidators.required,
                  FormValidators.validInt,
                  FormValidators.validAmount,
                ]),
                controller: _workedHoursController,
                mode:
                    _lineItem.shouldShowWorkHours
                        ? AutovalidateMode.always
                        : AutovalidateMode.disabled,
                onValidating: (isValid) {
                  SchedulerBinding.instance.addPostFrameCallback(
                    (_) => setState(() => _isWorkedHoursValid = isValid),
                  );
                },
                builder:
                    (_, controller) => Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        if (_lineItem.shouldShowWorkHours)
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [_buildWorkedHoursInput(controller)],
                          ),
                        if (shouldShowDetailedDeductionType)
                          _buildDetailedDeductionTypeDisplayAndPicker(context),
                      ],
                    ),
              ),
          ],
        ),
      ),
    );
  }

  /// Delete button needs to be placed outside of LayotMargins constraints
  Positioned _buildDeleteLineItemButton() {
    return Positioned(
      top: -6,
      right: -2,
      child: IconButton(
        onPressed: widget.onLineItemRemoved,
        icon: Image.asset(ImageAssets.minusOnCircle, scale: 2),
      ),
    );
  }

  List<Widget> _buildTaxDeductionSwitch(
    LineItemType lineItemType,
    DeductionType lineItemDeduction,
  ) {
    final deductionTypes = {
      ...widget.defaultQuoteSettings.enabledDeductionTypes,
      lineItemDeduction,
      DeductionType.none,
    }.where(_lineItem.supportedDeductionTypes.contains);

    if (deductionTypes.length < 2) {
      return const [];
    }

    final sortedDeductionTypes =
        DeductionType.values.where(deductionTypes.contains).toList();

    return [
      DeductionTypeSelector(
        deductionTypes: sortedDeductionTypes,
        currentDeductionType: _lineItem.deductionType,
        onChange: (type) {
          try {
            if (_formKey.currentState!.validate()) {
              setState(() {
                _lineItem.deductionType = type;

                if (_lineItem.shouldShowDetailedDeductionType(
                  isDeductionEligible: widget.isDeductionEligible,
                )) {
                  final detailedDeductionTypes = getDetailedDeductionTypesFor(
                    type: type,
                    lineItemType: _lineItem.type,
                  );

                  final defaultDetailedDeduction =
                      widget
                          .defaultQuoteSettings
                          .defaultDetailedDeductionTypes[_lineItem
                          .deductionType];

                  final shouldAssignDefault = detailedDeductionTypes.contains(
                    defaultDetailedDeduction,
                  );

                  _lineItem.detailedDeductionType =
                      shouldAssignDefault
                          ? defaultDetailedDeduction
                          : detailedDeductionTypes.first;
                }
              });
              widget.onLineItemChanged(_lineItem);
            }
          } catch (e) {
            GetIt.instance<Logger>().e(e);
          }
        },
      ),
      const VerticalMargin(margin: 10),
    ];
  }

  Widget _buildPriceSection() {
    final shouldShowBadge = _lineItem.prepaid || _lineItem.isMissingPrice;
    if (shouldShowBadge) {
      final priceBadgeType =
          _lineItem.prepaid ? PriceBadgeType.prepaid : PriceBadgeType.missing;
      return PriceBadge(type: priceBadgeType);
    }
    if (_lineItem.allowUnitPriceEditing) {
      return Row(
        children: [
          _CustomValidationInputRow(
            validator: FormValidators.compose(context, [
              FormValidators.required,
              FormValidators.validDouble,
              FormValidators.validAmount,
            ]),
            controller: _editorUnitPriceController,
            builder: (_, controller) => _buildPriceInput(controller),
          ),
          if (_lineItem.editorUnitPrice != _lineItem.unitPrice)
            Padding(
              padding: const EdgeInsets.only(left: Margins.small),
              child: Row(
                children: [
                  Text(
                    "+ ${getPriceString(_lineItem.unitPrice - _lineItem.editorUnitPrice)} ${S.of(context).genericSupplement.toLowerCase()}",
                  ),
                  const HorizontalMargin.verySmall(),
                  const _SupplementTooltip(),
                ],
              ),
            ),
        ],
      );
    } else {
      return Text(
        getPriceString(_lineItem.unitPrice * _lineItem.unitCount),
        style: Theme.of(context).textTheme.titleMedium,
      );
    }
  }

  Widget _buildPriceInput(TextEditingController controller) {
    return SizedBox(
      width: 140,
      child: TextFormField(
        style: Theme.of(
          context,
        ).textTheme.bodyMedium!.apply(color: context.doneColors.uiOnPrimary),
        decoration: _inputDecoration(context).copyWith(
          suffixText: 'kr',
          suffixStyle: Theme.of(context).textTheme.titleMedium!.apply(
            color: context.doneColors.typographyLowContrast,
          ),
        ),
        keyboardType: TextInputType.number,
        controller: controller,
        onChanged: (price) {
          setState(() {
            // we use .toString in case `price` is `null`, as `tryParse` doesn't accept null values
            _lineItem.editorUnitPrice = double.tryParse(price.toString()) ?? 0;
          });

          widget.onLineItemChanged(_lineItem);
        },
      ),
    );
  }

  Widget _buildUnitCountSection() {
    return _CustomValidationInputRow(
      validator: FormValidators.compose(context, [
        FormValidators.required,
        FormValidators.validDouble,
        FormValidators.validAmount,
      ]),
      controller: _unitCountController,
      builder:
          (_, controller) => Row(
            children: [
              if (_lineItem.allowUnitCountEditing)
                _buildUnitCountInput(controller)
              else
                Text(_lineItem.unitCount.toString()),
              if (_lineItem.allowUnitTypeEditing)
                _buildUnitTypeDisplayAndPicker()
              else
                Padding(
                  padding: const EdgeInsets.all(Margins.small),
                  child: Text(
                    _lineItem.unitType.getTrimmedUnitDisplayName(S.of(context)),
                  ),
                ),
              const HorizontalMargin.large(),
              if (_lineItem.allowUnitCountEditing) _buildUnitCounter(),
            ],
          ),
    );
  }

  Widget _buildWorkedHoursInput(TextEditingController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const VerticalMargin.small(),
        Text(
          S.of(context).workedHours,
          style: Theme.of(context).textTheme.labelMedium,
        ),
        const VerticalMargin.small(),
        Row(
          children: [
            SizedBox(
              width: 85,
              child: TextFormField(
                decoration: _inputDecoration(context).copyWith(suffixText: 'h'),
                keyboardType: TextInputType.number,
                controller: controller,
                onChanged: (hours) {
                  setState(() {
                    // we use .toString in case `hours` is `null`, as `tryParse` doesn't accept null values
                    _lineItem.workedHours = int.tryParse(hours.toString()) ?? 0;
                  });
                  widget.onLineItemChanged(_lineItem);
                },
              ),
            ),
            if (!_isWorkedHoursValid)
              Padding(
                padding: const EdgeInsets.only(left: 16),
                child: Icon(
                  Icons.error,
                  color: context.doneColors.red,
                  size: 32,
                ),
              ),
          ],
        ),
      ],
    );
  }

  void _updateWorkedHoursListener() {
    final shouldUpdateHours = _lineItem.unitType == LineItemUnitType.hours;
    final hours = double.tryParse(_unitCountController.text)?.toInt();
    if (shouldUpdateHours && hours != null && hours >= 0) {
      _workedHoursController.text = hours.toString();
      setState(() {
        _lineItem.workedHours = hours;
      });
    }
  }

  Widget _buildUnitCountInput(TextEditingController controller) {
    return SizedBox(
      width: 80,
      child: TextFormField(
        style: Theme.of(
          context,
        ).textTheme.bodyMedium!.apply(color: context.doneColors.uiOnPrimary),
        decoration: _inputDecoration(context),
        keyboardType: TextInputType.number,
        controller: controller,
        textAlign: TextAlign.end,
        onChanged: (unitCount) {
          // we use .toString in case `unitCount` is `null`, as `tryParse` doesn't accept null values
          final count = double.tryParse(unitCount.toString()) ?? 0;
          if (!_lineItem.allowUnitCountIncrease && count > _initialUnitCount) {
            controller.text = _lineItem.unitCount.toString();
            return;
          }
          setState(() {
            _lineItem.unitCount = count;
          });
          widget.onLineItemChanged(_lineItem);
        },
      ),
    );
  }

  Widget _buildUnitTypeDisplayAndPicker() {
    return Row(
      children: [
        const HorizontalMargin.small(),
        DoneChip(
          label: Text(
            _lineItem.unitType.getTrimmedUnitDisplayName(S.of(context)),
            style: Theme.of(context).textTheme.bodyMedium!.apply(
              color: context.doneColors.uiOnPrimary,
            ),
          ),
          onTap: () async {
            final result = await pickAmongChoices<LineItemUnitType>(
              context: context,
              cancelButtonText: Text(S.of(context).cancel),
              choices: LineItemUnitType.values,
              choiceTitleBuilder:
                  (context, reason) =>
                      Text(reason.getUnitDisplayName(S.of(context))),
            );
            if (result != null) {
              setState(() {
                _lineItem.unitType = result.choice;
              });
              widget.onLineItemChanged(_lineItem);
            }
          },
        ),
        const HorizontalMargin.small(),
      ],
    );
  }

  Widget _buildDetailedDeductionTypeDisplayAndPicker(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const VerticalMargin.small(),
        Text(
          S.of(context).type,
          style: Theme.of(context).textTheme.labelMedium,
        ),
        const VerticalMargin.medium(),
        ConstrainedBox(
          constraints: const BoxConstraints(minHeight: 36, minWidth: 70),
          child: DoneChip(
            label: Text(_lineItem.detailedDeductionType?.title(context) ?? ''),
            color:
                _lineItem.detailedDeductionType == DetailedDeductionType.none
                    ? context.doneColors.uiNegative
                    : context.doneColors.uiPurple,
            onTap: () async {
              final result = await pickAmongChoices<DetailedDeductionType>(
                context: context,
                cancelButtonText: Text(S.of(context).cancel),
                choices: getDetailedDeductionTypesFor(
                  type: _lineItem.deductionType,
                  lineItemType: _lineItem.type,
                ),
                choiceTitleBuilder:
                    (context, reason) => Text(reason.title(context)),
              );
              if (result != null) {
                setState(() {
                  _lineItem.detailedDeductionType = result.choice;
                });
                widget.onLineItemChanged(_lineItem);
              }
            },
          ),
        ),
      ],
    );
  }

  Widget _buildUnitCounter() {
    return DoneLineItemUnitCounter(
      onMinusTap: () {
        try {
          final unitCount = num.parse(_unitCountController.value.text) - 1;
          if (unitCount < 0) return;

          _unitCountController.text = unitCount.toString();
          setState(() {
            _lineItem.unitCount = unitCount.toDouble();
          });
          widget.onLineItemChanged(_lineItem);
        } catch (e) {
          GetIt.instance<Logger>().e("$e");
        }
      },
      onPlusTap:
          _lineItem.allowUnitCountIncrease ||
                  _lineItem.unitCount < _initialUnitCount
              ? () {
                try {
                  final unitCount =
                      num.parse(_unitCountController.value.text) + 1;

                  _unitCountController.text = unitCount.toString();

                  setState(() {
                    _lineItem.unitCount = unitCount.toDouble();
                  });
                  widget.onLineItemChanged(_lineItem);
                } catch (e) {
                  GetIt.instance<Logger>().e("$e");
                }
              }
              : null,
    );
  }

  Widget _buildItemDescriptionInput() {
    return _CustomValidationInputRow(
      builder:
          (context, controller) => TextFormField(
            controller: controller,
            decoration: _inputDecoration(context).copyWith(
              hintText: S.of(context).quoteItemDescriptionHint,
              hintStyle: Theme.of(context).textTheme.bodyMedium!.apply(
                color: context.doneColors.typographyLowContrast,
              ),
            ),
            style: Theme.of(context).textTheme.bodyMedium!.apply(
              color: context.doneColors.uiOnPrimary,
            ),
            minLines: 2,
            maxLines: null,
            textCapitalization: TextCapitalization.sentences,
            onChanged: (description) {
              if (!_formKey.currentState!.validate()) return;

              setState(() {
                _lineItem.description = description;
              });
              widget.onLineItemChanged(_lineItem);
            },
          ),
      validator: (description) {
        if (!_lineItem.isDescriptionRequired) return null;
        if (description.isEmpty) {
          return FormValidators.required(context, description);
        }
        return null;
      },
      controller: _descriptionController,
    );
  }

  InputDecoration _inputDecoration(BuildContext context) {
    return DoneFormFieldHolder.inputDecoration(
      fillColor: context.doneColors.uiBg2WithOpacity,
      borderSide: BorderSide.none,
      textStyle: Theme.of(context).textTheme.bodyLarge,
      context: context,
    );
  }

  @override
  void dispose() {
    _unitCountController.dispose();
    _workedHoursController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }
}

class _ContractedLineItem<L extends BaseLineItem> extends StatelessWidget {
  const _ContractedLineItem({
    super.key,
    required this.lineItem,
    required this.isDeductionEligible,
  });

  final L lineItem;
  final bool isDeductionEligible;

  @override
  Widget build(BuildContext context) {
    return DoneLineItemContainer(
      label: lineItem.label ?? lineItem.type.label(S.of(context)),
      displayError: !lineItem.isValid(isDeductionEligible: isDeductionEligible),
      isFocused: false,
      lineItemForm: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Wrap(
                  runSpacing: 8,
                  spacing: 8,
                  crossAxisAlignment: WrapCrossAlignment.center,
                  children: [
                    if (lineItem.prepaid || lineItem.isMissingPrice)
                      _buildPriceBadge(context, lineItem)
                    else
                      Text(
                        lineItem.compactTextTitle(
                          strings: S.of(context),
                          addVAT:
                              false, // Always show value as entered in quote generator
                        ),
                        style: Theme.of(context).textTheme.bodyMedium!.apply(
                          color: context.doneColors.uiOnPrimary,
                        ),
                      ),
                    if (lineItem.shouldShowDeductionType &&
                        lineItem.deductionType != DeductionType.none) ...[
                      DoneTinyBadge(
                        value: lineItem.deductionType.shortTitle(context),
                      ),
                    ],
                    if (lineItem.shouldShowDetailedDeductionType(
                      isDeductionEligible: isDeductionEligible,
                    )) ...[
                      DoneTinyBadge(
                        value: lineItem.detailedDeductionType?.title(context),
                      ),
                    ],
                  ],
                ),
                if (!lineItem.description.isNullOrEmpty() &&
                    lineItem.allowDescriptionEditing) ...[
                  const VerticalMargin(margin: 6),
                  Text(
                    lineItem.ellipsedDescription,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ],
            ),
          ),
          if (lineItem.isValid(isDeductionEligible: isDeductionEligible))
            Icon(Icons.chevron_right, color: context.doneColors.uiChevron)
          else
            Icon(Icons.error, size: 28, color: context.doneColors.uiNegative),
        ],
      ),
    );
  }

  Widget _buildPriceBadge(BuildContext context, L item) {
    final priceBadgeType =
        item.prepaid ? PriceBadgeType.prepaid : PriceBadgeType.missing;

    return Padding(
      padding: const EdgeInsets.only(bottom: Margins.verySmall),
      child: Row(
        children: [
          PriceBadge(type: priceBadgeType),
          if (lineItem.allowUnitCountEditing && lineItem.unitCount > 0) ...[
            const HorizontalMargin.small(),
            Text(
              'X ${lineItem.unitCount} ${lineItem.unitType.getUnitDisplayName(S.of(context)).toLowerCase()}',
              style: Theme.of(context).textTheme.bodyMedium!.apply(
                color: context.doneColors.uiOnPrimary,
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// An input row with a custom validation message below it, it must have a [TextFormField] as a child in [builder] which uses the [controller]
///
/// The [controller] is necessary as the value of its `text` property that would be validated using the [validator]
/// The [builder] passes the [controller] back to the widget tree so it can be plugged into the correct [TextFormField] which we are validating
///
/// An optional [onValidating] callback can be passed which returns the value of validation with every change,
/// The validation [mode] is to always validate but [AutovalidateMode.disabled] can be used to disable validation
class _CustomValidationInputRow extends StatefulWidget {
  const _CustomValidationInputRow({
    required this.builder,
    required this.validator,
    required this.controller,
    this.mode = AutovalidateMode.always,
    this.onValidating,
  });

  final Widget Function(BuildContext, TextEditingController) builder;
  final String? Function(String value) validator;
  final TextEditingController controller;
  final ValueChanged<bool>? onValidating;
  final AutovalidateMode mode;

  @override
  State<_CustomValidationInputRow> createState() =>
      _CustomValidationInputRowState();
}

class _CustomValidationInputRowState extends State<_CustomValidationInputRow> {
  String? errorMessage;

  @override
  void initState() {
    super.initState();
    if (widget.mode == AutovalidateMode.disabled) return;
    // initial check before controller's text changes - on initial value -.
    errorMessage = widget.validator.call(widget.controller.text);
    if (widget.onValidating != null) {
      widget.onValidating!.call(errorMessage == null);
    }
    // Add listener to validate whenever controller's text changes
    widget.controller.addListener(_validateControllerValue);
  }

  void _validateControllerValue() {
    final value = widget.controller.text;
    setState(() {
      errorMessage = widget.validator.call(value);
    });
    if (widget.onValidating != null) {
      widget.onValidating!.call(errorMessage == null);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        widget.builder(context, widget.controller),
        _CustomValidationErrorWidget(errorMessage: errorMessage),
      ],
    );
  }

  @override
  void dispose() {
    widget.controller.removeListener(_validateControllerValue);
    super.dispose();
  }
}

/// Because the TextFormFields for LineItemForm have limited width
/// Their validation error messages get cut off.
/// That's why they need a separate widget for displaying the error message
/// That can easily extend the limited width of the TextFormField
///
/// if `errorMessage` is null, no error will be shown
class _CustomValidationErrorWidget extends StatelessWidget {
  const _CustomValidationErrorWidget({this.errorMessage});

  final String? errorMessage;

  @override
  Widget build(BuildContext context) {
    return AnimatedCrossFade(
      duration: const Duration(milliseconds: 200),
      crossFadeState:
          errorMessage == null
              ? CrossFadeState.showFirst
              : CrossFadeState.showSecond,
      firstChild: const SizedBox(),
      secondChild: Column(
        children: [
          const VerticalMargin(margin: 6),
          Padding(
            padding: const EdgeInsets.only(left: 12),
            child: Text(
              errorMessage ?? '',
              style: const TextStyle(
                color: Color(0xffd32f2f),
                inherit: false,
                fontFamily: "Inter",
                fontSize: 13,
                fontWeight: FontWeight.w400,
                decoration: TextDecoration.none,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _SupplementTooltip extends StatelessWidget {
  const _SupplementTooltip();

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap:
          () => showAdaptivePopup<void>(
            context: context,
            builder:
                (context) => DonePopup(
                  title: Text(S.of(context).genericSupplement),
                  subtitle: Text(S.of(context).supplementInfo),
                  content: [
                    DoneButton(
                      title: Text(S.of(context).close),
                      style: DoneButtonStyle.neutral,
                      onPressed: () => Navigator.of(context).pop(),
                    ),
                  ],
                ),
          ),
      child: const Icon(Icons.info_outline, size: 18),
    );
  }
}
