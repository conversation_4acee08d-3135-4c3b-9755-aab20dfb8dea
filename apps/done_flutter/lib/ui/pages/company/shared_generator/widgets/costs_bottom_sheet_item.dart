import 'package:done_localizations/done_localizations.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

class CostsBottomSheetItem extends StatelessWidget {
  const CostsBottomSheetItem({
    super.key,
    required this.title,
    required this.price,
    this.isLargeDisplay = false,
    this.isDeductionPrice = false,
  });

  final String title;
  final double price;
  final bool isLargeDisplay;
  final bool isDeductionPrice; // show negative sign infront of price

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 5),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.bodyLarge!.copyWith(
              color: context.doneColors.typographyMediumContrast,
            ),
          ),
          Text(
            (isDeductionPrice ? "- " : "") + getPriceString(price),
            style: _getPriceTextStyle(context),
          ),
        ],
      ),
    );
  }

  TextStyle _getPriceTextStyle(BuildContext context) {
    if (isDeductionPrice) {
      return Theme.of(
        context,
      ).textTheme.labelMedium!.copyWith(color: context.doneColors.red);
    }

    if (price >= 0) {
      return isLargeDisplay
          ? Theme.of(context).textTheme.titleLarge!
          : Theme.of(context).textTheme.bodyLarge!;
    }

    return isLargeDisplay
        ? Theme.of(
          context,
        ).textTheme.titleLarge!.copyWith(color: context.doneColors.uiNegative)
        : Theme.of(
          context,
        ).textTheme.bodyLarge!.copyWith(color: context.doneColors.uiNegative);
  }
}
