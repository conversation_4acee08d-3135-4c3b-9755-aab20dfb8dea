import 'package:done_flutter/invoicing/widgets/show_invoice_generator_dialog.dart';
import 'package:done_flutter/ui/pages/company/shared_generator/generator_type.dart';
import 'package:done_flutter/utils/dialogs/quote_dialogs.dart';
import 'package:done_models/done_models.dart';
import 'package:flutter/material.dart';

Future<void> showGeneratorFor({
  required GeneratorType type,
  required BuildContext context,
  required Job job,
  required Quote quote,
}) {
  switch (type) {
    case GeneratorType.quote:
      return showQuoteGenerator(context: context, job: job, quote: quote);
    case GeneratorType.invoice:
      return showInvoiceGenerator(context: context, job: job, quote: quote);
  }
}
