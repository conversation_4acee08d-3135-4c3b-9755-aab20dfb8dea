import 'package:done_auth/done_auth.dart';
import 'package:done_database/done_database.dart';
import 'package:done_flutter/core/blocs/master_detail_cubit/home_selection_cubit.dart';
import 'package:done_flutter/core/models/navigation_bar.dart';
import 'package:done_flutter/ui/pages/chat/conversations_list_view.dart';

import 'package:done_localizations/done_localizations.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

/// [StreamBuilder] that fetches and displays a list of company messages
/// it only displays active messages

class CompanyMessagesView extends StatelessWidget {
  const CompanyMessagesView({super.key, required this.onTap});

  final ValueChanged<String> onTap;

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<UserConversations>(
      stream: GetIt.instance<ConversationsRepository>().companyMessages(
        context.authState.getUserCompany()!.id,
      ),
      builder: (context, snapshot) {
        if (!snapshot.hasData) return CenteredProgressIndicator();

        final filteredMessagePreviews = filterMessagePreviews(
          snapshot.data!.messagePreviews,
        );

        if (filteredMessagePreviews.isEmpty) {
          return EmptyState(
            description: Text(S.of(context).noActiveConversations),
          );
        }

        final tabIndex = CompanyHomeTabs.messages.index;
        return ConversationsListView(
          currentId: context.watch<HomeSelectionCubit>().state[tabIndex],
          messagesPreview: filteredMessagePreviews,
          onTap: onTap,
          storageKey: 'companyMessagesListView',
        );
      },
    );
  }
}

// TODO: remove this function later when we are sure that all migrations have been run successfully
// This check has no effect even after migration is done so it's ok to leave it
List<LatestMessagePreview> filterMessagePreviews(
  List<LatestMessagePreview> allMessagePreviews,
) {
  return allMessagePreviews
      .where((element) => element.status != "closed")
      .toList();
}
