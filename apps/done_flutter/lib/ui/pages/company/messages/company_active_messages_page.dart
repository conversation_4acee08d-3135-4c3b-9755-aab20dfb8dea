import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:done_flutter/core/blocs/master_detail_cubit/home_selection_cubit.dart';
import 'package:done_auth/done_auth.dart';
import 'package:done_flutter/core/models/navigation_bar.dart';
import 'package:done_flutter/core/router/routes/project_routes.dart';

import 'package:done_flutter/core/services/meili_search_service.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_flutter/ui/pages/chat/chat_page.dart';
import 'package:done_flutter/ui/pages/company/messages/company_messages_view.dart';
import 'package:done_flutter/ui/pages/company/search/searchable_mixin.dart';
import 'package:done_flutter/ui/pages/company/search/widgets/done_search_bar.dart';
import 'package:done_flutter/ui/pages/company/search/widgets/search_results_view.dart';
import 'package:done_flutter/ui/pages/master_detail/layout_adaptive_page.dart';
import 'package:done_image/done_image.dart';

import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

class CompanyActiveMessagesPage extends StatefulWidget {
  const CompanyActiveMessagesPage({super.key});

  @override
  State<CompanyActiveMessagesPage> createState() =>
      _CompanyActiveMessagesPageState();
}

class _CompanyActiveMessagesPageState extends State<CompanyActiveMessagesPage>
    with SearchableMixin {
  @override
  Widget build(BuildContext context) {
    return LayoutAdaptivePage<HomeSelectionCubit, Map<int, String?>>(
      stateExtractor: (state) => state[CompanyHomeTabs.messages.index],
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(isSearching ? 60 : 100),
        child: Column(
          children: [
            if (!isSearching) AppBar(title: Text(S.of(context).messages)),
            SafeArea(
              bottom: false,
              top: isSearching,
              child: DoneSearchBar(
                onFocus: () => setSearchingState(true),
                onCancel: () => setSearchingState(false),
                onQueryChanged: updateQuery,
                isSearchActivated: isSearching,
              ),
            ),
          ],
        ),
      ),
      masterViewBuilder:
          (newContext) => _buildBody(
            onTap:
                (id) => newContext
                    .read<HomeSelectionCubit>()
                    .updateSelectionFor(CompanyHomeTabs.messages, id),
          ),
      detailViewBuilder: (context, id) {
        if (id != null) {
          final job =
              FirebaseFirestore.instance
                  .collection('jobs')
                  .doc(id)
                  .withJobConverter;
          return ChatPage(jobRef: job);
        }
        return DetailViewPlaceholder(asset: ImageAssets.chat);
      },
      child: _buildBody(
        onTap: (id) {
          final params = ChatPageRouteParams(
            projectId: id,
            showProjectLink: true,
          );
          ChatPageRoute(params: params).navigate(context);
        },
      ),
    );
  }

  Widget _buildBody({required ValueChanged<String> onTap}) {
    return isSearching
        ? _CompanyMessagesSearchView(onTap: onTap, query: query)
        : CompanyMessagesView(onTap: onTap);
  }
}

class _CompanyMessagesSearchView extends StatelessWidget {
  const _CompanyMessagesSearchView({required this.query, required this.onTap});
  final String query;
  final ValueChanged<String> onTap;

  @override
  Widget build(BuildContext context) {
    final companyId = context.authState.user!.company!.id;
    return SearchResultsView<JobChatMessageSearchEntry>(
      isSearching: query.isNotEmpty,
      searchHintText: S.of(context).searchMessagesHint,
      searchCallback: GetIt.instance<MeiliSearchService>()
          .searchMessagesWithCompanyId(companyId, query),
      resultsBuilder: (results) {
        // Catch only text based messages.
        final filteredResults = results.where(
          (message) => !message.body.isNullOrEmpty(),
        );
        final tabIndex = CompanyHomeTabs.messages.index;

        return filteredResults
            .map<Widget>(
              (item) => ListTile(
                trailing: Icon(
                  Icons.keyboard_arrow_right,
                  color: context.doneColors.uiChevron,
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: Margins.medium,
                ),
                tileColor:
                    context.watch<HomeSelectionCubit>().state[tabIndex] ==
                            item.jobId
                        ? context.doneColors.uiBg2
                        : context.doneColors.uiPrimary,
                title: Text(
                  context.authState.user?.documentReference.id == item.senderId
                      ? S.of(context).you
                      : item.senderName ?? S.of(context).anonymous,
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                subtitle: Text(
                  item.body ?? '',
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
                onTap: () => onTap(item.jobId!),
              ),
            )
            .toList()
            .separatedBy(() => Divider(color: context.doneColors.uiBg1));
      },
    );
  }
}
