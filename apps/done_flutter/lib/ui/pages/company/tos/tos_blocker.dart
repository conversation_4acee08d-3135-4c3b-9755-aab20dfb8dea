import 'package:collection/collection.dart';
import 'package:done_auth/done_auth.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import 'package:done_localizations/done_localizations.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class TermsOfServiceBlockerPage extends StatelessWidget {
  const TermsOfServiceBlockerPage({super.key});

  @override
  Widget build(BuildContext context) {
    final owner = context.authState.company!.usersV2.values.firstWhereOrNull(
      (user) => user.role == CompanyUserRole.owner,
    );
    final companyName = context.authState.company!.name;

    return PopScope(
      canPop: false,
      child: Scaffold(
        appBar: AppBar(
          title: Text(S.of(context).tosTitle),
          actions: [
            TextButton(
              style: TextButton.styleFrom(foregroundColor: Colors.red),
              child: Text(S.of(context).profileSignOut),
              onPressed: () {
                Navigator.of(context).pop();
                BlocProvider.of<AuthCubit>(context).signOut();
              },
            ),
          ],
          automaticallyImplyLeading:
              false, // Used to prevent Flutter from adding a close button
        ),
        body: FutureBuilder<DocumentSnapshot<User>>(
          future: owner?.reference.get(),
          builder: (context, snapshot) {
            final ownerName = snapshot.data?.data()?.fullName;
            final companyOwner =
                '$companyName ${S.of(context).userRoleOwner.toLowerCase()}';
            return Center(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: Margins.medium),
                child: Text(
                  S
                      .of(context)
                      .termsOfServiceBlockerContent(ownerName ?? companyOwner),
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.labelMedium,
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
