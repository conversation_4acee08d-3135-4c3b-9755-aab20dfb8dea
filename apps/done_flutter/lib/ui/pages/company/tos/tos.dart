import 'package:done_database/done_database.dart';
import 'package:done_flutter/core/router/routes/routes_definitions.dart';

import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import 'package:done_auth/done_auth.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:get_it/get_it.dart';

class TermsOfServicePage extends StatefulWidget {
  const TermsOfServicePage({super.key, required this.terms});

  final DocumentReference<TermsOfService> terms;

  @override
  _TermsOfServicePageState createState() => _TermsOfServicePageState();
}

class _TermsOfServicePageState extends State<TermsOfServicePage> {
  late final ScrollController _scrollController;
  TermsOfServiceStatus? _tosStatus;
  bool _hasScrolledToBottom = false;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _scrollController.addListener(_scrollListener);
    SchedulerBinding.instance.addPostFrameCallback(
      (_) => _checkTosAcceptance(),
    );
  }

  Future<void> _checkTosAcceptance() async {
    final companyId = context.authState.user?.company?.id;
    if (companyId == null) return;
    final tosAcceptance = await GetIt.instance<CompanyRepository>()
        .fetchTosAcceptance(companyId);
    setState(() {
      _tosStatus = tosAcceptance;
    });
  }

  @override
  Widget build(BuildContext context) {
    const loadingPage = PopScope(
      canPop: false,
      child: LoadingPage(showAppBar: false),
    );
    if (_tosStatus == null) return loadingPage;
    final tosStatus = _tosStatus!;
    final isSigned = tosStatus.isAccepted;
    final isEnforced = tosStatus.isEnforced;
    // Only allow popping if ToS is signed or not enforced
    final canPop = isSigned || !isEnforced;
    final companyRole = context.authState.companyRole;

    final isOwner = companyRole == CompanyUserRole.owner;

    return PopScope(
      canPop: canPop,
      child: Scaffold(
        appBar: AppBar(
          title: Text(S.of(context).tosTitle),
          titleSpacing: canPop ? -Margins.small : null,
          actions: [
            if (!isSigned)
              Padding(
                padding: const EdgeInsets.only(right: Margins.small),
                child: TextButton(
                  style: TextButton.styleFrom(foregroundColor: Colors.red),
                  child: Text(S.of(context).profileSignOut),
                  onPressed: () {
                    Navigator.of(context).pop();
                    BlocProvider.of<AuthCubit>(context).signOut();
                  },
                ),
              ),
          ],
          // Only show back button if page can be popped
          automaticallyImplyLeading: canPop,
        ),
        body: StreamBuilder<DocumentSnapshot<TermsOfService>>(
          stream: widget.terms.snapshots(),
          builder: (context, snapshot) {
            if (!snapshot.hasData || snapshot.data!.data() == null) {
              return CenteredProgressIndicator();
            }

            return _buildTerms(snapshot.data!);
          },
        ),
        // We shouldn't show the accept button if the ToS is already signed or not owner
        bottomNavigationBar:
            !isSigned && isOwner
                ? SafeArea(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      vertical: Margins.small,
                      horizontal: Margins.large,
                    ),
                    child: DoneButton(
                      title: Text(S.of(context).tosAcceptTOS),
                      onPressed:
                          _hasScrolledToBottom
                              ? () => _confirmAccept(context)
                              : null,
                    ),
                  ),
                )
                : null,
      ),
    );
  }

  Widget _buildTerms(DocumentSnapshot<TermsOfService> snapshot) {
    // Initial check of the scroll position on terms build to see if it's already at the end of the page
    // As the content can be small and not scrollable on big screens
    SchedulerBinding.instance.addPostFrameCallback((_) => _scrollListener());

    final styleSheet = MarkdownStyleSheet(
      p: Theme.of(context).textTheme.bodyMedium,
      h2: Theme.of(context).textTheme.labelMedium,
      strong: Theme.of(
        context,
      ).textTheme.bodyMedium!.apply(color: context.doneColors.uiBlack),
      a: Theme.of(
        context,
      ).textTheme.bodyMedium!.apply(color: context.doneColors.purple),
    );
    return Scrollbar(
      controller: _scrollController,
      child: SingleChildScrollView(
        controller: _scrollController,
        child: Padding(
          padding: const EdgeInsets.symmetric(
            vertical: Margins.medium,
            horizontal: Margins.large,
          ),
          child: Column(
            children: <Widget>[
              MultilineMarkdownBody(
                data: snapshot.data()!.introMarkdown,
                styleSheet: styleSheet,
              ),
              const VerticalMargin.small(),
              const Divider(),
              const VerticalMargin.small(),
              MultilineMarkdownBody(
                data: snapshot.data()!.markdown,
                styleSheet: styleSheet,
              ),
              const VerticalMargin.medium(),
            ],
          ),
        ),
      ),
    );
  }

  void _confirmAccept(BuildContext context) {
    final company = context.authState.company;

    showDialog<void>(
      context: context,
      builder:
          (BuildContext context) => DoneDialog(
            title: Text(S.of(context).tosAcceptTOS),
            content: [
              if (company == null)
                const SizedBox.shrink()
              else
                Text(S.of(context).tosAcceptanceConfirmation(company.name)),
              const VerticalMargin.medium(),
              DoneButton(
                title: Text(
                  S.of(context).accept,
                  style: const TextStyle(fontWeight: FontWeight.w600),
                ),
                style: DoneButtonStyle.secondary,
                onPressed: () async {
                  final user = context.authState.user;
                  await _acceptTerms(widget.terms, user: user!);
                  if (mounted) Navigator.pop(context);
                  const RootRoute().navigate(context);
                },
              ),
              const VerticalMargin(margin: 6),
              DoneButton(
                title: Text(S.of(context).cancel),
                style: DoneButtonStyle.neutral,
                onPressed: () => Navigator.pop(context),
              ),
            ],
          ),
    );
  }

  void _scrollListener() {
    if (_scrollController.offset >=
        _scrollController.position.maxScrollExtent) {
      setState(() {
        _hasScrolledToBottom = true;
      });
    }
  }

  @override
  void dispose() {
    _scrollController
      ..removeListener(_scrollListener)
      ..dispose();
    super.dispose();
  }
}

Future<void> _acceptTerms(DocumentReference terms, {required User user}) {
  final ref = user.company!.collection('tosAcceptance').doc(terms.id);
  return ref.set(
    {
      'createTime': FieldValue.serverTimestamp(),
      'user': user.documentReference,
      'userFirstName': user.firstName,
      'userLastName': user.lastName,
      'acceptTime': FieldValue.serverTimestamp(),
    }..removeNullValues(),
  );
}
