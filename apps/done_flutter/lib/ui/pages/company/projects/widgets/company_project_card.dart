import 'package:done_flutter/ui/pages/company/projects/widgets/project_info_section.dart';
import 'package:done_maps/done_maps.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

class CompanyProjectCard extends StatelessWidget {
  const CompanyProjectCard({
    required this.job,
    required this.onTap,
    required this.height,
    this.margins = const EdgeInsets.only(left: 4, right: 6),
    this.selected = false,
    super.key,
  });

  final Job job;
  final ValueChanged<String> onTap;
  final double height;
  final EdgeInsets margins;
  final bool selected;

  @override
  Widget build(BuildContext context) {
    return ConstrainedBox(
      constraints: BoxConstraints.tightFor(height: height),
      child: Padding(
        padding: margins,
        child: Stack(
          children: [
            Positioned.fill(
              child: DecoratedBox(
                decoration: BoxDecoration(
                  color:
                      selected
                          ? context.doneColors.uiPurpleSelection
                          : context.doneColors.uiPrimary,
                  borderRadius: BorderRadius.circular(Margins.small),
                  border: Border.all(color: context.doneColors.uiBg2),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Expanded(
                      child: LayoutBuilder(
                        builder:
                            (context, constraints) => ClipRRect(
                              borderRadius: const BorderRadius.vertical(
                                top: Radius.circular(Margins.small),
                              ),
                              child: UserMap(
                                height: constraints.maxHeight / 2,
                                location: job.cache?.customerLocation,
                              ),
                            ),
                      ),
                    ),
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.all(
                          Margins.small,
                        ).copyWith(bottom: Margins.verySmall),
                        child: ProjectInfoSection(job: job),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Positioned.fill(
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(Margins.small),
                  onTap: () => onTap(job.id),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
