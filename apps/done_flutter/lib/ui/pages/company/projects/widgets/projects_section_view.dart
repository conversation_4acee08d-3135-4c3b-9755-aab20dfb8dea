import 'package:done_flutter/core/router/routes/project_routes.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_flutter/ui/pages/company/projects/domain/enums.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

class ProjectsSectionView extends StatelessWidget {
  const ProjectsSectionView({
    required this.jobs,
    required this.section,
    required this.onTap,
    this.pageSize = 2,
    this.isDesktop = false,
    this.sectionHeaderMargins = const EdgeInsets.symmetric(
      horizontal: Margins.medium,
    ),
    super.key,
  });

  final List<Job> jobs;
  final ProjectsSection section;

  final ValueChanged<String> onTap;

  final bool isDesktop;

  final int pageSize;

  final EdgeInsets sectionHeaderMargins;

  @override
  Widget build(BuildContext context) {
    final pageSize =
        section == ProjectsSection.newlyMatched ? 1 : this.pageSize;

    if (jobs.isEmpty) {
      assert(
        section.showWhenEmpty,
        'Trying to show an empty projects section that we should not show',
      );
      return _EmptyProjectsSection(section: section, isDesktop: isDesktop);
    }

    return ListViewSection(
      header: ListViewSectionHeader(
        padding: sectionHeaderMargins,
        title: Padding(
          padding: const EdgeInsets.symmetric(
            vertical: Margins.verySmall,
          ), // Offset for when button is not present
          child: Text(section.title(context)),
        ),
        accessory:
            jobs.length > pageSize
                ? TextButton(
                  onPressed: () {
                    final params = ProjectsSectionRouteParams(section: section);
                    ProjectsSectionRoute(
                      params: params,
                      extra: jobs,
                    ).navigate(context);
                  },
                  child: Text(
                    S.of(context).showAllCount(jobs.length),
                    style: Theme.of(context).textTheme.bodyLarge!.apply(
                      color: context.doneColors.purple,
                    ),
                  ),
                  style: TextButton.styleFrom(
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                )
                : null,
      ),
      content: LayoutBuilder(
        builder: (context, constraints) {
          final effectivePageSize = jobs.length >= pageSize ? pageSize : 1;
          final contentHeight = section.itemHeight(context) * effectivePageSize;
          return PaginatedSectionContent(
            height: contentHeight,
            itemCount: jobs.length,
            pageSize: pageSize,
            width: constraints.maxWidth,
            isDesktop: isDesktop,
            itemBuilder:
                (context, index) => section.itemBuilder(
                  context,
                  jobs[index],
                  onTap,
                  isDesktop: isDesktop,
                ),
            separatorBuilder: section.separatorBuilder,
          );
        },
      ),
    );
  }
}

class _EmptyProjectsSection extends StatelessWidget {
  const _EmptyProjectsSection({required this.section, this.isDesktop = false});

  final ProjectsSection section;
  final bool isDesktop;

  @override
  Widget build(BuildContext context) {
    return ListViewSection(
      header: ListViewSectionHeader(
        title: Text(section.title(context)),
        padding: EdgeInsets.symmetric(
          horizontal: isDesktop ? Margins.xlarge : Margins.medium,
          vertical: Margins.medium,
        ),
      ),
      content: Center(
        child: Padding(
          padding: const EdgeInsets.only(top: Margins.large),
          child: Text(
            section.emptyStateMessage(context),
            style: Theme.of(context).textTheme.bodyMedium!.copyWith(
              color: context.doneColors.typographyLowContrast,
            ),
          ),
        ),
      ),
    );
  }
}
