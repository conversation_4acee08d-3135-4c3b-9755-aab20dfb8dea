import 'package:done_localizations/done_localizations.dart';
import 'package:done_flutter/ui/widgets/user_avatar.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

class ProjectInfoSection extends StatelessWidget {
  const ProjectInfoSection({
    required this.job,
    this.descriptionMaxLines = 3,
    this.crossAxisAlignment = CrossAxisAlignment.start,
    super.key,
  });

  final Job job;
  final int descriptionMaxLines;
  final CrossAxisAlignment crossAxisAlignment;

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: crossAxisAlignment,
      children: [
        UserAvatar(job.customer, radius: Margins.large),
        const HorizontalMargin.small(),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    job.customerName?.split(' ').first ??
                        S.of(context).anonymous,
                    style: Theme.of(context).textTheme.labelMedium,
                  ),
                  const HorizontalMargin.small(),
                  Flexible(
                    child: Text(
                      job.cache?.customerLocation?.subLocality ?? ' ',
                      style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                        color: context.doneColors.typographyLowContrast,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              Text(
                job.description!,
                maxLines: descriptionMaxLines,
                overflow: TextOverflow.ellipsis,
                style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                  color: context.doneColors.typographyLowContrast,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
