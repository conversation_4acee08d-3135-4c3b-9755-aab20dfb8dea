import 'package:done_flutter/ui/pages/company/projects/domain/enums.dart';
import 'package:done_flutter/ui/pages/company/projects/widgets/project_info_section.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

class ProjectActionableRow extends StatelessWidget {
  const ProjectActionableRow({
    required this.job,
    required this.section,
    required this.onTap,
    this.padding = const EdgeInsets.symmetric(
      horizontal: Margins.small,
      vertical: Margins.small,
    ),
    this.selected = false,
    this.borderRadius = BorderRadius.zero,
    super.key,
  });

  final Job job;
  final ProjectsSection section;
  final ValueChanged<String> onTap;

  final EdgeInsets padding;
  final bool selected;

  final BorderRadius borderRadius;
  @override
  Widget build(BuildContext context) {
    final actionTitle = section.actionTitle(context, job);
    return DecoratedBox(
      decoration: BoxDecoration(
        color:
            selected
                ? context.doneColors.uiPurpleSelection
                : Colors.transparent,
        borderRadius: borderRadius,
      ),
      child: InkWell(
        borderRadius: borderRadius,
        onTap: () => onTap(job.id),
        child: Padding(
          padding: padding,
          child: Row(
            children: [
              Expanded(
                child: ProjectInfoSection(
                  job: job,
                  descriptionMaxLines: 1,
                  crossAxisAlignment: CrossAxisAlignment.center,
                ),
              ),
              if (actionTitle != null)
                SizedBox(
                  height: 28,
                  child: DoneActionChip(
                    style: section.actionStyle,
                    onPressed: () => section.onActionPressed(context, job),
                    title: Text(actionTitle),
                    image: section.actionImage(context),
                    useAlternativeBgColor: selected,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
