import 'package:done_calls/done_calls.dart';
import 'package:done_flutter/core/blocs/master_detail_cubit/home_selection_cubit.dart';
import 'package:collection/collection.dart';
import 'package:done_auth/done_auth.dart';
import 'package:done_flutter/core/models/navigation_bar.dart';
import 'package:done_database/done_database.dart';
import 'package:done_flutter/core/router/routes/project_routes.dart';

import 'package:done_flutter/core/services/video_call_service.dart';
import 'package:done_flutter/ui/widgets/progress/progress_prerequisites.dart';
import 'package:done_flutter/ui/widgets/progress/reports/job_report_card.dart';
import 'package:done_flutter/utils/extensions/enums/quote_creation_type.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_flutter/invoicing/widgets/create_invoice_dialog.dart';
import 'package:done_flutter/ui/pages/company/projects/widgets/company_project_card.dart';
import 'package:done_flutter/ui/pages/company/projects/widgets/project_actionable_row.dart';
import 'package:done_flutter/ui/widgets/jobs/job_close_actions.dart';
import 'package:done_flutter/ui/widgets/select_date_and_time.dart';
import 'package:done_image/done_image.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:logger/logger.dart';
import 'package:rxdart/rxdart.dart';

extension ProjectsSectionValues on ProjectsSection {
  String title(BuildContext context) {
    switch (this) {
      case ProjectsSection.newlyMatched:
        return S.of(context).newProjectsSection;
      case ProjectsSection.awaitingPrerequisites:
        return S.of(context).prerequisitesSection;
      case ProjectsSection.toCall:
        return S.of(context).toCallProjectsSection;
      case ProjectsSection.needsQuote:
        return S.of(context).toQuoteProjectsSection;
      case ProjectsSection.awaitingQuoteAcceptance:
        return S.of(context).quoteSentProjectsSection;
      case ProjectsSection.notScheduled:
        return S.of(context).notScheduledProjectsSection;
      case ProjectsSection.scheduled:
        return S.of(context).scheduledProjectsSection;
      case ProjectsSection.awaitingReport:
        return S.of(context).waitingForReportProjectsSection;
      case ProjectsSection.awaitingInvoiceBasis:
        return S.of(context).sendInvoiceBasis;
      case ProjectsSection.completed:
        return S.of(context).completedProjectsSection;
    }
  }

  String emptyStateMessage(BuildContext context) {
    switch (this) {
      case ProjectsSection.newlyMatched:
        return S.of(context).noNewProjects;
      case ProjectsSection.awaitingPrerequisites:
        return S.of(context).noNewProjects;
      case ProjectsSection.toCall:
        return S.of(context).noProjectsToCall;
      case ProjectsSection.needsQuote:
        return S.of(context).noProjectsToQuote;
      case ProjectsSection.awaitingQuoteAcceptance:
        return S.of(context).noProjectsWaitingForQuoteAnswer;
      case ProjectsSection.notScheduled:
        return S.of(context).noProjectsToPlan;
      case ProjectsSection.scheduled:
        return S.of(context).noProjectsScheduled;
      case ProjectsSection.awaitingReport:
        return S.of(context).noProjectsToReport;
      case ProjectsSection.awaitingInvoiceBasis:
        return S.of(context).noProjectsToInvoiceBasis;
      case ProjectsSection.completed:
        return S.of(context).noCompletedJobs;
    }
  }

  /// We don't show some sections when they are empty.
  ///
  /// Currently this applies to all sections.
  bool get showWhenEmpty => false;

  ActionChipStyle get actionStyle {
    if (this == ProjectsSection.toCall ||
        this == ProjectsSection.awaitingReport) {
      return ActionChipStyle.primary;
    }

    return ActionChipStyle.neutral;
  }

  String? actionTitle(BuildContext context, Job job) {
    switch (this) {
      case ProjectsSection.toCall:
        return S.of(context).callAction;
      case ProjectsSection.awaitingPrerequisites:
        return job.progressPrerequisitesState.buttonTitle(context);
      case ProjectsSection.newlyMatched:
      case ProjectsSection.needsQuote:
      case ProjectsSection.awaitingQuoteAcceptance:
      case ProjectsSection.scheduled:
        return S.of(context).chat;
      case ProjectsSection.notScheduled:
        return S.of(context).schedule;
      case ProjectsSection.awaitingReport:
        return S.of(context).report;
      case ProjectsSection.awaitingInvoiceBasis:
        return null;
      case ProjectsSection.completed:
        return S.of(context).closeProject;
    }
  }

  Widget? actionImage(BuildContext context) {
    final imageColor = actionStyle.textColor(context);
    switch (this) {
      case ProjectsSection.toCall:
        return Image.asset(
          ImageAssets.phone,
          height: 20,
          width: 20,
          color: imageColor,
        );
      case ProjectsSection.awaitingPrerequisites:
        return Image.asset(
          ImageAssets.edit,
          height: 14,
          width: 14,
          color: imageColor,
        );
      case ProjectsSection.newlyMatched:
      case ProjectsSection.needsQuote:
      case ProjectsSection.awaitingQuoteAcceptance:
      case ProjectsSection.scheduled:
        return Image.asset(
          ImageAssets.chat,
          height: 20,
          width: 20,
          color: imageColor,
        );
      case ProjectsSection.notScheduled:
        return Image.asset(
          ImageAssets.calendar,
          height: 20,
          width: 20,
          color: imageColor,
        );
      case ProjectsSection.awaitingReport:
        return Icon(Icons.receipt_long_rounded, size: 20, color: imageColor);
      case ProjectsSection.awaitingInvoiceBasis:
        return null;
      case ProjectsSection.completed:
        return Icon(Icons.check, size: 20, color: imageColor);
    }
  }

  Future<void> onActionPressed(BuildContext context, Job job) async {
    switch (this) {
      case ProjectsSection.toCall:
        final userType = context.authState.getUserType();
        final canCall =
            !CallManagerProvider.of(context).hasCall &&
            job.isCommunicationAllowedFor(userType);
        if (!canCall) return;
        return initiateCall(job, context, source: 'project_action_cell');
      case ProjectsSection.awaitingPrerequisites:
        final state = job.progressPrerequisitesState;
        final date = await doneDatetimePicker(
          context: context,
          title: state.buttonTitle(context),
          type: DoneDateTimePickerType.date,
          futureDatesAllowed: false,
        );

        if (date != null) {
          switch (state) {
            case ProgressPrerequisitesState.notStarted:
              await Mutations.instance
                  .job(job.id)
                  .markAsPrerequisitesPending(date);
              break;
            case ProgressPrerequisitesState.pending:
              await Mutations.instance
                  .job(job.id)
                  .markAsPrerequisitesFulfilled(date);
              break;
            case ProgressPrerequisitesState.fulfilled:
              // Should be impossible
              break;
          }
        }
        break;
      case ProjectsSection.newlyMatched:
      case ProjectsSection.needsQuote:
      case ProjectsSection.awaitingQuoteAcceptance:
      case ProjectsSection.scheduled:
        final params = ChatPageRouteParams(projectId: job.id);
        return ChatPageRoute(params: params).navigate(context);
      case ProjectsSection.notScheduled:
        await selectDateAndTime(context, job, ScheduleType.work);
        break;
      case ProjectsSection.awaitingReport:
        return _awaitingReportAction(context, job);
      case ProjectsSection.awaitingInvoiceBasis:
        throw UnimplementedError();
      case ProjectsSection.completed:
        final isCustomerUser = context.authState.isUserCustomer();
        return showCloseJobDialog(
          context: context,
          isJobDone: job.isMarkedAsDone,
          onClose:
              (JobCloseReason closeReason, {String? otherText}) => Mutations
                  .instance
                  .job(job.documentReference.id)
                  .close(
                    JobCloseEventEntry(
                      closedBy: context.authState.user!.documentReference,
                      closeType:
                          isCustomerUser
                              ? JobCloseEventType.customerClosedProject
                              : JobCloseEventType.companyClosedProject,
                      closeReason: closeReason,
                      closeOtherReason: otherText,
                    ),
                  ),
        );
    }
  }

  Future<void> _awaitingReportAction(BuildContext context, Job job) async {
    final shouldReport = job.installationReportForm != null;
    final jobReportsRepo = GetIt.instance<JobReportsRepository>();
    final companyId = context.authState.company!.id;
    if (!shouldReport) {
      if (job.quoteCreationTypes.length == 1) {
        return job.quoteCreationTypes.first.invoiceAction(context, job);
      }
      return showAdaptivePopup<void>(
        context: context,
        builder:
            (dialogContext) =>
                CreateInvoiceDialog(job: job, originalContext: context),
      );
    } else {
      JobReport? report;
      try {
        report = (await jobReportsRepo
            .installationJobReports(job.id, companyId: companyId)
            .whereType<List<JobReport>>()
            .first
            .timeout(const Duration(seconds: 5))).firstWhereOrNull(
          (report) => report.status == JobReportStatus.draft,
        );
      } catch (e) {
        GetIt.instance<Logger>().e(
          'Error while fetching draft job report',
          error: e,
        );
      }

      await showInstallationReportForm(
        context: context,
        job: job,
        reportFormPath: job.installationReportForm!.reference.path,
        type: InstallationReportType.installation,
        report: report,
      );
    }
  }

  /// The height that each item takes
  double itemHeight(BuildContext context) {
    // FIXME: Rewrite without the use of scale factor.
    // ignore: deprecated_member_use
    final textScaleFactor = MediaQuery.of(context).textScaleFactor;
    if (this == ProjectsSection.newlyMatched) return 162 * textScaleFactor;

    return 59 * textScaleFactor;
  }

  Widget itemBuilder(
    BuildContext context,
    Job job,
    ValueChanged<String> onTap, {
    bool isDesktop = false,
  }) {
    final tabIndex = CompanyHomeTabs.projects.index;

    if (this == ProjectsSection.newlyMatched) {
      return CompanyProjectCard(
        job: job,
        onTap: onTap,
        margins:
            isDesktop
                ? EdgeInsets.zero
                : const EdgeInsets.only(left: 4, right: 6),
        height: itemHeight(context),
        selected: context.watch<HomeSelectionCubit>().state[tabIndex] == job.id,
      );
    }

    return ProjectActionableRow(
      job: job,
      section: this,
      onTap: onTap,
      borderRadius: BorderRadius.circular(Margins.medium),
      selected: context.watch<HomeSelectionCubit>().state[tabIndex] == job.id,
    );
  }

  WidgetBuilder? get separatorBuilder {
    if (this == ProjectsSection.newlyMatched) return null;

    return (context) => Padding(
      padding: const EdgeInsets.only(
        left: Margins.xxlarge * 2,
        right: Margins.medium,
      ),
      child: Divider(height: 0, thickness: 1, color: context.doneColors.uiBg1),
    );
  }
}
