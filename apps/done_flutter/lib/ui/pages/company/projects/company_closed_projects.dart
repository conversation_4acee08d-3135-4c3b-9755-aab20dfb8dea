import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:done_flutter/core/blocs/master_detail_cubit/index_selection_cubit.dart';
import 'package:done_flutter/core/router/routes/project_routes.dart';

import 'package:done_flutter/ui/pages/job/job_detail_page.dart';
import 'package:done_flutter/ui/pages/master_detail/layout_adaptive_page.dart';
import 'package:done_flutter/utils/company_tos_checker_util.dart';
import 'package:done_image/done_image.dart';
import 'package:done_models/done_models.dart';
import 'package:done_auth/done_auth.dart';
import 'package:done_database/done_database.dart';

import 'package:done_localizations/done_localizations.dart';
import 'package:done_flutter/ui/widgets/jobs/job_row.dart';
import 'package:done_flutter/utils/version_check_util.dart';
import 'package:done_ui/done_ui.dart';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

class CompanyProjectsClosed extends StatelessWidget {
  const CompanyProjectsClosed({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => IndexSelectionCubit(),
      child: LayoutAdaptivePage<IndexSelectionCubit, String?>(
        stateExtractor: (state) => state,
        appBar: AppBar(
          titleSpacing: -8,
          centerTitle: false,
          title: Text(S.of(context).closedProjects),
          elevation: 0,
        ),
        masterViewBuilder:
            (newContext) => _ClosedProjectsView(
              onTap: (id) {
                newContext.read<IndexSelectionCubit>().selectIndex(id);
              },
            ),
        detailViewBuilder: (context, id) {
          if (id != null) {
            final job =
                FirebaseFirestore.instance
                    .collection('jobs')
                    .doc(id)
                    .withJobConverter;
            return JobDetailPage(jobRef: job);
          }
          return DetailViewPlaceholder(asset: ImageAssets.projects);
        },
        child: _ClosedProjectsView(
          onTap: (id) {
            final params = ProjectDetailsRouteParams(projectId: id);
            ProjectDetailsRoute(params: params).navigate(context);
          },
        ),
      ),
    );
  }
}

class _ClosedProjectsView extends StatelessWidget {
  const _ClosedProjectsView({required this.onTap});
  final ValueChanged<String> onTap;
  @override
  Widget build(BuildContext context) {
    final user = context.authState.user;
    final jobsRepo = GetIt.instance<JobsRepository>();
    return StreamBuilder<List<Job>>(
      stream: jobsRepo.companyJobs(user!.company!.id, 'closed'),
      builder: (context, jobsSnapshot) {
        if (!jobsSnapshot.hasData) return CenteredProgressIndicator();
        if (jobsSnapshot.data!.isEmpty)
          return EmptyState(
            title: Text(S.of(context).companyJobsTabClosedTitle),
            description: Text(S.of(context).companyJobsTabClosedBody),
          );

        return Scrollbar(
          child: ListView(
            key: const PageStorageKey('companyClosedProjectsListView'),
            children: <Widget>[
              if (kShowVersionUtils) const CollapsableSoftUpdateBanner(),
              const NewTermsOfServiceWarningBanner(),
              ...jobsSnapshot.data!
                  .map<Widget>(
                    (job) => JobRow(
                      job: job,
                      selected:
                          context.watch<IndexSelectionCubit>().state == job.id,
                      onTap: () => onTap(job.id),
                    ),
                  )
                  .separatedBy(() => const Divider(indent: 16, height: 12)),
            ],
          ),
        );
      },
    );
  }
}
