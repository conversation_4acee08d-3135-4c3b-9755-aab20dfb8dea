import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:collection/collection.dart';
import 'package:done_auth/done_auth.dart';
import 'package:done_database/done_database.dart';
import 'package:done_flutter/ui/widgets/cards/rich_metadata_card.dart';

import 'package:done_localizations/done_localizations.dart';
import 'package:done_models/done_models.dart';
import 'package:done_router/done_router.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:url_launcher/url_launcher.dart';

class InstallationInformationPage extends StatelessWidget {
  const InstallationInformationPage({
    required this.jobRef,
    required this.installationInfoId,
    required this.jobNotes,
    super.key,
  });

  final String installationInfoId;
  final DocumentReference<Job> jobRef;
  final JobNotes? jobNotes;

  @override
  Widget build(BuildContext context) {
    final installationInfo = jobNotes?.installationInformation
        ?.firstWhereOrNull((info) => info.id == installationInfoId);

    if (installationInfo != null) {
      return _InstallationInformationPageContent(
        installationInfo: installationInfo,
      );
    }
    final companyId = context.authState.company!.reference.id;

    return FutureBuilder<JobNotes>(
      future: GetIt.instance<JobsRepository>().fetchJobNotes(
        jobRef.id,
        companyId,
      ),
      builder: (context, notesSnapshot) {
        if (!notesSnapshot.hasData) return CenteredProgressIndicator();

        final installationInfo = notesSnapshot.data!.installationInformation!
            .firstWhere((info) => info.id == installationInfoId);

        return _InstallationInformationPageContent(
          installationInfo: installationInfo,
        );
      },
    );
  }
}

class _InstallationInformationPageContent extends StatelessWidget {
  const _InstallationInformationPageContent({required this.installationInfo});

  final JobInstallationInformation installationInfo;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Align(
          alignment: Alignment.bottomCenter,
          child: DoneBottomSheet(
            title:
                installationInfo.title != null
                    ? Text(installationInfo.title!)
                    : const SizedBox.shrink(),
            subtitle:
                installationInfo.description != null
                    ? Text(installationInfo.description!)
                    : null,
            content: [
              if (installationInfo.fields != null)
                ...installationInfo.fields!.map(
                  (field) => LabeledValue(
                    label: field.label,
                    value: field.value,
                    showCopyButton: true,
                  ),
                ),
              if (installationInfo.actions != null)
                ...installationInfo.actions!
                    .where((action) => action.title != null)
                    .map((action) => _InstallationInfoAction(action: action)),
              DoneButton(
                title: Text(S.of(context).close),
                style: DoneButtonStyle.neutral,
                onPressed: () => Navigator.of(context).pop(),
              ),
            ].separatedBy(() => const VerticalMargin.medium()),
          ),
        ),
      ],
    );
  }
}

class _InstallationInfoAction extends StatelessWidget {
  const _InstallationInfoAction({required this.action});
  final RichMetadata action;
  @override
  Widget build(BuildContext context) {
    final showButton =
        action.title != null &&
        action.url != null &&
        action.image == null &&
        action.logo == null &&
        action.description == null;

    if (showButton) {
      return DoneButton(
        title: Text(action.title!),
        style: DoneButtonStyle.secondary,
        onPressed:
            () => URLRouter.instance.handleUrl(
              action.url,
              context: context,
              mode: LaunchMode.externalApplication,
            ),
      );
    }

    return RichMetadataCard(
      title: action.title!,
      url: action.url,
      description: action.description,
      image: action.image,
      logo: action.logo,
    );
  }
}
