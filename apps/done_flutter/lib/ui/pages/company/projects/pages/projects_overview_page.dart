import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:done_flutter/core/blocs/master_detail_cubit/home_selection_cubit.dart';
import 'package:done_auth/done_auth.dart';
import 'package:done_flutter/core/models/navigation_bar.dart';
import 'package:done_database/done_database.dart';
import 'package:done_flutter/core/router/routes/project_routes.dart';
import 'package:done_flutter/core/router/routes/routes_definitions.dart';
import 'package:done_flutter/core/services/meili_search_service.dart';
import 'package:done_flutter/ui/widgets/stream_page_builder.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_flutter/ui/pages/company/projects/domain/enums.dart';
import 'package:done_flutter/ui/pages/company/projects/widgets/projects_section_view.dart';
import 'package:done_flutter/ui/pages/company/search/searchable_mixin.dart';
import 'package:done_flutter/ui/pages/company/search/widgets/done_search_bar.dart';
import 'package:done_flutter/ui/pages/company/search/widgets/search_results_view.dart';
import 'package:done_flutter/ui/pages/job/job_detail_page.dart';
import 'package:done_flutter/ui/pages/master_detail/layout_adaptive_page.dart';
import 'package:done_image/done_image.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

/// Shows an overview page of the company's open jobs
///
/// It provides access to each [ProjectsSection]'s jobs and the ability to view all
/// jobs in a certain section
///
/// Each section is shown with a:
/// *  header: showing [ProjectsSection]'s title, and a button to show all
/// * content: shows cards/rows for the jobs for the designated section
/// and it navigates to the selected job on tap,
/// it also  provides quick actions for some [ProjectsSection]
class ProjectsOverviewPage extends StatefulWidget {
  const ProjectsOverviewPage({super.key});
  static const keyIdentifier = 'projectsOverviewPage';
  @override
  State<ProjectsOverviewPage> createState() => _ProjectsOverviewPageState();
}

class _ProjectsOverviewPageState extends State<ProjectsOverviewPage>
    with SearchableMixin {
  @override
  Widget build(BuildContext context) {
    final companyRef = context.authState.user!.company!;
    return StreamPageBuilder<List<Job>>(
      stream: GetIt.instance<JobsRepository>().companyJobs(
        companyRef.id,
        'open',
      ),
      builder: (context, jobs) {
        return LayoutAdaptivePage<HomeSelectionCubit, Map<int, String?>>(
          stateExtractor: (state) => state[CompanyHomeTabs.projects.index],
          appBar: _buildProjectsOverviewAppBar(context),
          detailViewBuilder: (context, id) {
            if (id != null) {
              final job =
                  FirebaseFirestore.instance
                      .collection('jobs')
                      .doc(id)
                      .withJobConverter;
              return JobDetailPage(jobRef: job);
            }
            return DetailViewPlaceholder(asset: ImageAssets.projects);
          },
          masterViewBuilder:
              (newContext) => _buildBody(
                jobs: jobs,
                onTap:
                    (id) => newContext
                        .read<HomeSelectionCubit>()
                        .updateSelectionFor(CompanyHomeTabs.projects, id),
              ),
          child: _buildBody(
            jobs: jobs,
            onTap: (id) {
              final params = ProjectDetailsRouteParams(projectId: id);
              ProjectDetailsRoute(params: params).navigate(context);
            },
          ),
        );
      },
    );
  }

  PreferredSize _buildProjectsOverviewAppBar(BuildContext context) {
    return PreferredSize(
      preferredSize: Size.fromHeight(isSearching ? 60 : 100),
      child: Column(
        children: [
          if (!isSearching)
            AppBar(
              title: Text(S.of(context).projectsTabTitle),
              actions: [
                DoneAppBarActionButton(
                  title: Text(
                    S.of(context).closedProjects,
                    style: Theme.of(context).textTheme.bodyMedium!.apply(
                      color: context.doneColors.purple,
                    ),
                  ),
                  onPressed:
                      () => const ClosedProjectsRoute().navigate(context),
                ),
              ],
            ),
          SafeArea(
            bottom: false,
            top: isSearching,
            child: DoneSearchBar(
              onFocus: () => setSearchingState(true),
              onCancel: () => setSearchingState(false),
              onQueryChanged: updateQuery,
              isSearchActivated: isSearching,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return EmptyState(
      title: Text(S.of(context).companyJobsTabOpenTitle),
      description: Text(S.of(context).companyJobsTabOpenBody),
    );
  }

  Widget _buildBody({
    required List<Job>? jobs,
    required ValueChanged<String> onTap,
  }) {
    if (jobs == null || jobs.isEmpty) return _buildEmptyState();
    return isSearching
        ? _CompanyProjectsSearchView(query: query, onTap: onTap)
        : _ProjectsOverviewList(
          key: const PageStorageKey('ProjectsOverviewList'),
          jobs: jobs,
          onTap: onTap,
        );
  }
}

class _ProjectsOverviewList extends StatelessWidget {
  const _ProjectsOverviewList({
    required this.jobs,
    required this.onTap,
    super.key,
  });

  final List<Job> jobs;
  final ValueChanged<String> onTap;

  @override
  Widget build(BuildContext context) {
    final isDesktop =
        defaultTargetPlatform == TargetPlatform.macOS ||
        defaultTargetPlatform == TargetPlatform.windows ||
        defaultTargetPlatform == TargetPlatform.linux;
    final sectionsToShow =
        ProjectsSection.values.where((s) {
          return s.showWhenEmpty ||
              jobs.where((job) => job.section() == s).isNotEmpty;
        }).toList();

    final sectionHeaderMargins =
        isDesktop
            ? const EdgeInsets.only(
              left: Margins.xlarge + Margins.small,
              right: Margins.xxlarge,
              top: Margins.small,
            )
            : const EdgeInsets.only(
              left: Margins.medium,
              right: Margins.large + Margins.medium,
              top: Margins.small,
            );

    final dividerMargins = EdgeInsets.only(
      left: sectionHeaderMargins.left,
      right: sectionHeaderMargins.right + 10,
    );

    return Scrollbar(
      child: ListView.separated(
        primary: true,
        itemCount: sectionsToShow.length + 1,
        // to compensate for intercom button
        padding: const EdgeInsets.only(bottom: 120),
        itemBuilder: (context, index) {
          if (index == sectionsToShow.length)
            return _OpenAllProjectsButton(jobs: jobs, onTap: onTap);

          final section = sectionsToShow[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: Margins.small),
            child: ProjectsSectionView(
              key: PageStorageKey('companyProjectsSection${section.index}'),
              jobs: jobs.where((job) => job.section() == section).toList(),
              section: section,
              sectionHeaderMargins: sectionHeaderMargins,
              isDesktop: isDesktop,
              onTap: onTap,
            ),
          );
        },
        separatorBuilder:
            (context, index) => Padding(
              padding: dividerMargins,
              child: Divider(
                height: 0,
                thickness: 1,
                color: context.doneColors.uiBg1,
              ),
            ),
      ),
    );
  }
}

class _OpenAllProjectsButton extends StatelessWidget {
  const _OpenAllProjectsButton({required this.jobs, required this.onTap});

  final List<Job> jobs;
  final ValueChanged<String> onTap;

  @override
  Widget build(BuildContext context) {
    return TextButton(
      onPressed: () => AllProjectsRoute(extra: jobs).navigate(context),
      child: Text(
        S.of(context).allOpenProjects,
        style: Theme.of(
          context,
        ).textTheme.labelMedium!.apply(color: context.doneColors.purple),
      ),
    );
  }
}

class _CompanyProjectsSearchView extends StatelessWidget {
  const _CompanyProjectsSearchView({required this.query, required this.onTap});
  final String query;
  final ValueChanged<String> onTap;

  @override
  Widget build(BuildContext context) {
    final companyId = context.authState.user!.company!.id;
    final tabIndex = CompanyHomeTabs.projects.index;

    return SearchResultsView<JobSearchEntry>(
      isSearching: query.isNotEmpty,
      searchHintText: S.of(context).searchHintText,
      searchCallback: GetIt.instance<MeiliSearchService>()
          .searchJobsWithCompanyId(companyId, query),
      resultsBuilder:
          (results) => results
              .map<Widget>(
                (item) => ListTile(
                  trailing: Icon(
                    Icons.keyboard_arrow_right,
                    color: context.doneColors.uiChevron,
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: Margins.medium,
                  ),
                  tileColor:
                      context.watch<HomeSelectionCubit>().state[tabIndex] ==
                              item.id
                          ? context.doneColors.uiBg2WithOpacity
                          : context.doneColors.uiPrimary,
                  title: Text(
                    item.customerName ?? S.of(context).anonymous,
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        item.description,
                        style: Theme.of(context).textTheme.bodyMedium!.apply(
                          color: context.doneColors.uiOnPrimary,
                        ),
                      ),
                    ],
                  ),
                  onTap: () => onTap(item.id),
                ),
              )
              .toList()
              .separatedBy(() => const Divider()),
    );
  }
}
