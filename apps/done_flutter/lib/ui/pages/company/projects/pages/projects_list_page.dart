import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:done_auth/done_auth.dart';
import 'package:done_database/done_database.dart';
import 'package:done_flutter/core/router/routes/project_routes.dart';

import 'package:done_flutter/ui/widgets/jobs/job_row.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:done_flutter/core/blocs/master_detail_cubit/index_selection_cubit.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_flutter/ui/pages/company/projects/domain/enums.dart';
import 'package:done_flutter/ui/pages/job/job_detail_page.dart';
import 'package:done_flutter/ui/pages/master_detail/layout_adaptive_page.dart';
import 'package:done_image/done_image.dart';
import 'package:get_it/get_it.dart';

class ProjectsListPage extends StatelessWidget {
  const ProjectsListPage({this.jobs, this.section, super.key});

  final ProjectsSection? section;
  final List<Job>? jobs;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => IndexSelectionCubit(),
      child: LayoutAdaptivePage<IndexSelectionCubit, String?>(
        stateExtractor: (state) => state,
        appBar: AppBar(
          centerTitle: false,
          elevation: 0,
          titleSpacing: -8,
          title: Text(
            section?.title(context) ?? S.of(context).projectsTabTitle,
          ),
        ),
        masterViewBuilder: (newContext) {
          void onTap(String id) {
            newContext.read<IndexSelectionCubit>().selectIndex(id);
          }

          if (jobs != null) {
            return _ProjectsSectionListView(
              onTap: onTap,
              jobs: jobs!,
              section: section,
            );
          }

          return _StreamProjectsSectionListView(section: section, onTap: onTap);
        },
        detailViewBuilder: (context, id) {
          if (id != null) {
            final job =
                FirebaseFirestore.instance
                    .collection('jobs')
                    .doc(id)
                    .withJobConverter;
            return JobDetailPage(jobRef: job);
          }
          return DetailViewPlaceholder(asset: ImageAssets.projects);
        },
        child:
            jobs != null
                ? _ProjectsSectionListView(
                  onTap: (id) {
                    final params = ProjectDetailsRouteParams(projectId: id);
                    ProjectDetailsRoute(params: params).navigate(context);
                  },
                  jobs: jobs!,
                  section: section,
                )
                : _StreamProjectsSectionListView(
                  onTap: (id) {
                    final params = ProjectDetailsRouteParams(projectId: id);
                    ProjectDetailsRoute(params: params).navigate(context);
                  },
                  section: section,
                ),
      ),
    );
  }
}

class _StreamProjectsSectionListView extends StatelessWidget {
  const _StreamProjectsSectionListView({
    required this.section,
    required this.onTap,
  });

  final ProjectsSection? section;
  final ValueChanged<String> onTap;

  @override
  Widget build(BuildContext context) {
    final company = context.authState.company;
    final companyRef = company?.reference;

    if (companyRef == null) return CenteredProgressIndicator();
    return StreamBuilder<List<Job>>(
      stream: GetIt.instance<JobsRepository>().companyJobs(
        companyRef.id,
        'open',
      ),
      builder: (context, snapshot) {
        if (!snapshot.hasData) return CenteredProgressIndicator();
        final allJobs = snapshot.data!;
        final jobs =
            section != null
                ? allJobs.where((job) => job.section() == section).toList()
                : allJobs;

        return Scrollbar(
          child: _ProjectsSectionListView(
            section: section,
            jobs: jobs,
            onTap: onTap,
          ),
        );
      },
    );
  }
}

class _ProjectsSectionListView extends StatelessWidget {
  const _ProjectsSectionListView({
    required this.section,
    required this.jobs,
    required this.onTap,
  });

  final ProjectsSection? section;
  final List<Job> jobs;
  final ValueChanged<String> onTap;

  @override
  Widget build(BuildContext context) {
    if (jobs.isEmpty) return _EmptyProjectsListViewMessage(section: section);

    return ListView.separated(
      primary: true,
      shrinkWrap: true,
      keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
      itemCount: jobs.length,
      itemBuilder: (context, index) {
        final job = jobs[index];
        return JobRow(job: job, onTap: () => onTap(job.id));
      },
      separatorBuilder:
          (context, index) => Divider(
            indent: Margins.large,
            height: 1,
            color: context.doneColors.uiBg1,
          ),
    );
  }
}

class _EmptyProjectsListViewMessage extends StatelessWidget {
  const _EmptyProjectsListViewMessage({required this.section});

  final ProjectsSection? section;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: Margins.large),
        child: Text(
          section?.emptyStateMessage(context) ??
              S.of(context).companyJobsTabOpenBody,
          style: Theme.of(context).textTheme.bodyLarge,
        ),
      ),
    );
  }
}
