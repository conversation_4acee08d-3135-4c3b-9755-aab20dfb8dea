import 'package:done_localizations/done_localizations.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:intercom_flutter/intercom_flutter.dart';

class QuoteSuccessfullySentDialog extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return DoneDialog(
      title: Text(S.of(context).quoteSentDialogTitle),
      content: [
        const VerticalMargin.verySmall(),
        Text(
          S.of(context).quoteSentDialogText,
          style: Theme.of(
            context,
          ).textTheme.bodyMedium!.apply(color: context.doneColors.uiOnPrimary),
        ),
        const VerticalMargin.medium(),
        MultilineMarkdownBody(
          data: S.of(context).askSupportForQuestions,
          onTapLink: (_, link, __) {
            Intercom.instance.displayMessenger();
          },
        ),
        const VerticalMargin.xxlarge(),
        <PERSON><PERSON><PERSON><PERSON>(
          style: DoneButtonStyle.neutral,
          title: Text(S.of(context).dismiss),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
      ],
    );
  }
}
