import 'package:done_booking/done_booking.dart';
import 'package:done_database/done_database.dart';
import 'package:done_flutter/core/router/routes/project_routes.dart';
import 'package:done_flutter/ui/pages/company/shared_generator/widgets/costs_bottom_sheet.dart';
import 'package:done_flutter/ui/pages/company/shared_generator/widgets/costs_bottom_sheet_header.dart';
import 'package:done_flutter/ui/pages/company/shared_generator/widgets/delete_draft_dialog.dart';
import 'package:done_flutter/ui/pages/company/shared_generator/widgets/generator_app_bar.dart';
import 'package:done_flutter/ui/pages/company/shared_generator/widgets/generator_multiline_field.dart';
import 'package:done_flutter/ui/pages/company/shared_generator/widgets/generator_vat_info.dart';
import 'package:done_flutter/ui/pages/company/shared_generator/widgets/line_items_section.dart';
import 'package:logger/logger.dart';
import 'package:get_it/get_it.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_models/done_models.dart';
import 'package:done_auth/done_auth.dart';

import 'package:done_flutter/ui/widgets/cards/customer_card.dart';
import 'package:expandable_bottom_sheet/expandable_bottom_sheet.dart';
import 'package:done_flutter/ui/pages/company/quote_generator/preview_raw_quote_page.dart';

class QuoteGeneratorForm extends StatefulWidget {
  const QuoteGeneratorForm({
    super.key,
    required this.job,
    required this.rawQuote,
    required this.defaultQuoteSettings,
    required this.isExistingDraft,
    required this.onCancel,
    this.articleGroup,
  });

  final Job job;
  final RawQuote rawQuote;
  final DefaultQuoteSettings defaultQuoteSettings;
  final bool isExistingDraft;
  final JobArticleGroup? articleGroup;

  /// Should return weither to continue navigating back upon cancellation or not.
  final Future<bool> Function(
    BuildContext context,
    bool hasChanges,
    RawQuote quote,
  )
  onCancel;

  @override
  _QuoteGeneratorFormState createState() => _QuoteGeneratorFormState();
}

class _QuoteGeneratorFormState extends State<QuoteGeneratorForm> {
  late RawQuote _rawQuote;
  bool _hasChanges = false;
  late final ScrollController scrollController;
  late final GlobalKey<FormState> _quoteFormKey;
  late final GlobalKey<ExpandableBottomSheetState> _bottomSheetKey;
  late final GlobalKey<AnimatedListState> lineItemsListKey;

  @override
  void initState() {
    super.initState();
    _rawQuote = widget.rawQuote;
    scrollController = ScrollController();
    _quoteFormKey = GlobalKey<FormState>();
    _bottomSheetKey = GlobalKey<ExpandableBottomSheetState>();
    lineItemsListKey = GlobalKey<AnimatedListState>();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      // FIXME: Rewrite to use PopScope instead
      // ignore: deprecated_member_use
      child: WillPopScope(
        onWillPop: () async => widget.onCancel(context, _hasChanges, _rawQuote),
        child: Scaffold(
          backgroundColor: context.doneColors.uiPrimary,
          appBar: GeneratorAppBar(
            onCancel: () async {
              final shouldNavigateBack = await widget.onCancel(
                context,
                _hasChanges,
                _rawQuote,
              );
              if (shouldNavigateBack) Navigator.of(context).pop();
            },
            title:
                _rawQuote.type == QuoteType.offer
                    ? S.of(context).createQuote
                    : S.of(context).manageAdditions,
          ),
          resizeToAvoidBottomInset: false,
          body: LayoutBuilder(
            builder: (context, constraints) {
              return Align(
                child:
                    (constraints.maxWidth > Layouts.ultraWideLayout)
                        ? _buildBodyForUltraWideLayout()
                        : _buildBodyForNormalLayout(),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildBodyForNormalLayout() {
    return ColoredBox(
      color: context.doneColors.uiPrimary,
      child: ExpandableBottomSheet(
        key: _bottomSheetKey,
        background: _buildForm(),
        persistentHeader: CostsBottomSheetHeader(
          onContinuePressed: _saveChanges,
          continueEnabled: _rawQuote.isValid,
          amountToBePaid: _rawQuote.toPay,
          onTap: () {
            if (_bottomSheetKey.currentState!.expansionStatus ==
                ExpansionStatus.contracted) {
              _bottomSheetKey.currentState!.expand();
            } else {
              _bottomSheetKey.currentState!.contract();
            }
          },
        ),
        expandableContent: CostsBottomSheet(
          lineItems: _rawQuote.lineItems,
          pricesIncludeVat: _rawQuote.pricesIncludeVat,
          calculationOptions: _rawQuote.calculationOptions,
        ),
      ),
    );
  }

  Widget _buildBodyForUltraWideLayout() {
    return Row(
      children: [
        Expanded(flex: 2, child: _buildForm(isUltraWideLayout: true)),
        const HorizontalMargin.large(),
        Expanded(
          child: ColoredBox(
            color: context.doneColors.uiBgSheet,
            child: Column(
              children: [
                const VerticalMargin.small(),
                GeneratorVatInfo(
                  isCentered: true,
                  pricesIncludeVat: _rawQuote.pricesIncludeVat,
                ),
                CostsBottomSheet(
                  lineItems: _rawQuote.lineItems,
                  pricesIncludeVat: _rawQuote.pricesIncludeVat,
                  calculationOptions: _rawQuote.calculationOptions,
                ),
                CostsBottomSheetHeader(
                  onContinuePressed: _saveChanges,
                  continueEnabled: _rawQuote.isValid,
                  amountToBePaid: _rawQuote.toPay,
                  isUltraWideLayout: true,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildForm({bool isUltraWideLayout = false}) {
    final children = _children(context, isUltraWideLayout: isUltraWideLayout);
    return LayoutMargins(
      child: Form(
        key: _quoteFormKey,
        child: Scrollbar(
          controller: scrollController,
          child: PageContent(
            child: ListView.builder(
              controller: scrollController,
              shrinkWrap: true,
              addRepaintBoundaries: false,
              itemCount: children.length,
              itemBuilder: (context, index) => children[index],
            ),
          ),
        ),
      ),
    );
  }

  List<Widget> _children(
    BuildContext context, {
    bool isUltraWideLayout = false,
  }) {
    final screenHeight = MediaQuery.of(context).size.height;
    final bottomSheetHeaderHeight = screenHeight / 7;
    //* when keyboard is shown the bottom sheet header has a glitch
    //* this is used to hide BottomSheetHeader when keyboard is shown
    final bottomPadding =
        MediaQuery.of(context).viewInsets.bottom >
                0 // Keyboard visible?
            ? MediaQuery.of(context).viewInsets.bottom
            : bottomSheetHeaderHeight + MediaQuery.of(context).padding.bottom;
    return [
      const VerticalMargin.large(),
      if (widget.job.tags?.contains(JobTag.express) ?? false)
        const AutoMargins(child: ExpressJobCard()),
      AutoMargins(
        child: CustomerCard(
          customerRef: widget.job.customer,
          onTap: () {
            final params = QuoteCustomerProfileRouteParams(
              customerId: widget.job.customer.id,
              quoteId: _rawQuote.id,
              projectId: widget.job.id,
            );
            QuoteCustomerProfileRoute(params: params).navigate(context);
          },
        ),
      ),
      AutoMargins(
        child: GeneratorVatInfo(pricesIncludeVat: _rawQuote.pricesIncludeVat),
      ),
      if (_rawQuote.isWorkDescriptionRequired)
        AutoMargins(
          child: GeneratorMultilineField(
            label: S.of(context).workDescription,
            onChange: (description) {
              setState(() {
                _hasChanges = true;

                _rawQuote = _rawQuote.copyWith(workDescription: description);
              });
            },
            isRequired: true,
            minLines: 5,
            initialValue: _rawQuote.workDescription,
          ),
        ),
      LineItemsSection(
        lineItems: _rawQuote.lineItems,
        defaultQuoteSettings: widget.defaultQuoteSettings,
        articleGroupRef:
            _rawQuote.lockedArticleGroup ?? widget.articleGroup?.reference,
        lineItemBuilder: ({article, type}) {
          assert(article != null || type != null);
          if (article != null) return QuoteLineItem.newItemFromArticle(article);
          return QuoteLineItem.newItemForType(
            type!,
            widget.defaultQuoteSettings,
          );
        },
        onChange: () {
          setState(() {
            _hasChanges = true;
          });
        },
      ),
      AutoMargins(
        child: GeneratorMultilineField(
          label: S.of(context).otherInformation,
          onChange: (otherInfo) {
            setState(() {
              _hasChanges = true;

              _rawQuote = _rawQuote.copyWith(otherInformation: otherInfo);
            });
          },
          initialValue: _rawQuote.otherInformation,
        ),
      ),
      if (widget.isExistingDraft)
        AutoMargins(
          child: DoneButton(
            title: Text(S.of(context).deleteDraft),
            style: DoneButtonStyle.negative,
            onPressed: () async {
              final result = await showDeleteDraftConfirmDialog(context);
              if (result) {
                await _rawQuote.documentReference.delete();
                Navigator.of(context).pop();
              }
            },
          ),
        ),
      VerticalMargin(margin: isUltraWideLayout ? Margins.small : bottomPadding),
    ].separatedBy(() => const VerticalMargin.medium());
  }

  Future<bool> _saveChanges({bool savingOnLeavingPage = false}) async {
    if (!_quoteFormKey.currentState!.validate()) {
      if (savingOnLeavingPage) {
        Navigator.of(context).pop();
      }
      return false;
    }

    try {
      await _updateRawQuote();

      final isQuoteSent = await Navigator.of(context).push(
        MaterialPageRoute<bool>(
          builder:
              (_) => PreviewRawQuotePage(rawQuote: _rawQuote, job: widget.job),
        ),
      );
      GetIt.instance<Logger>().i(
        "Raw quote preview popped: isQuoteSent? $isQuoteSent",
      );
      if (isQuoteSent ?? false) {
        Navigator.of(context).pop(true);
      }
      return true;
    } catch (e) {
      GetIt.instance<Logger>().e(e);
      final snackBar = SnackBar(
        content: Text(S.of(context).errorOccuredTryAgain),
      );
      ScaffoldMessenger.of(context).showSnackBar(snackBar);
      return false;
    }
  }

  Future<void> _updateRawQuote() async {
    final authState = context.authState;
    _rawQuote = _rawQuote.copyWith(
      createdBy: authState.user!.documentReference,
      company: authState.getUserCompany(),
      jobCache: widget.job.cache,
      contact: authState.getUserName(
        context,
        anonymousText: S.of(context).anonymous,
      ),
    );

    await Mutations.instance
        .rawQuote(_rawQuote.documentReference)
        .update(_rawQuote);
  }

  @override
  void dispose() {
    scrollController.dispose();
    super.dispose();
  }
}
