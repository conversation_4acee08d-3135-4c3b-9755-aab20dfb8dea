import 'package:done_auth/done_auth.dart';
import 'package:done_flutter/ui/pages/company/quote_generator/widgets/article_row.dart';
import 'package:done_flutter/ui/pages/company/search/widgets/search_results_view.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

class ArticlesList extends StatelessWidget {
  const ArticlesList({
    super.key,
    required this.articles,
    required this.articleGroup,
    this.articleRowType = ArticleRowType.info,
    this.selectedArticles = const [],
    this.onSelectedArticlesUpdate,
  });

  final List<Article> articles;
  final ArticleGroup articleGroup;
  final List<String> selectedArticles;
  final VoidCallback? onSelectedArticlesUpdate;
  final ArticleRowType articleRowType;

  @override
  Widget build(BuildContext context) {
    if (articles.isEmpty) return const EmptySearchResults();
    final isCustomer = context.authState.isUserCustomer();
    final articlesToShow =
        isCustomer
            ? articles
                .where(
                  (article) =>
                      article.prices.customerPrice.amount.value != null,
                )
                .toList()
            : articles;
    return ListView.separated(
      padding: EdgeInsets.zero,
      itemCount: articlesToShow.length,
      itemBuilder:
          (context, index) => ArticleRow(
            calculationOptions: articleGroup.calculationOptions,
            article: articlesToShow[index],
            type: articleRowType,
            onTap:
                articleRowType == ArticleRowType.selectable
                    ? (String value) {
                      selectedArticles.contains(value)
                          ? selectedArticles.remove(value)
                          : selectedArticles.add(value);
                      onSelectedArticlesUpdate?.call();
                    }
                    : null,
            selected: selectedArticles.contains(articles[index].id),
          ),
      separatorBuilder:
          (context, index) => Padding(
            padding: EdgeInsets.only(
              left:
                  articleRowType == ArticleRowType.selectable
                      ? Margins.xxxlarge
                      : Margins.large,
              right: Margins.xlarge,
            ),
            child: const Divider(height: 0),
          ),
    );
  }
}
