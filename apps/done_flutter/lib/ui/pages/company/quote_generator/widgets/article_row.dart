import 'package:done_deductions/done_deductions.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

enum ArticleRowType { selectable, info }

class ArticleRow extends StatelessWidget {
  const ArticleRow({
    required this.article,
    required this.onTap,
    required this.calculationOptions,
    this.selected = false,
    this.type = ArticleRowType.info,
    super.key,
  }) : assert(
         type == ArticleRowType.info ||
             (type == ArticleRowType.selectable && onTap != null),
         'Article row of type selectable must have onTap as non `null`',
       );

  final Article article;
  final bool selected;
  final ValueChanged<String>? onTap;
  final CalculationOptions calculationOptions;
  final ArticleRowType type;

  @override
  Widget build(BuildContext context) {
    final articleCentAmount = article.prices.customerPrice.amount.value;
    final articleAmountToPay =
        articleCentAmount == null
            ? null
            : article.amountToPay(options: calculationOptions);

    if (type == ArticleRowType.info) {
      return _ArticleInfoTile(
        article: article,
        articleAmountToPay: articleAmountToPay,
      );
    }

    return MultiSelectionListTile(
      title: Text(article.title),
      trailing: _ArticleCost(
        articleAmountToPay: articleAmountToPay,
        article: article,
      ),
      subtitle: article.description != null ? Text(article.description!) : null,
      selected: selected,
      onTap: () => onTap!.call(article.id),
    );
  }
}

class _ArticleInfoTile extends StatelessWidget {
  const _ArticleInfoTile({
    required this.article,
    required this.articleAmountToPay,
  });

  final Article article;
  final double? articleAmountToPay;

  @override
  Widget build(BuildContext context) {
    return ListTile(
      title: Text(article.title, style: Theme.of(context).textTheme.bodyMedium),
      subtitle:
          article.description != null
              ? Text(
                article.description!,
                style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                  color: context.doneColors.typographyLowContrast,
                ),
              )
              : null,
      trailing: _ArticleCost(
        article: article,
        articleAmountToPay: articleAmountToPay,
      ),
      minLeadingWidth: 14,
    );
  }
}

class _ArticleCost extends StatelessWidget {
  const _ArticleCost({required this.articleAmountToPay, required this.article});

  final double? articleAmountToPay;
  final Article article;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        if (articleAmountToPay != null)
          Text(
            getPriceString(articleAmountToPay),
            style: Theme.of(context).textTheme.labelMedium,
          ),
        if (article.deduction != DeductionType.none)
          DeductionTypesBadge(
            deductionTypes: [article.deduction],
            shrink: true,
          ),
      ].separatedBy(() => const VerticalMargin.verySmall()),
    );
  }
}
