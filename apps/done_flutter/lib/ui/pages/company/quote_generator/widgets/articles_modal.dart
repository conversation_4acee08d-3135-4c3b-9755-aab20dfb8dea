import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:done_database/done_database.dart';

import 'package:done_localizations/done_localizations.dart';
import 'package:done_flutter/ui/pages/company/quote_generator/widgets/article_row.dart';
import 'package:done_flutter/ui/pages/company/quote_generator/widgets/articles_list.dart';
import 'package:done_flutter/ui/pages/company/search/searchable_mixin.dart';
import 'package:done_flutter/ui/pages/company/search/widgets/done_search_bar.dart';
import 'package:done_flutter/ui/pages/company/shared_generator/widgets/show_generator_modal.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';

Future<List<Article>?> showArticlesModal(
  BuildContext context, {
  required DocumentReference<ArticleGroup> articleGroupRef,
}) async {
  final articles = await showGeneratorModal<List<Article>>(
    context: context,
    child: StreamBuilder(
      stream: articleGroupRef.snapshots(),
      builder: (context, articleGroupSnapshot) {
        return StreamBuilder<List<Article>>(
          stream: GetIt.instance<FinancialGeneratorRepository>().articles(
            articleGroupRef.id,
          ),
          builder: ((context, snapshot) {
            final articles = snapshot.data;
            final articleGroup = articleGroupSnapshot.data?.data();
            if (articles == null || articleGroup == null)
              return const _LoadingArticlesModal();
            return SelectableArticlesModal(
              articles: articles,
              articleGroup: articleGroup,
            );
          }),
        );
      },
    ),
  );
  return articles;
}

class _LoadingArticlesModal extends StatelessWidget {
  const _LoadingArticlesModal();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            Expanded(child: CenteredProgressIndicator()),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: Margins.xlarge),
              child: DoneButton(
                title: Text(S.of(context).cancel),
                style: DoneButtonStyle.neutral,
                onPressed: () => Navigator.of(context).pop(),
              ),
            ),
            const VerticalMargin.medium(),
          ],
        ),
      ),
    );
  }
}

class SelectableArticlesModal extends StatefulWidget {
  const SelectableArticlesModal({
    required this.articles,
    required this.articleGroup,
    super.key,
  });

  final List<Article> articles;
  final ArticleGroup articleGroup;

  @override
  State<SelectableArticlesModal> createState() =>
      _SelectableArticlesModalState();
}

class _SelectableArticlesModalState extends State<SelectableArticlesModal>
    with SearchableMixin {
  final List<String> _selectedArticlesNumbers = [];

  @override
  Widget build(BuildContext context) {
    final filteredArticles =
        widget.articles
            .where(
              (element) => element.title.trim().toLowerCase().contains(
                query.trim().toLowerCase(),
              ),
            )
            .toList();

    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        body: SafeArea(
          child: Column(
            children: <Widget>[
              const VerticalMargin.medium(),
              DoneSearchBar(
                onFocus: () => setSearchingState(true),
                onCancel: () => setSearchingState(false),
                onQueryChanged: updateQuery,
                isSearchActivated: isSearching,
              ),
              Expanded(
                child: ArticlesList(
                  articleGroup: widget.articleGroup,
                  articleRowType: ArticleRowType.selectable,
                  articles: isSearching ? filteredArticles : widget.articles,
                  selectedArticles: _selectedArticlesNumbers,
                  onSelectedArticlesUpdate: () => setState(() {}),
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: Margins.xlarge),
                child: Column(
                  children: <Widget>[
                    DoneButton(
                      title: Text(
                        S
                            .of(context)
                            .addArticles(_selectedArticlesNumbers.length),
                      ),
                      style: DoneButtonStyle.secondary,
                      onPressed:
                          _selectedArticlesNumbers.isNotEmpty
                              ? () {
                                final selectedArticles =
                                    widget.articles
                                        .where(
                                          (article) => _selectedArticlesNumbers
                                              .contains(article.id),
                                        )
                                        .toList();
                                if (selectedArticles.isEmpty) return;
                                Navigator.of(context).pop(selectedArticles);
                              }
                              : null,
                    ),
                    DoneButton(
                      title: Text(S.of(context).cancel),
                      style: DoneButtonStyle.neutral,
                      onPressed: () => Navigator.of(context).pop(),
                    ),
                    const SizedBox.shrink(),
                  ].separatedBy(() => const VerticalMargin.medium()),
                ),
              ),
            ].separatedBy(() => const VerticalMargin.verySmall()),
          ),
        ),
      ),
    );
  }
}
