import 'package:done_database/done_database.dart';
import 'package:done_deductions/done_deductions.dart';
import 'package:done_flutter/ui/pages/company/shared_generator/widgets/send_to_customer_confirmation_popup.dart';
import 'package:done_flutter/ui/pages/quotes/raw_quote_detail_view.dart';
import 'package:done_flutter/utils/extensions/models/job.dart';
import 'package:done_flutter/utils/extensions/models/raw_quote.dart';
import 'package:done_models/done_models.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

class PreviewRawQuotePage extends StatelessWidget {
  const PreviewRawQuotePage({
    super.key,
    required this.rawQuote,
    required this.job,
  });

  final RawQuote rawQuote;
  final Job job;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        titleSpacing: -8,
        centerTitle: false,
        title: DoneAppBarTitle(
          title: Text(job.sendQuoteTitle(context)),
          subtitle: Text(
            S.of(context).draft,
            style: Theme.of(context).textTheme.bodyMedium!.apply(
              color: context.doneColors.uiOnPrimary,
            ),
          ),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.only(bottom: 36),
        child: RawQuoteDetailView(rawQuote: rawQuote, job: job),
      ),
      bottomSheet: SendTotalBottomSheet(
        totalAmount: rawQuote.totalNetAfterTaxDeductions,
        onSendPressed: () => _onSendPressed(context, rawQuote),
      ),
    );
  }

  Future<void> _onSendPressed(BuildContext context, RawQuote rawQuote) async {
    if (rawQuote.isValid) {
      final shouldSend = await showSendToCustomerConfirmationPopup(
        context,
        rawQuote.shouldSendQuoteText(context),
        rawQuote.jobCache!.customerName!,
        rawQuote.totalNetAfterTaxDeductions,
      );
      if (shouldSend) {
        await Mutations.instance
            .rawQuote(rawQuote.documentReference)
            .send(rawQuote);
        Navigator.of(context).pop(true);
      }
    }
  }
}
