import 'package:done_auth/done_auth.dart';
import 'package:done_database/done_database.dart';

import 'package:done_flutter/ui/pages/company/shared_generator/widgets/delete_draft_dialog.dart';
import 'package:done_flutter/utils/extensions/models/raw_quote.dart';
import 'package:done_flutter/utils/first_build_callback.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:done_models/done_models.dart';
import 'package:done_flutter/ui/pages/company/quote_generator/widgets/quote_generator_form.dart';
import 'package:get_it/get_it.dart';

/// Fetches company default quote settings and handles initialising logic
/// between [create mode] and [edit mode] for [QuoteGeneratorForm]
class QuoteGeneratorModal extends StatefulWidget {
  const QuoteGeneratorModal({
    super.key,
    required this.job,
    this.rawQuote,
    this.quote,
    this.fixedPriceJob,
    this.deleteOnCancel = false,
    this.articleGroup,
  });

  final Job job;
  final RawQuote? rawQuote;
  final Quote? quote;
  final FixedPriceJob? fixedPriceJob;
  final bool deleteOnCancel;
  final JobArticleGroup? articleGroup;

  @override
  _QuoteGeneratorModalState createState() => _QuoteGeneratorModalState();
}

class _QuoteGeneratorModalState extends State<QuoteGeneratorModal> {
  RawQuote? _rawQuote;

  @override
  void initState() {
    super.initState();
    //* full raw quote is present only when editing drafts
    _rawQuote = widget.rawQuote;
  }

  /// creates a new raw quote on the backend and passes this data onward to the quote generator form
  Future<void> _createRawQuote(DefaultQuoteSettings settings) async {
    late RawQuote newRawQuote;
    final rawQuotesRepo = GetIt.instance<RawQuotesRepository>();
    if (widget.quote?.rawQuote != null) {
      final rawQuote = await rawQuotesRepo.fetchRawQuote(
        widget.quote!.rawQuote!.id,
      );

      newRawQuote = rawQuote.duplicate(
        createdBy: context.authState.user!.documentReference,
        isReplacing: true,
      );
      await newRawQuote.documentReference.set(newRawQuote);
    } else {
      newRawQuote = await rawQuotesRepo.createRawQuote(widget.job, settings);
    }

    setState(() {
      _rawQuote = newRawQuote;
    });
  }

  @override
  Widget build(BuildContext context) {
    final bookingFlowConfig =
        GetIt.instance<BookingRepository>().bookingFlowConfig();
    return StreamBuilder<DefaultQuoteSettings>(
      stream: GetIt.instance<FinancialGeneratorRepository>()
          .generatorQuoteSettings(widget.job, bookingFlowConfig),
      builder: (
        BuildContext context,
        AsyncSnapshot<DefaultQuoteSettings> snapshot,
      ) {
        if (!snapshot.hasData) return CenteredProgressIndicator();
        // TODO: Rework how intiial quote is created so we don't need to first create an empty template and then update it
        if (_rawQuote == null) {
          return FirstBuildCallback(
            onFirstBuild: () => _createRawQuote(snapshot.data!),
            child: CenteredProgressIndicator(),
          );
        }

        if (widget.quote == null && widget.rawQuote == null) {
          if (widget.fixedPriceJob != null) {
            _rawQuote = RawQuote.fromFixedPriceJob(
              fixedPriceJob: widget.fixedPriceJob!,
              rawQuote: _rawQuote!,
              settings: snapshot.data!,
            );
            _rawQuote!.documentReference.set(_rawQuote!);
          } else if (widget.articleGroup != null) {
            _rawQuote = RawQuote.withArticleGroup(
              rawQuote: _rawQuote!,
              articleGroup: widget.articleGroup!,
            );
            _rawQuote!.documentReference.set(_rawQuote!);
          } else {
            _rawQuote = RawQuote.fromSettings(
              rawQuote: _rawQuote!,
              settings: snapshot.data!,
            );
            _rawQuote!.documentReference.set(_rawQuote!);
          }
        }

        return QuoteGeneratorForm(
          job: widget.job,
          rawQuote: _rawQuote!,
          defaultQuoteSettings: snapshot.data!,
          isExistingDraft: widget.rawQuote != null,
          articleGroup: widget.articleGroup,
          onCancel: (context, hasChanges, quote) async {
            if (hasChanges) {
              return _promptKeepingDraft(context, quote);
            } else if (widget.rawQuote == null || widget.deleteOnCancel) {
              // New, unchanged draft. Delete.
              await _rawQuote!.documentReference.delete();
            }
            return true;
          },
        );
      },
    );
  }

  /// Prompts the user with a dialog asking if user wants to keep draft, delete
  /// it or continue editing, after cancelling the modal.
  ///
  /// Returns if navigation should proceed or not.
  Future<bool> _promptKeepingDraft(
    BuildContext context,
    RawQuote updatedQuote,
  ) async {
    final selection = await showShouldDeleteDraftDialog(context);
    if (selection == LeavingGeneratorSelection.deleteDraft) {
      await _rawQuote!.documentReference.delete();
    } else if (selection == LeavingGeneratorSelection.saveDraft) {
      await Mutations.instance
          .rawQuote(updatedQuote.documentReference)
          .update(updatedQuote);
    }

    return (selection != LeavingGeneratorSelection.cancel);
  }
}
