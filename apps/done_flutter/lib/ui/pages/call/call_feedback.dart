import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:done_database/done_database.dart';
import 'package:done_flutter/utils/ask_reason.dart';
import 'package:done_flutter/utils/extensions/enums/low_call_quality_reason.dart';
import 'package:done_flutter/utils/extensions/enums/message_type.dart';
import 'package:done_models/done_models.dart';
import 'package:done_localizations/done_localizations.dart';

import 'package:done_flutter/ui/widgets/chat/chat_avatar.dart';
import 'package:done_flutter/ui/widgets/chat/chat_ui_mixin.dart';
import 'package:done_image/done_image.dart';
import 'package:flutter/material.dart';
import 'package:done_auth/done_auth.dart';
import 'package:done_flutter/utils/extensions/string_extensions.dart';
import 'package:done_ui/done_ui.dart';

class CallFeedback extends StatefulWidget {
  const CallFeedback({
    super.key,
    required this.role,
    required this.job,
    required this.message,
    required this.durationText,
  });

  final CallFeedbackRole role;
  final Job job;
  final CallChatMessage message;
  final String durationText;

  @override
  _CallFeedbackState createState() => _CallFeedbackState();
}

class _CallFeedbackState extends State<CallFeedback> with ChatUiMixin {
  double rating = 0;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          ChatAvatar(ImageAssets.videocall),
          const HorizontalMargin.medium(),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Text(
                  S.of(context).videoCallFinished,
                  style: MessageType.call.textStyle(context),
                ),
                Text(
                  widget.durationText.capitalize(),
                  style: Theme.of(context).textTheme.labelMedium!.copyWith(
                    color: context.doneColors.typographyMediumContrast,
                  ),
                ),
                const VerticalMargin.medium(),
                _buildRatingCard(context),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<void> handleSubmit(BuildContext context, double rating) async {
    final callFeedbackEntry = CallFeedbackEntry(
      author: context.authState.user!.documentReference,
      job: widget.job.documentReference,
      message: widget.message.documentReference!,
      rating: rating,
      role: widget.role.name,
    );

    String? pickedReason;
    String? customReason;
    if (rating < 4) {
      final result = await askReason<LowCallQualityReason>(
        context: context,
        title: Text(S.of(context).callQualityFeedback),
        otherTitle: Text(S.of(context).callFeedbackOtherReasonTitle),
        reasons: LowCallQualityReason.values,
        reasonTitleBuilder: (context, reason) => Text(reason.title(context)),
        actionTitle: S.of(context).offerSend,
      );

      if (result == null) return;
      pickedReason = result.reason.title(context);
      customReason = result.otherText?.trim();
    }
    // TODO: add call-feedback model and converter
    final ref = FirebaseFirestore.instance.collection('callFeedback').doc();
    final deviceData = await ClientInfo.current;
    await Mutations.instance
        .calls(ref)
        .createFeedback(
          callFeedbackEntry,
          widget.role,
          pickedReason,
          customReason,
          widget.message.documentReference!,
          extra: deviceData.toMap(),
        );
  }

  void handleRatingChanged(double newRating) {
    setState(() {
      rating = newRating;
    });
  }

  Future<void> handleClose() async {
    await widget.message.documentReference!.update(<String, dynamic>{
      widget.role == CallFeedbackRole.caller
              ? 'callerSkippedFeedback'
              : 'receiverSkippedFeedback':
          true,
    });
  }

  Widget _buildRatingCard(BuildContext context) {
    return DecoratedBox(
      decoration: chatBoxDecoration(context),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            Text(
              S.of(context).callQualityFeedback,
              style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                color: context.doneColors.typographyHightContrast,
              ),
            ),
            const VerticalMargin.small(),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: <Widget>[
                IconButton(
                  visualDensity: VisualDensity.compact,
                  icon: const Icon(Icons.thumb_up_off_alt_outlined),
                  onPressed: () => handleSubmit(context, 5),
                  color: context.doneColors.uiPurple,
                ),
                IconButton(
                  visualDensity: VisualDensity.compact,
                  icon: const Icon(Icons.thumb_down_off_alt_outlined),
                  onPressed: () => handleSubmit(context, 1),
                  color: context.doneColors.uiPurple,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
