import 'package:done_calls/done_calls.dart';
import 'package:done_flutter/ui/pages/call/video.dart';
import 'package:done_flutter/ui/widgets/user_avatar.dart';
import 'package:done_image/done_image.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

class MinimizedCallView extends StatelessWidget {
  const MinimizedCallView({
    super.key,
    required this.onMinimizedCallTap,
    required this.call,
    required this.thumbnailCorner,
    required this.onCornerChanged,
    this.streamIdentifier = VideoStreamIdentifier.remote,
  });

  final OnMinimizedCallTap onMinimizedCallTap;
  final VideoCall call;
  final Alignment thumbnailCorner;
  final ValueChanged<Alignment> onCornerChanged;
  final VideoStreamIdentifier streamIdentifier;

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Stack(
        children: [
          DraggableIntoCorners(
            corner: thumbnailCorner,
            onSwipedToCorner: onCornerChanged,
            child: GestureDetector(
              onTap: onMinimizedCallTap,
              child: VideoStreamThumbnail(
                call: call,
                stream: streamIdentifier,
                placeholder: Stack(
                  children: [
                    Positioned(
                      top: 10,
                      right: 16,
                      child: Image.asset(
                        ImageAssets.cameraOff,
                        color: context.doneColors.uiPrimary,
                        height: 20,
                        width: 20,
                      ),
                    ),
                    Center(child: UserAvatar(call.remoteUser, radius: 30)),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class VideoStreamThumbnail extends StatelessWidget {
  const VideoStreamThumbnail({
    super.key,
    required this.call,
    required this.stream,
    required this.placeholder,
  });

  final VideoCall call;
  final VideoStreamIdentifier stream;
  final Widget placeholder;

  @override
  Widget build(BuildContext context) {
    return DecoratedBox(
      decoration: BoxDecoration(
        color: context.doneColors.uiBlack.withValues(alpha: 0.5),
        border: Border.all(color: Colors.transparent),
        borderRadius: BorderRadius.circular(12),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: call.getVideoStream(
          key: Key("Thumbnail ${stream.name}"),
          stream: stream,
          width: 120,
          height: 180,
          placeholder: placeholder,
        ),
      ),
    );
  }
}
