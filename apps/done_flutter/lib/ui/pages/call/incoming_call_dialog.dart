import 'dart:async';
import 'dart:math' as math;

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:done_calls/done_calls.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_flutter/ui/widgets/user_avatar.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

/// Shows a shaky dialog with information about incoming call
///
/// It also loops a ringtone
class IncomingCallDialog extends StatefulWidget {
  const IncomingCallDialog({
    required this.onAnswer,
    required this.request,
    super.key,
  });

  final VoidCallback onAnswer;
  final CallKeepCallData request;

  @override
  State<IncomingCallDialog> createState() => _IncomingCallDialogState();
}

class _IncomingCallDialogState extends State<IncomingCallDialog>
    with SingleTickerProviderStateMixin {
  late final AnimationController _controller;
  late final Timer _timer;
  late bool _isCallAccepted;
  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 400),
    );
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) async {
      await _controller.forward();
      _controller.reset();
    });
    _isCallAccepted = false;
  }

  @override
  Widget build(BuildContext context) {
    return LoopingAudioAsset(
      assetPath: 'assets/ringtone.mp3',
      child: AnimatedBuilder(
        animation: _controller,
        builder: (BuildContext context, Widget? child) {
          final dx = math.sin(2 * _controller.value * 2 * math.pi) * 20;
          return Transform.translate(offset: Offset(dx, 0), child: child);
        },
        child: DoneDialog(
          title: Text(S.of(context).videoCall),
          content: [
            StreamBuilder<DocumentSnapshot<User>?>(
              stream: widget.request.remoteUser(),
              builder: (context, snapshot) {
                if (!snapshot.hasData || snapshot.data?.data() == null) {
                  return CenteredProgressIndicator();
                }

                final user = snapshot.data!.data()!;
                return Column(
                  children: [
                    NonFetchingUserAvatar(
                      user: snapshot.data!.data()!,
                      radius: 32,
                    ),
                    const VerticalMargin.medium(),
                    Text(user.fullNameWithFallback(context)),
                  ],
                );
              },
            ),
            const VerticalMargin.large(),
            if (_isCallAccepted)
              const DoneAdaptiveLoadingIndicator()
            else
              _AnswerDeclineCallButtons(
                onAnswer: () {
                  setState(() => _isCallAccepted = true);
                  _timer.cancel();
                  widget.onAnswer();
                },
                onDecline: widget.request.decline,
              ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    _timer.cancel();
    super.dispose();
  }
}

class _AnswerDeclineCallButtons extends StatelessWidget {
  const _AnswerDeclineCallButtons({
    required this.onAnswer,
    required this.onDecline,
  });
  final VoidCallback onAnswer;
  final VoidCallback onDecline;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        DoneButton(title: Text(S.of(context).callAnswer), onPressed: onAnswer),
        const VerticalMargin.medium(),
        DoneButton(
          style: DoneButtonStyle.negative,
          title: Text(S.of(context).callDecline),
          onPressed: onDecline,
        ),
      ],
    );
  }
}
