import 'package:done_localizations/done_localizations.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';

class UnsupportedCallDialog extends StatelessWidget {
  const UnsupportedCallDialog({
    super.key,
    required this.clearCall,
    required this.declineCall,
  });
  final VoidCallback clearCall;
  final AsyncCallback declineCall;

  @override
  Widget build(BuildContext context) {
    return DoneDialog(
      title: Text(S.of(context).callIncoming),
      content: [
        Text(S.of(context).callAnswerInAppOnly),
        <PERSON><PERSON><PERSON><PERSON>(
          style: DoneButtonStyle.neutral,
          title: Text(S.of(context).genericContinue),
          onPressed: clearCall,
        ),
        Done<PERSON><PERSON><PERSON>(
          style: DoneButtonStyle.negative,
          title: Text(S.of(context).callDecline),
          onPressed: declineCall,
        ),
      ].separatedBy(() => const VerticalMargin.small()),
    );
  }
}
