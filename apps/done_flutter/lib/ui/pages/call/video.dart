import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:done_calls/done_calls.dart';
import 'package:done_flutter/ui/pages/call/minimized_call_view.dart';
import 'package:done_flutter/ui/widgets/call/call_actions.dart';
import 'package:done_flutter/utils/formatted_user_name.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:done_auth/done_auth.dart';
import 'package:done_flutter/ui/widgets/user_avatar.dart' show UserAvatar;

Stream<T> unwrapStreamFuture<T>(Future<Stream<T>> input) async* {
  final stream = await input;

  await for (final item in stream) {
    yield item;
  }
}

class OpacityNotifier extends ValueNotifier<double> {
  OpacityNotifier(super.value);
  static const opacityDuration = 500;

  void updateOpacity() {
    value = 0.0;
    Future.delayed(const Duration(milliseconds: opacityDuration), () {
      value = 1.0;
    });
  }
}

typedef OnMinimize = void Function();
typedef OnMinimizedCallTap = void Function();

class VideoPage extends StatefulWidget {
  const VideoPage({
    super.key,
    required this.isMinimized,
    required this.onMinimize,
    required this.call,
    required this.onMinimizedCallTap,
  });

  final VideoCall call;
  final bool isMinimized;
  final OnMinimize onMinimize;
  final OnMinimizedCallTap onMinimizedCallTap;

  @override
  _VideoPageState createState() => _VideoPageState();
}

class _VideoPageState extends State<VideoPage> {
  Alignment thumbnailCorner = Alignment.topRight;
  final opacityNotifier = OpacityNotifier(1);

  @override
  Widget build(BuildContext context) {
    if (widget.isMinimized) {
      return MinimizedCallView(
        call: widget.call,
        onMinimizedCallTap: widget.onMinimizedCallTap,
        thumbnailCorner: thumbnailCorner,
        onCornerChanged:
            (newCorner) => setState(() => thumbnailCorner = newCorner),
      );
    }

    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) widget.call.toggleFullScreen();
    });

    return Scaffold(
      body: ColoredBox(color: Colors.black, child: _buildCall(context)),
    );
  }

  Widget _buildCall(BuildContext context) {
    const curve = Curves.ease;
    return Stack(
      children: <Widget>[
        ValueListenableBuilder(
          valueListenable: opacityNotifier,
          builder: (context, opacity, child) {
            return AnimatedOpacity(
              curve: curve,
              opacity: opacity,
              duration: const Duration(
                milliseconds: OpacityNotifier.opacityDuration,
              ),
              child: _buildVideoStream(context),
            );
          },
        ),
        CallActionsSheet(
          call: widget.call,
          onMinimize: widget.onMinimize,
          animationOpacity: opacityNotifier,
        ),
        ValueListenableBuilder(
          valueListenable: opacityNotifier,
          builder: (context, opacity, child) {
            return AnimatedOpacity(
              opacity: opacity,
              curve: curve,
              duration: const Duration(
                milliseconds: OpacityNotifier.opacityDuration,
              ),
              child: _buildThumbnail(context),
            );
          },
        ),
      ],
    );
  }

  Widget _buildVideoStream(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        widget.call.toggleFullScreen();
      },
      onVerticalDragUpdate: (details) {
        const sensitivity = 10;
        if (details.delta.dy > sensitivity) {
          // Minimize call on swipe down
          widget.onMinimize();
        }
      },
      child: StreamBuilder<bool>(
        stream: widget.call.isFrontFacing,
        initialData: widget.call.isFrontFacing.value,
        builder: (context, isCameraFrontFacing) {
          final identifier =
              context.authState.isUserCustomer() && !isCameraFrontFacing.data!
                  ? VideoStreamIdentifier.local
                  : VideoStreamIdentifier.remote;
          return widget.call.getVideoStream(
            key: Key("Main video ${identifier.name}"),
            stream: identifier,
            width: MediaQuery.of(context).size.width,
            height: MediaQuery.of(context).size.height,
            placeholder: Column(
              children: <Widget>[
                const VerticalMargin(margin: 200),
                UserAvatar(
                  widget.call.role == VideoCallRole.receiver
                      ? context.authState.user!.documentReference
                      : widget.call.remoteUser,
                  radius: 40,
                ),
                const VerticalMargin(margin: 15),
                _buildReceiverName(),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildThumbnail(BuildContext context) {
    return SafeArea(
      child: DraggableIntoCorners(
        corner: thumbnailCorner,
        onSwipedToCorner:
            (newCorner) => setState(() => thumbnailCorner = newCorner),
        child: StreamBuilder<bool>(
          stream: widget.call.isFrontFacing,
          initialData: widget.call.isFrontFacing.value,
          builder: (context, isCameraFrontFacing) {
            return VideoStreamThumbnail(
              call: widget.call,
              stream:
                  context.authState.isUserCustomer() &&
                          !isCameraFrontFacing.data!
                      ? VideoStreamIdentifier.remote
                      : VideoStreamIdentifier.local,
              placeholder: Center(
                child: UserAvatar(
                  context.authState.isUserCustomer()
                      ? widget.call.remoteUser
                      : context.authState.user!.documentReference,
                  radius: 30,
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  StreamBuilder<DocumentSnapshot<User>> _buildReceiverName() {
    return StreamBuilder<DocumentSnapshot<User>>(
      stream: widget.call.remoteUser.snapshots(),
      builder: (context, snapshot) {
        final displayName = formattedUserName(snapshot);
        return Text(
          displayName,
          style: Theme.of(
            context,
          ).textTheme.headlineMedium!.copyWith(color: Colors.white),
        );
      },
    );
  }
}
