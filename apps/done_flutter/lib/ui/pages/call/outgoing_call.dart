import 'package:done_calls/done_calls.dart';
import 'package:done_flutter/ui/pages/call/minimized_call_view.dart';
import 'package:done_flutter/ui/pages/call/video.dart';
import 'package:done_flutter/ui/widgets/call/call_actions.dart';
import 'package:done_flutter/utils/formatted_user_name.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart'
    show DocumentReference, DocumentSnapshot;
import 'package:done_flutter/ui/widgets/user_avatar.dart' show UserAvatar;
import 'package:done_localizations/done_localizations.dart';

class OutgoingCall extends StatefulWidget {
  const OutgoingCall({
    super.key,
    required this.remoteUser,
    required this.isMinimized,
    required this.onMinimize,
    required this.onMinimizedCallTap,
    this.call,
  });

  final VideoCall? call;
  final DocumentReference<User> remoteUser;
  final bool isMinimized;
  final OnMinimize onMinimize;
  final OnMinimizedCallTap onMinimizedCallTap;

  @override
  State<OutgoingCall> createState() => _OutgoingCallState();
}

class _OutgoingCallState extends State<OutgoingCall> {
  Alignment thumbnailCorner = Alignment.topRight;

  @override
  Widget build(BuildContext context) {
    if (widget.call == null) return const SizedBox();

    return LoopingAudioAsset(
      assetPath: 'assets/tone.m4a',
      child: widget.isMinimized ? _buildMinimizedView() : _buildCall(context),
    );
  }

  Widget _buildMinimizedView() {
    return MinimizedCallView(
      onMinimizedCallTap: widget.onMinimizedCallTap,
      call: widget.call!,
      streamIdentifier: VideoStreamIdentifier.local,
      thumbnailCorner: thumbnailCorner,
      onCornerChanged:
          (newCorner) => setState(() => thumbnailCorner = newCorner),
    );
  }

  Widget _buildCall(BuildContext context) {
    return Scaffold(
      body: StreamBuilder<DocumentSnapshot<User>>(
        stream: widget.remoteUser.snapshots(),
        builder:
            (context, snapshot) => ColoredBox(
              color: Colors.black,
              child: Stack(
                children: <Widget>[
                  _buildVideo(context),
                  _buildCallActions(context),
                  _buildUserAvatar(context, snapshot),
                ],
              ),
            ),
      ),
    );
  }

  Widget _buildVideo(BuildContext context) {
    return StreamBuilder<bool>(
      stream: widget.call!.isCameraEnabled,
      initialData: widget.call!.isCameraEnabled.value,
      builder: (context, isCameraEnabled) {
        return (isCameraEnabled.data ?? false)
            ? GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: widget.call!.toggleFullScreen,
              child: widget.call!.getVideoStream(
                stream: VideoStreamIdentifier.local,
                width: MediaQuery.of(context).size.width,
                height: MediaQuery.of(context).size.height,
                placeholder: UserAvatar(widget.remoteUser, radius: 39),
              ),
            )
            : const SizedBox();
      },
    );
  }

  Widget _buildCallActions(BuildContext context) {
    return CallActionsSheet(
      call: widget.call!,
      isOutgoing: true,
      onMinimize: widget.onMinimize,
    );
  }

  Widget _buildUserAvatar(
    BuildContext context,
    AsyncSnapshot<DocumentSnapshot<User>> snapshot,
  ) {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          UserAvatar(widget.remoteUser, radius: 40),
          const VerticalMargin(margin: 25),
          Text(
            formattedUserName(snapshot),
            style: Theme.of(context).textTheme.headlineMedium!.apply(
              color: context.doneColors.uiAlwaysPureWhite,
            ),
          ),
          const VerticalMargin(margin: 5),
          StreamBuilder<bool>(
            stream: widget.call?.isCameraEnabled,
            initialData: widget.call?.isCameraEnabled.value,
            builder:
                (context, isCameraEnabled) => Text(
                  (isCameraEnabled.data ?? false)
                      ? S.of(context).startingVideoCall
                      : S.of(context).startingAudioCall,
                  style: Theme.of(
                    context,
                  ).textTheme.titleMedium!.copyWith(color: Colors.white60),
                ),
          ),
          const VerticalMargin(margin: 200),
        ],
      ),
    );
  }
}
