import 'package:done_localizations/done_localizations.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

Future<bool> showCallPermissionsDialog(BuildContext context) async {
  return showConfirmationPopup(
    context: context,
    type: PopupType.dialog,
    title: Text(S.of(context).callPermissionsDialogTitle),
    message: Text(S.of(context).callPermissionsDialogMessage),
    actionTitle: Text(S.of(context).allow),
    barrierDismissible: false,
  );
}
