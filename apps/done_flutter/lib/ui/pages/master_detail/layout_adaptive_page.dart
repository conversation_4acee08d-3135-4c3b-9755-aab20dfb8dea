import 'package:done_flutter/core/blocs/master_detail_cubit/index_selection_cubit.dart';
import 'package:done_flutter/ui/widgets/master_detail_base.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

/// Shows the [child] on normal layouts and shows [MasterDetailBase] on wide layouts
///
/// The [MasterDetailBase] depends on [IndexSelectionCubit] to show the appropriate value in detail page.
/// So, it should have [IndexSelectionCubit] above in the tree or an error would be thrown
class LayoutAdaptivePage<B extends BlocBase<T>, T extends Object?>
    extends StatelessWidget {
  const LayoutAdaptivePage({
    super.key,
    required this.appBar,
    required this.masterViewBuilder,
    required this.detailViewBuilder,
    required this.child,
    required this.stateExtractor,
  });

  /// The state extractor of the layout from which the details is shown/built
  final String? Function(T) stateExtractor;

  /// The app bar to be shown when layout is not wide, or be shown on master view for wide layout
  final PreferredSizeWidget appBar;

  /// The builder for master view (normally shown on left and shows a list)
  final WidgetBuilder masterViewBuilder;

  /// The builder for detail view (normally shown on right and shows details for selected item)
  final DetailViewBuilder detailViewBuilder;

  /// The widget to be shown on normal layout(mobile phones and small screens)
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isUltraWideLayout =
            constraints.maxWidth > Layouts.ultraWideLayout;

        return Scaffold(
          appBar: isUltraWideLayout ? null : appBar,
          body:
              isUltraWideLayout
                  ? MasterDetailBase<B, T>(
                    masterViewBuilder:
                        (context) => Scaffold(
                          appBar: appBar,
                          body: masterViewBuilder(context),
                        ),
                    detailViewBuilder: detailViewBuilder,
                    stateExtractor: stateExtractor,
                  )
                  : child,
        );
      },
    );
  }
}
