import 'package:done_flutter/core/blocs/master_detail_cubit/index_selection_cubit.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

/// An app bar that hides back button for [Layouts.ultraWideLayout] that are part of master detail views
///
/// Should be used in combination with `LayoutAdaptivePage` for the detail view
class LayoutAdaptiveAppBar extends StatelessWidget
    implements PreferredSizeWidget {
  const LayoutAdaptiveAppBar({
    super.key,
    this.title,
    this.actions,
    this.automaticallyImplyLeading = true,
  });

  final Widget? title;
  final List<Widget>? actions;
  final bool automaticallyImplyLeading;

  @override
  Widget build(BuildContext context) {
    final isUltraWideLayout =
        MediaQuery.of(context).size.width > Layouts.ultraWideLayout;
    final isInDetailView = context.readOrNull<IndexSelectionCubit>() != null;

    return AppBar(
      centerTitle: false,
      elevation: isUltraWideLayout ? 1 : 0,
      titleSpacing: isUltraWideLayout ? 16 : -8,
      title: title,
      actions: actions,
      // only show leading if it's not a wide layout or not in detail view
      automaticallyImplyLeading: !isUltraWideLayout || !isInDetailView,
      backgroundColor:
          isUltraWideLayout
              ? context.doneColors.uiBgSheet
              : context.doneColors.uiPrimary,
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

extension ReadOrNull on BuildContext {
  T? readOrNull<T>() {
    try {
      return read<T>();
    } on ProviderNotFoundException catch (_) {
      return null;
    }
  }
}
