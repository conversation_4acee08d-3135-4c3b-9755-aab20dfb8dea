import 'package:done_auth/done_auth.dart';
import 'package:done_database/done_database.dart';
import 'package:done_flutter/core/router/routes/project_routes.dart';
import 'package:done_flutter/utils/dialogs/quote_dialogs.dart';
import 'package:done_flutter/utils/extensions/models/raw_quote.dart';
import 'package:done_flutter/utils/pdf_util.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:done_localizations/done_localizations.dart';

class QuoteDetailAction {
  const QuoteDetailAction({
    required this.title,
    required this.onSelected,
    this.buttonStyle = DoneButtonStyle.neutral,
  });

  final String Function(BuildContext) title;
  final void Function(BuildContext, Quote, RawQuote) onSelected;
  final DoneButtonStyle buttonStyle;
}

List<QuoteDetailAction> quoteActions(
  BuildContext context,
  Quote quote,
  RawQuote rawQuote,
  Job job,
) {
  final showCraftsmanActions =
      context.authState.isUserCraftsman() &&
      quote.rawQuote != null &&
      job.isInteractive;

  return [
    QuoteDetailAction(
      title: (context) => S.of(context).viewAsPdf,
      onSelected:
          (context, quote, rawQuote) => viewAsPdf(
            context: context,
            storagePath: quote.fileRef!,
            pushRoute: (url) {
              final params = ViewQuotePdfRouteParams(
                projectId: rawQuote.job.id,
                quoteId: quote.id!,
                url: url,
              );
              ViewQuotePdfRoute(params: params).navigate(context);
            },
          ),
    ),
    if (showCraftsmanActions)
      QuoteDetailAction(
        title: (context) => S.of(context).createCopy,
        onSelected: duplicateQuoteAndPresentGenerator,
      ),
    if (showCraftsmanActions)
      QuoteDetailAction(
        title: (context) => S.of(context).replaceQuote,
        onSelected: (context, quote, rawQuote) async {
          return duplicateQuoteAndPresentGenerator(
            context,
            quote,
            rawQuote,
            isReplacing: true,
          );
        },
      ),
    if (showCraftsmanActions && quote.status == QuoteStatus.pending)
      QuoteDetailAction(
        title: (context) => S.of(context).cancelQuoteTitle,
        buttonStyle: DoneButtonStyle.negative,
        onSelected: cancelQuote,
      ),
  ];
}

Future<void> cancelQuote(
  BuildContext context,
  Quote quote,
  RawQuote rawQuote,
) async {
  final confirmCancellation = await showConfirmationPopup(
    context: context,
    title: Text(S.of(context).cancelQuoteTitle),
    message: Text(S.of(context).cancelQuoteDescription),
    actionTitle: Text(S.of(context).cancelQuoteTitle),
    cancelTitle: Text(S.of(context).close),
  );
  if (!confirmCancellation) return;
  return Mutations.instance.quote(quote.documentReference!).cancel();
}

Future<void> duplicateQuoteAndPresentGenerator(
  BuildContext context,
  Quote quote,
  RawQuote rawQuote, {
  bool isReplacing = false,
}) async {
  final copy = rawQuote.duplicate(
    createdBy: context.authState.user!.documentReference,
    isReplacing: isReplacing,
  );

  await copy.documentReference.set(copy);

  final job = (await rawQuote.job.get()).data()!;

  await showQuoteGenerator(
    context: context,
    job: job,
    rawQuote: copy,
    deleteOnCancel: true,
  );
}
