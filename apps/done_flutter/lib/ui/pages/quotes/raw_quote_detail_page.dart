import 'package:done_database/done_database.dart';
import 'package:done_flutter/ui/pages/quotes/quote_actions.dart';
import 'package:done_flutter/ui/pages/quotes/raw_quote_detail_view.dart';
import 'package:done_flutter/utils/extensions/enums/quote.dart';
import 'package:done_models/done_models.dart';
import 'package:done_auth/done_auth.dart';
import 'package:done_flutter/ui/widgets/accept_decline_controls.dart';
import 'package:done_flutter/utils/dialogs/quote_dialogs.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';

class RawQuoteDetailPage extends StatelessWidget {
  const RawQuoteDetailPage({
    super.key,
    required this.quote,
    required this.rawQuote,
  });

  final Quote quote;
  final RawQuote rawQuote;

  @override
  Widget build(BuildContext context) {
    final isUserCustomer = context.authState.isUserCustomer();
    return FutureBuilder(
      future: GetIt.instance<JobsRepository>().fetchJob(rawQuote.job.id),
      builder: (context, snapshot) {
        final job = snapshot.data;
        if (job == null) return const LoadingPage();
        return Scaffold(
          appBar: AppBar(
            titleSpacing: -8,
            centerTitle: false,
            title: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  quote.title(context),
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                Text(
                  quote.informationText(context),
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
              ],
            ),
            actions: <Widget>[
              PopupMenuButton<QuoteDetailAction>(
                onSelected:
                    (action) => action.onSelected(context, quote, rawQuote),
                itemBuilder:
                    (BuildContext context) =>
                        quoteActions(context, quote, rawQuote, job)
                            .map(
                              (action) => PopupMenuItem<QuoteDetailAction>(
                                value: action,
                                child: Text(action.title(context)),
                              ),
                            )
                            .toList(),
              ),
            ],
            elevation: 0,
          ),
          floatingActionButton:
              (isUserCustomer && quote.status == QuoteStatus.pending)
                  ? AcceptDeclineControls(
                    onAccept: () => showQuoteAcceptConfirmation(context, quote),
                    onDecline: () => showQuoteDeclineDialog(context, quote),
                  )
                  : null,
          floatingActionButtonLocation:
              FloatingActionButtonLocation.centerFloat,
          body: RawQuoteDetailView(rawQuote: rawQuote, quote: quote, job: job),
        );
      },
    );
  }
}
