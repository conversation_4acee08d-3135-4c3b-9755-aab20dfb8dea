import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:cross_file/cross_file.dart';
import 'package:done_auth/done_auth.dart';
import 'package:done_database/done_database.dart';
import 'package:done_flutter/core/services/firebase_storage_service.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_flutter/ui/widgets/deduction_type_selector.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';

class PdfQuoteModal extends StatefulWidget {
  const PdfQuoteModal({super.key, required this.pdfFile, required this.job});

  final XFile pdfFile;
  final Job job;

  @override
  State<PdfQuoteModal> createState() => _PdfQuoteModalState();
}

class _PdfQuoteModalState extends State<PdfQuoteModal> {
  late final GlobalKey<FormState> _pdfQuoteFormKey;
  late final DocumentReference<ChatMessage> _messageRef;
  late final Reference? _fileRef;
  late bool _isUploading;
  late DeductionType _deductionType;
  int? _amountIncludingVat;
  int? _amountToPay;

  @override
  void initState() {
    super.initState();
    _pdfQuoteFormKey = GlobalKey<FormState>();
    _deductionType = DeductionType.rot;
    _messageRef = widget.job.documentReference
        .collection('messages')
        .doc()
        .withChatMessageConverter(job: widget.job);
    _isUploading = true;
    _uploadPdf();
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _pdfQuoteFormKey,
      autovalidateMode: AutovalidateMode.onUserInteraction,
      child: DonePopup(
        title: Text(S.of(context).uploadPDFQuote),
        subtitle: Text(widget.pdfFile.name),
        content: [
          DeductionTypeSelector(
            currentDeductionType: _deductionType,
            deductionTypes: DeductionType.values,
            onChange: (type) {
              setState(() => _deductionType = type);
            },
          ),
          const VerticalMargin.medium(),
          TextFormField(
            keyboardType: TextInputType.number,
            style: Theme.of(
              context,
            ).textTheme.bodyMedium!.apply(color: context.doneColors.uiBlack),
            onChanged: (change) {
              setState(() => _amountIncludingVat = int.tryParse(change));
            },
            validator: FormValidators.compose(context, [
              FormValidators.required,
              FormValidators.validAmount,
              FormValidators.validInt,
            ]),
            decoration: DoneFormFieldHolder.inputDecoration(
              label: Text('${S.of(context).amount} (${S.of(context).inclVat})'),
              textStyle: Theme.of(context).textTheme.bodyLarge,
              context: context,
            ),
          ),
          const VerticalMargin.large(),
          TextFormField(
            keyboardType: TextInputType.number,
            style: TextStyle(color: context.doneColors.uiBlack),
            onChanged: (change) {
              setState(() => _amountToPay = int.tryParse(change));
            },
            validator: FormValidators.compose(context, [
              FormValidators.required,
              FormValidators.validAmount,
              FormValidators.validInt,
            ]),
            decoration: DoneFormFieldHolder.inputDecoration(
              label: Text(S.of(context).toPayAfterTax),
              textStyle: Theme.of(context).textTheme.bodyLarge,
              context: context,
            ),
          ),
          const VerticalMargin.medium(),
          DoneAsyncAction(
            action: _sendPdfQuote,
            builder:
                (context, actionOrNull, isLoading) => DoneButton(
                  title:
                      _isUploading || isLoading
                          ? const DoneAdaptiveLoadingIndicator()
                          : Text(S.of(context).sendQuote),
                  style: DoneButtonStyle.secondary,
                  onPressed: _isUploading ? null : actionOrNull,
                ),
          ),
          const VerticalMargin.medium(),
          DoneButton(
            title: Text(S.of(context).cancel),
            style: DoneButtonStyle.neutral,
            onPressed: () {
              Navigator.of(context).pop();
            },
          ),

          // To compensate for soft-keyboard on mobile
          VerticalMargin(margin: MediaQuery.of(context).viewInsets.bottom),
        ],
      ),
    );
  }

  Future<void> _uploadPdf() async {
    _fileRef = await GetIt.instance<FirebaseStorageService>().uploadPdf(
      widget.pdfFile,
      _messageRef,
      widget.job.documentReference,
    );

    setState(() => _isUploading = false);
  }

  Future<void> _sendPdfQuote() async {
    if (_pdfQuoteFormKey.currentState!.validate()) {
      final sender = context.authState.user!.documentReference;
      final quote = _createQuoteForMessage(sender);

      await Mutations.instance
          .messages(_messageRef)
          .sendQuoteMessage(quote, widget.job.documentReference);

      Navigator.of(context).pop();
    }
  }

  Quote _createQuoteForMessage(DocumentReference<User> sender) {
    return Quote(
      fileRef: _fileRef?.fullPath,
      customer: widget.job.customer,
      company: widget.job.company,
      filename: widget.pdfFile.name,
      status: QuoteStatus.pending,
      deductions: [_deductionType],
      type: QuoteType.offer,
      amountIncVat: _amountIncludingVat!,
      sender: sender,
      amountAfterTaxDeductions: _amountToPay!,
    );
  }
}
