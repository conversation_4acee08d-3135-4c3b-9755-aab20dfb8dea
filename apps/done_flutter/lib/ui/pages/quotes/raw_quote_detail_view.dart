import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:done_flutter/core/router/routes/project_routes.dart';
import 'package:done_flutter/ui/pages/company/shared_details/widgets/details_summary_section.dart';
import 'package:done_flutter/ui/pages/company/shared_details/widgets/line_item_detail_entry.dart';
import 'package:done_flutter/ui/pages/quotes/quote_actions.dart';
import 'package:done_flutter/ui/widgets/cards/company_card.dart';
import 'package:done_flutter/ui/widgets/cards/customer_card.dart';
import 'package:done_flutter/ui/widgets/expandable_text.dart';
import 'package:done_image/done_image.dart';
import 'package:done_flutter/utils/extensions/enums/quote.dart';

import 'package:done_localizations/done_localizations.dart';
import 'package:done_models/done_models.dart';
import 'package:done_router/done_router.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';

class RawQuoteDetailView extends StatelessWidget {
  const RawQuoteDetailView({
    super.key,
    required this.rawQuote,
    this.quote,
    required this.job,
  });
  final RawQuote rawQuote;
  final Quote? quote;
  final Job job;
  @override
  Widget build(BuildContext context) {
    return StreamBuilder<DocumentSnapshot<Partnership>>(
      stream: rawQuote.partnerRef?.snapshots(),
      builder: (context, snapshot) {
        final partnership = snapshot.data?.data();
        return Scrollbar(
          child: ListView(
            padding: const EdgeInsets.fromLTRB(20, 16, 20, 0),
            primary: true,
            children:
                [
                  if (quote != null && quote!.amountAfterTaxDeductions > 0)
                    _buildStatus(context, quote!),
                  _buildCompanyCard(context, rawQuote, partnership),
                  _buildCustomerCard(context, rawQuote),
                  if (rawQuote.workDescription?.trim().isNotEmpty ?? false)
                    _buildWorkSpecifications(
                      context,
                      rawQuote.workDescription!,
                    ),
                  DoneDetailsSection(
                    title: S.of(context).priceSpecification,
                    titleStyle: Theme.of(context).textTheme.titleLarge!,
                    children:
                        rawQuote.lineItems
                            .map(
                              (e) => LineItemDetailsEntry(
                                item: e,
                                addVat: !rawQuote.pricesIncludeVat,
                                options: rawQuote.calculationOptions,
                              ),
                            )
                            .toList(),
                  ),
                  DetailsSummarySection(
                    amountIncludingVat: rawQuote.amountIncVat,
                    fullTaxDeduction: rawQuote.fullTaxDeduction,
                    totalNetAfterTaxDeductions:
                        rawQuote.totalNetAfterTaxDeductions,
                    centsRounding: rawQuote.centsRounding,
                  ),
                  const VerticalMargin.small(),
                  if (!rawQuote.otherInformation.isNullOrEmpty())
                    _buildOtherInformation(context, rawQuote.otherInformation!),
                  if (quote != null) ...[
                    _buildInformation(context, quote!, partnership),
                    _buildActionButtons(context, quote!),
                  ],
                  // This bottom margin is required since quote accept and decline buttons otherwise block the bottom widget,
                  const VerticalMargin(margin: 80),
                ].separatedBy(() => const VerticalMargin.xlarge()).toList(),
          ),
        );
      },
    );
  }

  Widget _buildStatus(BuildContext context, Quote quote) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        if (quote.status != QuoteStatus.pending) ...[
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: Margins.large,
              vertical: Margins.small,
            ),
            decoration: BoxDecoration(
              color: quote.bgColor(context),
              borderRadius: const BorderRadius.all(Radius.circular(8)),
            ),
            child: LabeledValue(
              label: S.of(context).totalGross,
              labelStyle: Theme.of(context).textTheme.bodyLarge,
              value: getPriceString(quote.amountAfterTaxDeductions),
              valueStyle: Theme.of(context).textTheme.titleLarge,
            ),
          ),
        ],
        if (quote.status == QuoteStatus.pending)
          HighlightedInfoView(
            text: Text(
              daysLeftSinceToday(quote.createTime!.toDate(), 13) > 0
                  ? S
                      .of(context)
                      .quotePageDayLeftWarning(
                        daysLeftSinceToday(
                          quote.createTime!.toDate(),
                          13,
                        ).toString(),
                      )
                  : S.of(context).quoteExpired,
              style: Theme.of(context).textTheme.bodyMedium!.apply(
                color: context.doneColors.uiOnPrimary,
              ),
            ),
            icon: Image.asset(
              ImageAssets.calendar,
              scale: 2,
              color: context.doneColors.uiOnPrimary,
            ),
          ),
        const VerticalMargin.small(),
        Text(
          S
              .of(context)
              .offeredAt(
                TimeFormatter(quote.createTime!.toDate(), context).toHuman(),
              ),
          textAlign: TextAlign.end,
        ),
      ],
    );
  }

  Widget _buildCompanyCard(
    BuildContext context,
    RawQuote rawQuote,
    Partnership? partnership,
  ) {
    return DoneDetailsSection(
      title: S.of(context).serviceProvider,
      titleStyle: Theme.of(context).textTheme.titleLarge!,
      dense: true,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            CompanyCard(
              companyRef: rawQuote.company,
              onTap: () {
                final params = QuoteCompanyProfileRouteParams(
                  companyId: rawQuote.company.id,
                  projectId: rawQuote.job.id,
                  quoteId: quote?.id ?? 'draft',
                );
                QuoteCompanyProfileRoute(params: params).navigate(context);
              },
            ),
            if (partnership != null)
              QuotePartnershipRow(partnership: partnership),
          ].separatedBy(() => const VerticalMargin.small()),
        ),
      ],
    );
  }

  Widget _buildCustomerCard(BuildContext context, RawQuote rawQuote) {
    return DoneDetailsSection(
      title: S.of(context).customer,
      titleStyle: Theme.of(context).textTheme.titleLarge!,
      dense: true,
      children: [
        CustomerCard(
          customerRef: rawQuote.customer,
          hasSubtitle: false,
          onTap: () {
            final params = QuoteCustomerProfileRouteParams(
              quoteId: quote?.id ?? 'draft',
              customerId: rawQuote.customer.id,
              projectId: rawQuote.job.id,
            );

            QuoteCustomerProfileRoute(params: params).navigate(context);
          },
        ),
      ],
    );
  }

  Widget _buildWorkSpecifications(BuildContext context, String workSpecs) {
    return DoneDetailsSection(
      title: S.of(context).workSpecification,
      titleStyle: Theme.of(context).textTheme.titleLarge!,
      children: [
        DoneExpandableText(workSpecs, maxLines: 10),
        const VerticalMargin.xlarge(),
      ],
    );
  }

  Widget _buildOtherInformation(BuildContext context, String otherInformation) {
    return DoneDetailsSection(
      title: S.of(context).otherInformation,
      titleStyle: Theme.of(context).textTheme.labelMedium!,
      children: [
        MultilineMarkdownBody(
          data: otherInformation,
          styleSheet: MarkdownStyleSheet(
            h2: Theme.of(context).textTheme.labelMedium,
          ),
        ),
      ],
    );
  }

  Widget _buildInformation(
    BuildContext context,
    Quote quote,
    Partnership? partnership,
  ) {
    return Column(
      children: <Widget>[
        HighlightedInfoView(
          text: Text(
            S
                .of(context)
                .quotePageBillingWarning(
                  partnership?.name ?? quote.companyName,
                ),
          ),
          icon: Text("📄", style: Theme.of(context).textTheme.bodyLarge),
        ),
        ..._buildTerms(context, partnership),
      ].separatedBy(() => const VerticalMargin.medium()),
    );
  }

  List<Widget> _buildTerms(BuildContext context, Partnership? partnership) {
    final terms = partnership?.terms;
    if (terms == null)
      return _buildDefaultTermsSections(context);
    else
      return [
        MultilineMarkdownBody(
          data: terms,
          styleSheet: MarkdownStyleSheet(
            h2: Theme.of(context).textTheme.bodyLarge,
          ),
          onTapLink:
              (_, url, __) =>
                  URLRouter.instance.handleUrl(url, context: context),
        ),
      ];
  }

  List<Widget> _buildDefaultTermsSections(BuildContext context) {
    final titleStyle = Theme.of(context).textTheme.titleLarge!;

    return <Widget>[
      DoneDetailsSection(
        title: S.of(context).billing,
        titleStyle: titleStyle,
        children: [
          Text(
            S.of(context).billingInfo,
            style: Theme.of(context).textTheme.bodyMedium!.apply(
              color: context.doneColors.uiOnPrimary,
            ),
          ),
        ],
      ),
      DoneDetailsSection(
        title: S.of(context).taxDeduction,
        titleStyle: titleStyle,
        children: [
          Text(
            S.of(context).quotePageTaxDeductionInfo,
            style: Theme.of(context).textTheme.bodyMedium!.apply(
              color: context.doneColors.uiOnPrimary,
            ),
          ),
        ],
      ),
      DoneDetailsSection(
        title: S.of(context).materialCost,
        titleStyle: titleStyle,
        children: [
          Text(
            S.of(context).quotePageMaterialCostInfo,
            style: Theme.of(context).textTheme.bodyMedium!.apply(
              color: context.doneColors.uiOnPrimary,
            ),
          ),
        ],
      ),
      DoneDetailsSection(
        title: S.of(context).hiddenFaults,
        titleStyle: titleStyle,
        children: [
          Text(
            S.of(context).quotePageHiddenFaults,
            style: Theme.of(context).textTheme.bodyMedium!.apply(
              color: context.doneColors.uiOnPrimary,
            ),
          ),
        ],
      ),
      DoneDetailsSection(
        title: S.of(context).cancellation,
        titleStyle: titleStyle,
        children: [
          Text(
            S.of(context).quotePageCancellationInfo,
            style: Theme.of(context).textTheme.bodyMedium!.apply(
              color: context.doneColors.uiOnPrimary,
            ),
          ),
        ],
      ),
    ];
  }

  Widget _buildActionButtons(BuildContext context, Quote quote) {
    return Column(
      children: quoteActions(context, quote, rawQuote, job)
          .map<Widget>(
            (action) => DoneButton(
              title: Text(action.title(context)),
              onPressed: () => action.onSelected(context, quote, rawQuote),
              style: action.buttonStyle,
            ),
          )
          .separatedBy(() => const VerticalMargin.medium()),
    );
  }
}

class QuotePartnershipRow extends StatelessWidget {
  const QuotePartnershipRow({required this.partnership, super.key});

  final Partnership partnership;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          S.of(context).onBehalfOf.toUpperCase(),
          style: Theme.of(context).textTheme.labelSmall!.copyWith(
            color: context.doneColors.typographyLowContrast,
            letterSpacing: 1,
          ),
        ),
        if (partnership.logo != null) ...[
          const HorizontalMargin.small(),
          ConstrainedBox(
            constraints: const BoxConstraints(maxWidth: 120, maxHeight: 25),
            child: CachedNetworkImage(imageUrl: partnership.logo!),
          ),
        ] else
          Text(
            ' ${partnership.name.toUpperCase()}',
            style: Theme.of(context).textTheme.labelSmall!.copyWith(
              color: context.doneColors.typographyLowContrast,
              letterSpacing: 1,
            ),
          ),
      ],
    );
  }
}
