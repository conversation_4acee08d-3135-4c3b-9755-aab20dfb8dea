import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:done_database/done_database.dart';

import 'package:done_flutter/ui/pages/quotes/raw_quote_detail_page.dart';
import 'package:done_flutter/ui/pages/view_pdf/pdf_router_page.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:done_localizations/done_localizations.dart';

/// Loads a Quote and shows either a QuoteDetailPage or a ViewPDFPage depending on if
/// the quote was created from a RawQuote or a file.
class QuoteDetailRouter extends StatelessWidget {
  const QuoteDetailRouter({super.key, required this.quoteReference});

  final DocumentReference<Quote> quoteReference;

  @override
  Widget build(BuildContext context) {
    final quotesRepo = GetIt.instance<QuotesRepository>();
    return StreamBuilder<Quote>(
      // TODO(@ayman): Fix the below query for job id, we need a better way.
      stream: quotesRepo.quote(
        jobId: quoteReference.parent.parent!.id,
        id: quoteReference.id,
      ),
      builder: (context, snapshot) {
        if (!snapshot.hasData) return const LoadingPage();
        final quote = snapshot.data!;

        if (quote.rawQuote != null) {
          final rawQuotesRepo = GetIt.instance<RawQuotesRepository>();

          return StreamBuilder<RawQuote>(
            stream: rawQuotesRepo.rawQuote(quote.rawQuote!.id),
            builder: (context, snapshot) {
              if (!snapshot.hasData) return const LoadingPage();
              return RawQuoteDetailPage(quote: quote, rawQuote: snapshot.data!);
            },
          );
        }

        // Load URL and show PDF page
        return PdfRouterPage(
          pdfFilePath: quote.fileRef!,
          title: Text(S.of(context).quote),
        );
      },
    );
  }
}
