import 'package:flutter/material.dart';

import 'package:done_localizations/done_localizations.dart';

class VersionNotSupportedPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[Text(S.of(context).needToUpdate)],
          ),
        ),
      ),
    );
  }
}
