import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:done_flutter/core/router/routes/project_routes.dart';
import 'package:done_flutter/ui/pages/job/widgets/craftsman_proceeds_view.dart';
import 'package:done_flutter/ui/pages/job/widgets/job_articles_section.dart';
import 'package:done_flutter/ui/pages/job/widgets/job_detail_page_app_bar.dart';
import 'package:done_flutter/ui/widgets/buttons/job_actions.dart';
import 'package:done_flutter/ui/widgets/cards/attachment_card.dart';
import 'package:done_flutter/ui/widgets/customer_have_no_app_badge.dart';
import 'package:done_flutter/ui/widgets/insured_by_row.dart';
import 'package:done_flutter/ui/widgets/job_notes_section.dart';
import 'package:done_flutter/ui/widgets/progress/progress_installation_report.dart';
import 'package:done_flutter/ui/widgets/progress/progress_customer_precheck.dart';
import 'package:done_flutter/ui/widgets/progress/progress_prerequisites.dart';
import 'package:done_flutter/ui/widgets/stream_page_builder.dart';
import 'package:done_flutter/utils/extensions/models/fixed_price_job_entries.dart';
import 'package:done_flutter/utils/mixins/dropzone_handler_mixin.dart';
import 'package:done_image/done_image.dart';
import 'package:done_maps/done_maps.dart';
import 'package:done_models/done_models.dart';
import 'package:done_auth/done_auth.dart';
import 'package:done_booking/done_booking.dart';
import 'package:done_database/done_database.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_flutter/ui/widgets/cards/company_card.dart';
import 'package:done_flutter/ui/widgets/cards/customer_card.dart';
import 'package:done_flutter/ui/widgets/progress/progress_invoice.dart';
import 'package:done_flutter/ui/widgets/progress/progress_project_start.dart';
import 'package:done_flutter/ui/widgets/expandable_text.dart';
import 'package:done_flutter/ui/widgets/jobs/job_status_string.dart';
import 'package:done_flutter/ui/widgets/conversation_preview.dart';
import 'package:done_flutter/ui/widgets/progress/progress_match.dart';
import 'package:done_flutter/ui/widgets/progress/progress_quotes.dart';
import 'package:done_flutter/ui/widgets/progress/progress_company_review.dart';
import 'package:done_flutter/ui/widgets/progress/progress_video_call.dart';

import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';

class JobDetailPage extends StatefulWidget {
  const JobDetailPage({super.key, required this.jobRef});

  final DocumentReference<Job> jobRef;

  @override
  _JobDetailPageState createState() => _JobDetailPageState();
}

class _JobDetailPageState extends State<JobDetailPage>
    with DropzoneHandlerMixin {
  /// This is needed for cases where there are multiple nested scrolling widgets,
  /// to sync the scroll bar with the main scroll view. Feels quite stupid.
  late final ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
  }

  @override
  Widget build(BuildContext context) {
    final userType = context.authState.getUserType();
    final jobsRepo = GetIt.instance<JobsRepository>();

    return StreamPageBuilder<Job>(
      stream: jobsRepo.job(widget.jobRef.id),
      builder: (context, job) {
        // When set with a new job stream, `jobSnapshot` still holds the old data
        // so, we should show loading if the data is old
        if (widget.jobRef.id != job.id) {
          return const LoadingPage();
        }

        return StreamPageBuilder<User>(
          stream: GetIt.instance<UserRepository>().user(job.customer.id),
          builder: (context, customer) {
            return Scaffold(
              appBar: JobDetailPageAppBar(job: job, customer: customer),
              body: GestureDetector(
                onTap: () => FocusScope.of(context).unfocus(),
                child: buildDropzone(
                  context: context,
                  child: Scrollbar(
                    controller: _scrollController,
                    child: SingleChildScrollView(
                      controller: _scrollController,
                      child: PageContent(
                        child: _buildList(context, job, userType, customer),
                      ),
                    ),
                  ),
                  job: job,
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildList(
    BuildContext context,
    Job job,
    UserType userType,
    User customer,
  ) {
    final isWideLayout =
        MediaQuery.of(context).size.width >= Layouts.wideLayout;
    final isCustomer = context.authState.isUserCustomer();
    final attachments = job.attachments?.where(
      (attachment) => attachment.title != null,
    );
    final shouldShowCard = job.company != null;
    final showProceeds =
        job.craftsmanProceeds != null &&
        job.network.id == donePartnerId &&
        context.authState.isCompanyAdmin;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: <Widget>[
        if (customer.lastLoggedIn == null) const CustomerHaveNoAppBanner(),
        const VerticalMargin.large(),
        _buildStatusSection(context, job),
        const VerticalMargin.small(),
        if (showProceeds)
          CraftsmanProceedsView(
            proceedsValue: (job.craftsmanProceeds!.value ?? 0) / 100,
            isEstimation: job.craftsmanProceeds?.isEstimation ?? true,
          ),
        _buildDetailSection(context, job),
        if ((job.tags?.contains(JobTag.express) ?? false))
          const AutoMargins(child: ExpressJobCard()),
        if (job.isFixedPriceJob) _buildFixedPriceSection(context, job),
        if (!isCustomer &&
            job.articles != null &&
            job.articles!.isNotEmpty) ...[
          JobArticlesSection(articles: job.articles!),
          const VerticalMargin.medium(),
        ],
        if (attachments != null && attachments.isNotEmpty)
          _buildAttachmentCards(context, attachments),
        const VerticalMargin.small(),
        if (job.cache?.customerLocation?.addressLine != null)
          _buildAddressSection(context, job),
        const VerticalMargin.small(),
        Padding(
          // Add extra padding when card is showing to avoid overlap
          padding:
              shouldShowCard
                  ? const EdgeInsets.only(bottom: 30)
                  : EdgeInsets.zero,
          child: Stack(
            clipBehavior: Clip.none,
            children: <Widget>[
              _buildMapSection(context, job.cache?.customerLocation),
              if (shouldShowCard)
                _buildProfileCardSection(context, job, userType),
            ],
          ),
        ),
        const VerticalMargin.small(),
        if (isCustomer && job.company != null)
          InsuredByRow(
            companyRef: job.company,
            title: Text(S.of(context).workInsuredBy.toUpperCase()),
          ),
        const VerticalMargin.small(),
        _buildLatestMessageSection(context, job),
        if (!context.authState.isUserCustomer()) ...[
          const VerticalMargin.small(),
          _buildNoteSection(context, job, minLines: 3),
        ],
        const VerticalMargin.large(),
        _buildProgressSection(context, job, userType),
        const VerticalMargin.large(),
        if (isCustomer)
          AutoMargins(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  S.of(context).details,
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const VerticalMargin.small(),
                ..._buildDetails(context, job),
                const VerticalMargin.xlarge(),
              ],
            ),
          ),
        if (!isWideLayout) ...[
          _buildActionsSection(context, job),
          const VerticalMargin.small(),
        ],
      ],
    );
  }

  Widget _buildAddressSection(BuildContext context, Job job) {
    return AutoMargins(
      child: LabeledValue(
        label: S.of(context).address,
        value: job.cache?.customerLocation?.addressLine,
        showCopyButton: true,
      ),
    );
  }

  Widget _buildFixedPriceSection(BuildContext context, Job job) {
    return Column(children: job.fixedPriceJobs!.widgets);
  }

  Widget _buildAttachmentCards(
    BuildContext context,
    Iterable<Attachment> attachments,
  ) {
    return AutoMargins(
      child: Column(
        children:
            attachments
                .map<Widget>((item) => AttachmentCard(attachment: item))
                .separatedBy(VerticalMargin.small)
                .toList(),
      ),
    );
  }

  Widget _buildStatusSection(BuildContext context, Job job) {
    return AutoMargins(
      child: HighlightedInfoView(
        text: Text(
          jobStatusString(job, context),
          style: Theme.of(context).textTheme.labelMedium,
          key: const Key("JobStatusView"),
        ),
        backgroundColor: jobStatusColor(context, job),
        padding: const EdgeInsets.symmetric(
          horizontal: Margins.medium,
          vertical: Margins.small,
        ),
      ),
    );
  }

  Widget _buildDetailSection(BuildContext context, Job job) {
    final isCustomer = context.authState.isUserCustomer();
    return AutoMargins(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          DoneExpandableText(job.description!, maxLines: 6),
          const VerticalMargin.medium(),
          if (job.images != null && job.images!.isNotEmpty) ...[
            const VerticalMargin.small(),
            ImageList<UrlsPhotoDataSource>.network(
              networkImages: job.images,
              onTap: (source) {
                final params = ProjectPhotoRouteParams(
                  projectId: job.id,
                  initialIndex: source.initialIndex,
                );
                ProjectPhotoRoute(
                  params: params,
                  extra: job.images,
                ).navigate(context);
              },
            ),
          ],
          if (!isCustomer) ...[
            const VerticalMargin.medium(),
            ..._buildDetails(context, job),
          ],
        ],
      ),
    );
  }

  List<Widget> _buildDetails(BuildContext context, Job job) {
    final isCustomer = context.authState.isUserCustomer();
    final isPlatformCompanyUser =
        context.authState.company != null &&
        context.authState.company!.isPlatformCompany;

    return [
      if (job.preferredCallTime != null)
        LabeledValue(
          label: S.of(context).chatTalkTime,
          value: job.preferredCallTime,
        ),
      if (job.preferredStartDate != null)
        LabeledValue(
          label: S.of(context).chatDesiredProjectStart,
          value: job.preferredStartDate,
        ),
      if (job.budget != null)
        LabeledValue(label: S.of(context).customerBudget, value: job.budget),
      if (job.externalReference == null &&
          (isCustomer || (!isCustomer && !isPlatformCompanyUser)))
        LabeledValue(
          label: S.of(context).jobsReferenceNumber,
          value: job.referenceNumber,
          showCopyButton: true,
        ),
      if (job.externalReference != null)
        LabeledValue(
          label:
              isPlatformCompanyUser
                  ? S.of(context).jobsReferenceNumber
                  : S.of(context).jobsExternalReference,
          value: job.externalReference,
          showCopyButton: true,
        ),
      if (isCustomer && job.discountCode != null)
        _JobDiscountCode(code: job.discountCode!),
    ].separatedBy(() => const Divider());
  }

  Widget _buildMapSection(BuildContext context, DoneLocation? location) {
    return location == null
        ? const SizedBox.shrink()
        : UserMap(location: location, height: 150);
  }

  Widget _buildProfileCardSection(
    BuildContext context,
    Job job,
    UserType userType,
  ) {
    return Positioned(
      bottom: job.cache?.customerLocation == null ? 0 : -30,
      right: LayoutMargins.of(context)?.margins.right ?? 0,
      left: LayoutMargins.of(context)?.margins.left ?? 0,
      child:
          (userType != UserType.craftsman)
              ? CompanyCard(
                companyRef: job.company,
                onTap: () {
                  final params = ProjectCompanyProfileRouteParams(
                    companyId: job.company!.id,
                    projectId: job.id,
                  );
                  ProjectCompanyProfileRoute(params: params).navigate(context);
                },
              )
              : CustomerCard(
                customerRef: job.customer,
                onTap: () {
                  final params = ProjectCustomerProfileRouteParams(
                    customerId: job.customer.id,
                    projectId: job.id,
                  );
                  ProjectCustomerProfileRoute(params: params).navigate(context);
                },
              ),
    );
  }

  Widget _buildLatestMessageSection(BuildContext context, Job job) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        AutoMargins(
          child: Text(
            S.of(context).chat,
            style: Theme.of(context).textTheme.titleLarge,
          ),
        ),
        const VerticalMargin.verySmall(),
        ConversationPreviewListItem(job: job),
      ],
    );
  }

  Widget _buildNoteSection(BuildContext context, Job job, {int? minLines}) {
    return JobNotesSection(
      job: job,
      companyId: context.authState.getUserCompany()!.id,
      minLines: minLines,
    );
  }

  Widget _buildProgressSection(
    BuildContext context,
    Job job,
    UserType userType,
  ) {
    final showProgressVideoCall =
        job.events.callScheduled != null ||
        (userType == UserType.craftsman && !job.isInvoicedThroughPartnership);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: <Widget>[
        AutoMargins(
          child: Text(
            S.of(context).status,
            style: Theme.of(context).textTheme.titleLarge,
          ),
        ),
        const VerticalMargin.small(),
        if (job.hasPrechecksForm) ProgressCustomerPrecheck(job: job),
        if (userType == UserType.customer) ProgressMatch(job: job),
        if (showProgressVideoCall) ProgressVideoCall(job: job),
        if (job.hasPrerequisites && userType == UserType.craftsman)
          ProgressPrerequisites(job: job),
        ProgressQuotes(job: job),
        if (!(userType == UserType.craftsman && job.isMarkedAsDone))
          ProgressProjectStart(job: job),
        if (job.hasReportForm)
          ProgressInstallationReport(job: job, didCreateReport: () {}),
        if (job.invoicedThroughPartnership != true) ProgressInvoice(job: job),
        ProgressCompanyReview(job: job),
        const VerticalMargin.large(),
      ],
    );
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }
}

Widget _buildActionsSection(BuildContext context, Job job) {
  final actions = jobActions(context: context, job: job);
  if (actions.isEmpty) return const SizedBox.shrink();
  return AutoMargins(
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: <Widget>[
        Text(
          S.of(context).projectActions,
          style: Theme.of(context).textTheme.titleLarge,
        ),
        const VerticalMargin.medium(),
        ...actions.separatedBy(() => const VerticalMargin.small()),
        const VerticalMargin.medium(),
      ],
    ),
  );
}

class _JobDiscountCode extends StatelessWidget {
  const _JobDiscountCode({required this.code});
  final String code;
  @override
  Widget build(BuildContext context) {
    return FutureBuilder<DiscountCode?>(
      future: GetIt.instance<BookingRepository>().fetchDiscountCode(code),
      builder: (context, snapshot) {
        if (!snapshot.hasData || snapshot.data == null) return const SizedBox();
        return DiscountCodeDetails(discountCode: snapshot.data!);
      },
    );
  }
}
