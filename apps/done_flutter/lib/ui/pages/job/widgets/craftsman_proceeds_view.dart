import 'package:done_localizations/done_localizations.dart';
import 'package:done_ui/done_ui.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';

class CraftsmanProceedsView extends StatelessWidget {
  const CraftsmanProceedsView({
    required this.proceedsValue,
    this.isEstimation = true,
  });
  final double proceedsValue;
  final bool isEstimation;

  @override
  Widget build(BuildContext context) {
    if (proceedsValue < 1) return const SizedBox.shrink();
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: LayoutMargins.of(context)?.margins.left ?? 0,
      ).copyWith(bottom: Margins.medium),
      child: DottedBorder(
        color: _borderColor(context),
        strokeWidth: 2,
        radius: const Radius.circular(Margins.small),
        borderType: BorderType.RRect,
        dashPattern: const [5, 5],
        child: DecoratedBox(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(Margins.small),
            color: _backgroundColor(context),
          ),
          child: Padding(
            padding: const EdgeInsets.all(Margins.medium),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  _title(context),
                  style: Theme.of(context).textTheme.labelMedium!.apply(
                    color: context.doneColors.typographyHightContrast,
                  ),
                ),
                RichText(
                  text: TextSpan(
                    children: [
                      if (isEstimation)
                        TextSpan(
                          text: '${S.of(context).estimatedProceedsValue} ',
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                      TextSpan(
                        text: _stringifiedValue,
                        style: Theme.of(context).textTheme.titleLarge!.copyWith(
                          color: context.doneColors.typographyHightContrast,
                        ),
                      ),
                    ],
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Color _borderColor(BuildContext context) =>
      isEstimation
          ? context.doneColors.uiPositive
          : context.doneColors.uiPrimary;

  Color _backgroundColor(BuildContext context) =>
      isEstimation
          ? context.doneColors.uiPrimary
          : context.doneColors.uiPositiveBg10;

  String _title(BuildContext context) =>
      isEstimation
          ? S.of(context).estimatedProceedsTitle
          : S.of(context).yourProceedsTitle;

  String get _stringifiedValue => getPriceString(proceedsValue);
}
