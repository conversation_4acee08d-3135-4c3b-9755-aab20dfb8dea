import 'package:done_localizations/done_localizations.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

class JobArticlesSection extends StatelessWidget {
  const JobArticlesSection({required this.articles, super.key});

  final List<JobArticle> articles;
  @override
  Widget build(BuildContext context) {
    if (articles.isEmpty) return const SizedBox.shrink();
    const divider = AutoMargins(child: Divider());
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        divider,
        Padding(
          padding: EdgeInsets.symmetric(
            horizontal: LayoutMargins.of(context)?.margins.left ?? 0,
            vertical: Margins.small,
          ).copyWith(top: 0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                S.of(context).order,
                style: Theme.of(context).textTheme.bodyMedium!.apply(
                  color: context.doneColors.typographyMediumContrast,
                ),
              ),
              const VerticalMargin.small(),
              ...articles
                  .map<Widget>((article) => _JobArticleTile(article: article))
                  .separatedBy(() => const VerticalMargin.verySmall()),
            ],
          ),
        ),
      ],
    );
  }
}

class _JobArticleTile extends StatelessWidget {
  const _JobArticleTile({required this.article});
  final JobArticle article;

  @override
  Widget build(BuildContext context) {
    final style = Theme.of(context).textTheme.labelMedium!.apply(
      color: context.doneColors.typographyHightContrast,
    );

    return Text('${article.quantity} x ${article.title}', style: style);
  }
}
