import 'package:done_booking/done_booking.dart';
import 'package:done_flutter/ui/pages/job/job_detail_page.dart';
import 'package:done_flutter/ui/widgets/buttons/job_actions.dart';
import 'package:done_flutter/ui/widgets/call/company_actions_app_bar.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

/// Used only in [JobDetailPage] to display the app bar.
class JobDetailPageAppBar extends StatelessWidget
    implements PreferredSizeWidget {
  const JobDetailPageAppBar({required this.job, required this.customer});

  final Job job;
  final User customer;

  @override
  Widget build(BuildContext context) {
    final isWideLayout =
        MediaQuery.of(context).size.width >= Layouts.wideLayout;

    final subtitleContents = <String>[
      if ((job.services?.isNotEmpty ?? false))
        serviceTypeFrom(job.services!.first)?.title(context) ??
            S.of(context).other,
      if (job.cache?.customerLocation?.shortLocation != null)
        job.cache!.customerLocation!.shortLocation!,
    ];

    return CompanyActionsAppBar(
      actions: (isWideLayout) ? jobActions(context: context, job: job) : null,
      context: context,
      job: job,
      customer: customer,
      title: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Text(
            S.of(context).chatProject,
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          Text(
            subtitleContents.join(', '),
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ],
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
