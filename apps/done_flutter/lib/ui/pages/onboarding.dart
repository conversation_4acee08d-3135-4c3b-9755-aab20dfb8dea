import 'dart:async';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:done_auth/done_auth.dart';
import 'package:done_router/done_router.dart';
import 'package:done_localizations/done_localizations.dart';

import 'package:done_ui/done_ui.dart';
import 'package:done_image/done_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:get_it/get_it.dart';
import 'package:logger/logger.dart';

class OnBoarding {
  OnBoarding({
    required this.title,
    required this.subtitle,
    required this.imagePath,
  });

  String title;
  String subtitle;
  String imagePath;
}

class OnBoardingScreen extends StatefulWidget {
  const OnBoardingScreen({super.key});

  @override
  _OnBoardingScreenState createState() => _OnBoardingScreenState();
}

class Indicator extends ChangeNotifier {
  Indicator() {
    currentPage = 0;
  }

  late int _currentPage;

  int get currentPage => _currentPage;

  set currentPage(int index) {
    _currentPage = index;
    notifyListeners();
  }
}

class _OnBoardingScreenState extends State<OnBoardingScreen> {
  List<OnBoarding> onBoardingPages(BuildContext context) {
    return [
      OnBoarding(
        title: S.of(context).onboardingPage1Title,
        subtitle: S.of(context).onboardingPage1Subtitle,
        imagePath: ImageAssets.onboardingPaint,
      ),
      OnBoarding(
        title: S.of(context).onboardingPage2Title,
        subtitle: S.of(context).onboardingPage2Subtitle,
        imagePath: ImageAssets.onboardingVideo,
      ),
      OnBoarding(
        title: S.of(context).onboardingPage3Title,
        subtitle: S.of(context).onboardingPage3Subtitle,
        imagePath: ImageAssets.onboardingCoin,
      ),
      OnBoarding(
        title: S.of(context).onboardingPage4Title,
        subtitle: S.of(context).onboardingPage4Subtitle,
        imagePath: ImageAssets.onboardingChat,
      ),
      OnBoarding(
        title: S.of(context).onboardingPage5Title,
        subtitle: S.of(context).onboardingPage5Subtitle,
        imagePath: ImageAssets.onboardingCertificate,
      ),
    ];
  }

  final int _countPages = 5;
  final _pageController = PageController();
  final _currentPageNotifier = ValueNotifier<int>(0);

  int _currentPage = 0;
  bool _isLoading = false;
  // To debounce signInAnonymously calls, otherwise multiple users can/will be created
  bool _isSignedIn = false;
  StreamSubscription<List<ConnectivityResult>>?
  _anonymousConnectivitySubscription;

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthCubit, AuthState>(
      listener: (context, state) {
        setState(() => _isLoading = !state.isUnauthenticated);
      },
      child: Scaffold(
        backgroundColor: context.doneColors.uiPrimary,
        key: const Key("onBoardingPage"),
        resizeToAvoidBottomInset: false,
        body:
            _isLoading
                ? const SafeArea(
                  child: SizedBox.expand(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(height: 8),
                        Center(child: CupertinoActivityIndicator()),
                      ],
                    ),
                  ),
                )
                : _buildMain(context),
      ),
    );
  }

  Widget _buildMain(BuildContext context) {
    return ColoredBox(
      color: context.doneColors.uiPrimary,
      child: Stack(
        children: <Widget>[
          PageView.builder(
            controller: _pageController,
            onPageChanged: (int index) {
              _currentPageNotifier.value = index;
              setState(() {
                _currentPage = index;
              });
            },
            physics: const AlwaysScrollableScrollPhysics(),
            itemCount: _countPages,
            itemBuilder: (BuildContext context, int index) {
              return OnBoardingPage(
                onBoarding: onBoardingPages(context)[index],
              );
            },
          ),
          SafeArea(
            child: Container(
              alignment: Alignment.bottomCenter,
              padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 32),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  ValueListenableBuilder<int>(
                    valueListenable: _currentPageNotifier,
                    builder:
                        (context, value, child) => PageViewDotIndicator(
                          count: _countPages,
                          currentItem: value,
                          unselectedColor: context.doneColors.uiBg2,
                          selectedColor: context.doneColors.uiBlack,
                          padding: EdgeInsets.zero,
                        ),
                  ),
                  if (_currentPage < _countPages - 1)
                    TextButton(
                      style: TextButton.styleFrom(
                        foregroundColor:
                            context.doneColors.typographyHightContrast,
                        textStyle: Theme.of(context).textTheme.titleLarge,
                        padding: const EdgeInsets.symmetric(
                          vertical: Margins.small,
                          horizontal: Margins.xxlarge,
                        ),
                      ),
                      onPressed: () {
                        if (_pageController.page == _countPages - 1) {
                        } else {
                          _pageController.animateToPage(
                            _pageController.page!.round() + 1,
                            duration: const Duration(milliseconds: 300),
                            curve: Curves.easeOut,
                          );
                        }
                      },
                      child: Text(S.of(context).next),
                    )
                  else
                    DoneButton(
                      style: DoneButtonStyle.secondary,
                      title: Text(S.of(context).onboardingExplore),
                      onPressed:
                          () => _confirmUserTermsAndSignInAnonymously(context),
                    ),
                ],
              ),
            ),
          ),
          Container(
            margin: const EdgeInsets.fromLTRB(32, 80, 0, 0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                const DoneLogo(),
                const VerticalMargin.medium(),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      S.of(context).onboardingSubtitle,
                      style: Theme.of(context).textTheme.bodyLarge,
                    ),
                    const HorizontalMargin.verySmall(),
                    GestureDetector(
                      key: const Key('OnboardingSignInButton'),
                      onTap: () {
                        final params = LoginRouteParams();
                        LoginRoute(params: params).navigate(context);
                      },
                      child: Text(
                        S.of(context).loginSignIn,
                        style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                          fontWeight: FontWeight.bold,
                          decoration: TextDecoration.underline,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _confirmUserTermsAndSignInAnonymously(BuildContext context) {
    showDialog<void>(
      context: context,
      builder:
          (BuildContext context) => DoneDialog(
            title: Text(S.of(context).tosTitle),
            content: [
              MultilineMarkdownBody(
                data: S.of(context).loginTermsMarkdown,
                onTapLink:
                    (_, url, __) =>
                        URLRouter.instance.handleUrl(url, context: context),
                styleSheet: MarkdownStyleSheet(
                  a: Theme.of(context).textTheme.labelMedium!.apply(
                    color: context.doneColors.purple,
                  ),
                  p: Theme.of(context).textTheme.labelMedium,
                ),
              ),
              const VerticalMargin.medium(),
              DoneButton(
                style: DoneButtonStyle.secondary,
                title: Text(S.of(context).tosAcceptTOS),
                onPressed: () async {
                  Navigator.of(context).pop();
                  await _signInAnonymously();
                },
              ),
              const VerticalMargin(margin: 6),
              DoneButton(
                style: DoneButtonStyle.neutral,
                title: Text(S.of(context).cancel),
                onPressed: () {
                  Navigator.pop(context);
                },
              ),
            ],
          ),
    );
  }

  Future<void> _signInAnonymously() async {
    setState(() async {
      _isLoading = true;

      await _anonymousConnectivitySubscription?.cancel();

      // In case the user has no internet connection, we need to listen for connectivity changes
      // to sign in the user when the connection is restored.
      // This is to avoid a deadlock situation where the user is trying to sign in
      // while the connection is restored.
      // It feels a bit bonkers though.
      _anonymousConnectivitySubscription = GetIt.instance<Connectivity>()
          .onConnectivityChanged
          .asBroadcastStream()
          .listen((results) async {
            final hasConnectivity = !results.contains(ConnectivityResult.none);
            if (!hasConnectivity) return;
            // User has connection now and was trying to login before connection returned
            if (!_isSignedIn && _isLoading) {
              GetIt.I<Logger>().d(
                'User has connection now and was trying to login before connection returned',
              );
              _isSignedIn =
                  await BlocProvider.of<AuthCubit>(context).signInAnonymously();

              // Avoid deadlock
              setState(() => _isLoading = false);
            }
          });

      _isSignedIn =
          await BlocProvider.of<AuthCubit>(context).signInAnonymously();
    });
  }

  @override
  void dispose() {
    _anonymousConnectivitySubscription?.cancel();
    super.dispose();
  }
}

class DoneLogo extends StatelessWidget {
  const DoneLogo({super.key});

  @override
  Widget build(BuildContext context) {
    return Text(
      "done.",
      style: TextStyle(
        color: context.doneColors.typographyHightContrast,
        fontFamily: 'Spectral',
        fontWeight: FontWeight.w800,
        letterSpacing: -2,
        fontSize: 48,
        height: 0.5,
      ),
    );
  }
}

class OnBoardingPage extends StatelessWidget {
  const OnBoardingPage({super.key, required this.onBoarding});

  final OnBoarding onBoarding;

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Container(
        margin: const EdgeInsets.only(bottom: 40, top: 90),
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.end,
          children: <Widget>[
            Flexible(
              child: Align(
                alignment: Alignment.bottomLeft,
                child: Image.asset(
                  onBoarding.imagePath,
                  alignment: Alignment.bottomLeft,
                ),
              ),
            ),
            const VerticalMargin(margin: 30),
            Text(
              onBoarding.title,
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const VerticalMargin.large(),
            Text(
              onBoarding.subtitle,
              style: Theme.of(context).textTheme.titleMedium!.copyWith(
                color: context.doneColors.typographyLowContrast,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
