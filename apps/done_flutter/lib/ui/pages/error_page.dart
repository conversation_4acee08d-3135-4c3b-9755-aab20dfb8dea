import 'package:done_database/done_database.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:done_ui/done_ui.dart';

class ErrorPage extends StatelessWidget {
  const ErrorPage({super.key, this.error});

  final Object? error;

  String _getErrorMessage(BuildContext context) {
    if (error is FirebaseException) {
      switch ((error! as FirebaseException).code) {
        case 'permission-denied':
        case 'not-found':
          return S.of(context).errorNotFoundPermissionDenied;
        case 'unavailable':
          return S.of(context).errorUnavailable;
        case 'failed-precondition':
          return S.of(context).errorFailedPrecondition;
        default:
          return S.of(context).errorUnexpected;
      }
    }

    if (error is FirestoreDocumentNotFoundException) {
      return S.of(context).errorNotFoundPermissionDenied;
    }

    // Default error message for non-FirebaseException errors
    return S.of(context).errorUnexpected;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(Margins.large),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: Theme.of(context).colorScheme.error,
              ),
              const VerticalMargin.large(),
              Text(
                _getErrorMessage(context),
                style: Theme.of(context).textTheme.titleLarge,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
