import 'package:done_flutter/ui/widgets/conversation_preview_item.dart';
import 'package:done_flutter/utils/company_tos_checker_util.dart';
import 'package:done_flutter/utils/version_check_util.dart';
import 'package:done_models/done_models.dart';
import 'package:flutter/material.dart';

class ConversationsListView extends StatelessWidget {
  const ConversationsListView({
    super.key,
    required this.messagesPreview,
    required this.onTap,
    required this.storageKey,
    required this.currentId,
  });

  final List<LatestMessagePreview> messagesPreview;
  final ValueChanged<String> onTap;

  /// The key for [PageStorageKey] of the list
  final String storageKey;
  final String? currentId;

  @override
  Widget build(BuildContext context) {
    return Scrollbar(
      child: ListView.builder(
        primary: true,
        key: PageStorageKey(storageKey),
        itemCount: messagesPreview.length + 1, //* +1 for the banners
        itemBuilder: (context, index) {
          if (index == 0) {
            return const Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (kShowVersionUtils) CollapsableSoftUpdateBanner(),
                NewTermsOfServiceWarningBanner(),
              ],
            );
          }
          final message = messagesPreview[index - 1];
          return ConversationPreviewItem(
            key: ValueKey('${message.jobId}-$index'),
            latestMessagePreview: message,
            onTap: onTap,
            selected: currentId == message.jobId,
          );
        },
      ),
    );
  }
}
