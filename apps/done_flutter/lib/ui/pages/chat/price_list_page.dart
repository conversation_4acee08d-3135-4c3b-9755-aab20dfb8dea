import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:done_database/done_database.dart';
import 'package:done_image/done_image.dart';

import 'package:done_localizations/done_localizations.dart';
import 'package:done_flutter/ui/pages/company/quote_generator/widgets/articles_list.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';

class PriceListPage extends StatelessWidget {
  const PriceListPage({required this.articleGroupRef, super.key});
  final DocumentReference<ArticleGroup> articleGroupRef;

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<DocumentSnapshot<ArticleGroup>>(
      stream: articleGroupRef.snapshots(),
      builder: (context, articleGroupSnapshot) {
        return StreamBuilder<List<Article>>(
          stream: GetIt.instance<FinancialGeneratorRepository>().articles(
            articleGroupRef.id,
          ),
          builder: (context, articlesSnapshot) {
            final articleGroup = articleGroupSnapshot.data?.data();
            final articles = articlesSnapshot.data;

            return PriceListModal(
              articleGroup: articleGroup,
              articles: articles,
            );
          },
        );
      },
    );
  }
}

class PriceListModal extends StatelessWidget {
  const PriceListModal({
    required this.articleGroup,
    required this.articles,
    super.key,
  });

  final ArticleGroup? articleGroup;
  final List<Article>? articles;

  @override
  Widget build(BuildContext context) {
    final isLoading = articleGroup == null || articles == null;
    final bottomPadding = MediaQuery.of(context).padding.bottom;
    return DoneModal(
      child: Scaffold(
        body: Column(
          children: [
            if (isLoading)
              Expanded(child: CenteredProgressIndicator())
            else
              PriceListInfoView(
                articleGroup: articleGroup!,
                articles: articles!,
              ),
            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: Margins.xlarge,
              ).copyWith(top: Margins.small, bottom: bottomPadding),
              child: DoneButton(
                title: Text(S.of(context).close),
                style: DoneButtonStyle.neutral,
                onPressed: () => Navigator.of(context).pop(),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class PriceListInfoView extends StatelessWidget {
  const PriceListInfoView({
    super.key,
    required this.articleGroup,
    required this.articles,
  });

  final ArticleGroup articleGroup;
  final List<Article> articles;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Column(
        children: [
          const VerticalMargin.medium(),
          if (articleGroup.logo != null) ...[
            ConstrainedBox(
              constraints: const BoxConstraints(maxHeight: 36, maxWidth: 140),
              child: CachedNetworkImage(imageUrl: articleGroup.logo!),
            ),
            const VerticalMargin.verySmall(),
          ],
          Text(
            articleGroup.title,
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const Divider(),
          Expanded(
            child: ArticlesList(articles: articles, articleGroup: articleGroup),
          ),
        ],
      ),
    );
  }
}
