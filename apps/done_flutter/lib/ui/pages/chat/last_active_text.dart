import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:done_database/done_database.dart';

import 'package:done_localizations/done_localizations.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';

class LastActiveText extends StatefulWidget {
  const LastActiveText({
    super.key,
    required this.isUserCustomer,
    required this.job,
  });

  final bool isUserCustomer;
  final Job job;

  @override
  State<LastActiveText> createState() => _LastActiveTextState();
}

class _LastActiveTextState extends State<LastActiveText> {
  Timestamp? closestLastActive;

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<Timestamp?>(
      stream: _lastActiveTimestamps(),
      builder: (context, snapshot) {
        if (snapshot.connectionState != ConnectionState.active)
          return const Text(' ');

        final lastActive = _getClosestLastActiveTimestamp(snapshot.data);

        return Text(
          lastActive == null
              ? S.of(context).notLoggedIn
              : '${S.of(context).active} ${TimeFormatter.getTimeAgo(lastActive.toDate(), context)}',
          style: Theme.of(
            context,
          ).textTheme.bodyMedium!.apply(color: context.doneColors.uiOnPrimary),
        );
      },
    );
  }

  /// The stream of [Timestamp]s of the customer or company's [User]s' last seen at
  Stream<Timestamp?> _lastActiveTimestamps() {
    if (!widget.isUserCustomer) {
      return GetIt.instance<UserRepository>()
          .user(widget.job.customer.id)
          .map<Timestamp?>((user) => user.lastLoggedIn);
    }

    return GetIt.instance<CompanyRepository>()
        .company(widget.job.company!.id)
        // Map company Stream into Stream of its users
        .map<List<CompanyUserEntry>>(
          (company) => company.usersV2.values.toList(),
        )
        // Get company's Users
        .map<List<Stream<User>>>(
          (userEntries) =>
              userEntries
                  .map(
                    (entry) => GetIt.instance<UserRepository>().user(
                      entry.reference.id,
                    ),
                  )
                  .toList(),
        )
        // Expand the List<Stream<DocumentSnapshot>> into a Stream<Stream<DocumentSnapshot>> that we can flatten
        .asyncExpand((streams) async* {
          for (final stream in streams) yield stream;
          // Get `lastLoggedIn` [Timestamp] value out of each user's snapshot
        })
        .flatten()
        .map((user) => user?.lastLoggedIn);
  }

  /// This function updates the [closestLastActive] variable to the Timestamp closer to the current time and returns it
  ///
  /// it should be called before showing the last active timestamp in the build method
  Timestamp? _getClosestLastActiveTimestamp(Timestamp? newest) {
    if (newest == null) return closestLastActive;

    closestLastActive ??= newest;

    if (newest.compareTo(closestLastActive!) > 0) {
      closestLastActive = newest;
    }
    return closestLastActive;
  }
}

/// An extension to flatten a Stream<Stream<T>> into Stream<T>
extension StreamsFlatten<T> on Stream<Stream<T>> {
  Stream<T?> flatten() async* {
    await for (final stream in this) yield* stream;
  }
}
