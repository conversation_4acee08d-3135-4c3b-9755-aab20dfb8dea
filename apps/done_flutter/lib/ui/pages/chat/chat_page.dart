import 'package:collection/collection.dart';
import 'package:done_analytics/done_analytics.dart';
import 'package:done_flutter/core/router/routes/project_routes.dart';
import 'package:done_flutter/ui/pages/chat/close_conversation_button.dart';
import 'package:done_flutter/ui/pages/chat/last_active_text.dart';
import 'package:done_flutter/ui/widgets/call/call_app_bar_button.dart';
import 'package:done_flutter/ui/widgets/chat/message_types/multi_images_message.dart';
import 'package:done_flutter/ui/widgets/customer_have_no_app_badge.dart';
import 'package:done_flutter/ui/widgets/firestore_ui_pagination.dart';
import 'package:done_flutter/ui/widgets/select_date_and_time.dart';
import 'package:done_flutter/utils/cached_widget.dart';
import 'package:done_flutter/utils/extensions/models/chat_messages.dart';
import 'package:done_flutter/utils/extensions/string_extensions.dart';
import 'package:done_flutter/utils/mixins/dropzone_handler_mixin.dart';

import 'package:done_models/done_models.dart';
import 'package:done_database/done_database.dart';

import 'package:done_flutter/ui/widgets/buttons/send_quote_button.dart';
import 'package:done_flutter/ui/widgets/call/company_actions_app_bar.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:get_it/get_it.dart';
import 'package:done_flutter/ui/widgets/chat/chat_message_widget.dart';
import 'package:done_flutter/ui/widgets/chat/text_composer.dart';
import 'package:done_auth/done_auth.dart';

class ChatPage extends StatefulWidget {
  const ChatPage({
    super.key,
    required this.jobRef,
    this.showProjectLink = true,
  });

  final DocumentReference<Job> jobRef;
  final bool showProjectLink;

  @override
  State<ChatPage> createState() => _ChatPageState();
}

class _ChatPageState extends State<ChatPage> with DropzoneHandlerMixin {
  late final ScrollController _scrollController;
  late DocumentReference user;
  late Job job;
  late User customer;

  String? lastSenderId;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    //* presumably the only way to access user reference inside dispose()
    //* is to instantiate it in didChangeDependencies()
    user = context.authState.user!.documentReference;
  }

  @override
  Widget build(BuildContext context) {
    _updateLastRead(user);
    final jobsRepo = GetIt.instance<JobsRepository>();
    final userRepo = GetIt.instance<UserRepository>();
    return StreamBuilder<Job>(
      stream: jobsRepo.job(widget.jobRef.id),
      builder: (context, jobSnapshot) {
        // When set with a new job stream, `jobSnapshot` still holds the old data
        // so, we should show loading if the data is old
        if (!jobSnapshot.hasData || widget.jobRef.id != jobSnapshot.data?.id) {
          // If craftsman is denied permission to read job, maybe match is still ongoing
          final shouldReferesh =
              jobSnapshot.hasError &&
              jobSnapshot.error is FirebaseException &&
              (jobSnapshot.error! as FirebaseException).code ==
                  'permission-denied';
          if (shouldReferesh) {
            Future.delayed(const Duration(seconds: 1), () {
              if (mounted) setState(() {});
            });
          }
          return const LoadingPage();
        }
        job = jobSnapshot.data!;
        final userType = context.authState.getUserType();
        final isCraftsman = context.authState.isUserCraftsman();
        return StreamBuilder<User>(
          stream: userRepo.user(job.customer.id),
          builder: (context, customerSnapshot) {
            if (!customerSnapshot.hasData) return const LoadingPage();
            customer = customerSnapshot.data!;
            final isWideLayout =
                MediaQuery.of(context).size.width >= Layouts.wideLayout;
            final isInteractive = job.isInteractive;
            return Scaffold(
              appBar: CompanyActionsAppBar(
                context: context,
                job: job,
                customer: customer,
                title: _ChatPageAppBarTitle(job: job),
                actions:
                    (isWideLayout && widget.showProjectLink)
                        ? [
                          DoneButton(
                            style: DoneButtonStyle.neutral,
                            title: Text(S.of(context).showProject),
                            onPressed: _showJobDetails,
                          ),
                          if (isCraftsman)
                            if (isInteractive) ...[
                              SendQuoteButton(job: job),
                              DoneButton(
                                image: const Icon(Icons.calendar_month),
                                style: DoneButtonStyle.secondary,
                                title: Text(_scheduleButtonLabel(context, job)),
                                onPressed: () async {
                                  await selectDateAndTime(
                                    context,
                                    job,
                                    ScheduleType.work,
                                  );
                                },
                              ),
                            ] else
                              CloseConversationButton(jobId: job.id),
                          CallAppBarButton(
                            context: context,
                            job: job,
                            isWideLayout: isWideLayout,
                          ),
                        ]
                        : null,
              ),
              floatingActionButtonLocation:
                  FloatingActionButtonLocation.centerTop,
              floatingActionButton:
                  !isWideLayout
                      ? _buildFloatingActions(
                        context,
                        job: job,
                        isInteractive: isInteractive,
                        isCraftsman: isCraftsman,
                      )
                      : null,
              body: buildDropzone(
                context: context,
                child: Column(
                  children: <Widget>[
                    Expanded(child: _buildBody(context)),
                    if (customer.lastLoggedIn == null)
                      const CustomerHaveNoAppBanner(),
                    if (job.isCommunicationAllowedFor(userType))
                      DecoratedBox(
                        decoration: BoxDecoration(
                          color: context.doneColors.uiPrimary,
                        ),
                        child: TextComposer(job: job),
                      ),
                  ],
                ),
                job: job,
              ),
            );
          },
        );
      },
    );
  }

  Widget? _buildFloatingActions(
    BuildContext context, {
    required Job job,
    bool isInteractive = false,
    bool isCraftsman = false,
  }) {
    return Padding(
      padding: const EdgeInsets.only(top: 64),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        spacing: Margins.medium,
        children: <Widget>[
          if (widget.showProjectLink)
            _FloatingActionButton(
              labelText: S.of(context).chatProject,
              icon: const Icon(Icons.info_outline),
              onPressed: _showJobDetails,
            ),
          if (isInteractive && isCraftsman)
            _FloatingActionButton(
              labelText: _scheduleButtonLabel(context, job),
              icon: const Icon(Icons.calendar_month),
              onPressed: () async {
                await selectDateAndTime(context, job, ScheduleType.work);
              },
            ),
        ],
      ),
    );
  }

  String _scheduleButtonLabel(BuildContext context, Job job) {
    final startDate = job.scheduledWorkStartTime;
    return startDate != null
        ? TimeFormatter(startDate.toDate(), context).toHuman().capitalize()
        : S.of(context).schedule;
  }

  Widget _buildBody(BuildContext context) {
    // FIXME: We build non paginated chat list on web as it has weird scroll behavior
    //  use paginated chat list when this is fixed https://github.com/firebase/flutterfire/issues/10110
    return kIsWeb
        ? _buildNonPaginatedChatList(context)
        : _buildPaginatedChatList(context);
  }

  Widget _buildNonPaginatedChatList(BuildContext context) {
    return StreamBuilder<QuerySnapshot<ChatMessage>>(
      stream: job.latestMessagesStream().snapshots(),
      builder: (context, snapshot) {
        if (!snapshot.hasData || snapshot.data!.size < 1)
          return const Center(child: DoneAdaptiveLoadingIndicator());
        final messages = snapshot.data!.docs;
        return Scrollbar(
          controller: _scrollController,
          child: ListView.builder(
            controller: _scrollController,
            itemCount: messages.grouped.length,
            itemBuilder:
                (context, index) =>
                    _buildChatItems(context, messages.grouped[index]),
            padding: const EdgeInsets.only(top: 20),
            reverse: true,
          ),
        );
      },
    );
  }

  Widget _buildPaginatedChatList(BuildContext context) {
    // FIXME: Using CahcedWidget here is a workaround as whenever we update lastRead on the job the
    //  list gets rebuilt - due to the StreamBuilder<Job> above - which causes flickering of the UI
    //  we need to use a bloc for this view or find a better way to handle it
    return CachedWidget(
      child: FirestoreQueryBuilder<ChatMessage>(
        query: job.latestMessagesStream(),
        builder: (context, snapshot, child) {
          return Scrollbar(
            controller: _scrollController,
            child: NotificationListener(
              /// Listen for scrolling notification, get the speed and direction of the scroll, close the keyboard (if it's open)
              /// if the scroll did a large enough jump compared to previous frame.
              onNotification: (notif) {
                if (notif is! ScrollUpdateNotification ||
                    notif.dragDetails == null) {
                  return true;
                }
                final dragDetails = notif.dragDetails!;
                if (dragDetails.delta.dy < 8.0) return true;
                final currentFocus = FocusScope.of(context);
                if (!currentFocus.hasPrimaryFocus) currentFocus.unfocus();

                return true;
              },
              child:
                  snapshot.isFetching
                      ? const DoneAdaptiveLoadingIndicator()
                      : ListView.builder(
                        padding: const EdgeInsets.only(top: 20),
                        reverse: true,
                        controller: _scrollController,
                        itemCount: snapshot.docs.grouped.length,
                        itemBuilder: (context, index) {
                          if (snapshot.hasMore &&
                              index + 1 == snapshot.docs.grouped.length) {
                            // Tell FirestoreQueryBuilder to try to obtain more items.
                            // It is safe to call this function from within the build method.
                            snapshot.fetchMore();
                          }
                          return _buildChatItems(
                            context,
                            snapshot.docs.grouped[index],
                          );
                        },
                      ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildChatItems(
    BuildContext context,
    GroupedChatMessage groupedMessage,
  ) {
    if (groupedMessage.messages.isEmpty) return const SizedBox.shrink();
    final currentSenderId = groupedMessage.sender.id;

    final isPreviousMsgCoalesced =
        lastSenderId != null && lastSenderId == currentSenderId;

    lastSenderId = currentSenderId;
    if (groupedMessage.messages.length == 1) {
      final message = groupedMessage.messages.first;
      return _buildListItem(
        context: context,
        message: message,
        job: job,
        isNextMsgCoalesced: isPreviousMsgCoalesced,
      );
    }

    if (groupedMessage.type == MessageType.image &&
        groupedMessage.messages.length > 3) {
      return PageContent(
        child: MultiImagesMessage(
          images:
              groupedMessage.messages.reversed
                  .cast<ImageChatMessage>()
                  .toList(),
          jobId: job.id,
          isPreviousMessageCoalesced: isPreviousMsgCoalesced,
        ),
      );
    } else {
      return Column(
        children:
            groupedMessage.messages.reversed
                .mapIndexed(
                  (index, message) => _buildListItem(
                    context: context,
                    message: message,
                    job: job,
                    isNextMsgCoalesced:
                        isPreviousMsgCoalesced &&
                        index + 1 != groupedMessage.messages.length,
                  ),
                )
                .toList(),
      );
    }
  }

  Widget _buildListItem({
    required BuildContext context,
    required ChatMessage message,
    required bool isNextMsgCoalesced,
    required Job job,
  }) {
    return PageContent(
      child: ChatMessageWidget(
        message: message,
        job: job,
        isPreviousMessageCoalesced: isNextMsgCoalesced,
      ),
    );
  }

  Future<void> _updateLastRead(DocumentReference user) async {
    // We are using `Timestamp.now()` here to avoid a flickering issue where lastRead is rendered `null` for a split second
    // Read more here: https://stackoverflow.com/questions/71724670/why-fieldvalue-servertimestamp-return-null-value-from-first-snapshot

    await widget.jobRef.update({"lastRead.${user.id}": Timestamp.now()});
    if (!mounted) return;
    final isCraftsman = context.authState.isUserCraftsman();
    if (isCraftsman) {
      //* update lastRead in conversations-per-company for craftsmen
      // FIXME: When company assignees land we should not set `conversations-per-company` as it will be deprecated
      // And only update conversationsPerUser for all parties
      final companyId = context.authState.getUserCompany()!.id;
      final conversationsObject = <String, dynamic>{};
      conversationsObject['conversations'] = {
        widget.jobRef.path: {
          'lastRead': {user.id: Timestamp.now()},
        },
      };
      final conversationsReference = FirebaseFirestore.instance
          .collection('conversations-per-company')
          .doc(companyId);
      await conversationsReference.set(
        conversationsObject,
        SetOptions(merge: true),
      );
    } else {
      final conversationLastReadKey =
          'conversations.${widget.jobRef.id}.lastRead.${user.id}';
      final conversationsReference = FirebaseFirestore.instance
          .collection('conversationsPerUser')
          .doc(user.id);
      await conversationsReference.update({
        conversationLastReadKey: Timestamp.now(),
      });
    }
  }

  void _showJobDetails() {
    EventLogger.instance.logEvent('tappedShowProjectFromChat');
    final params = ProjectDetailsRouteParams(projectId: widget.jobRef.id);
    ProjectDetailsRoute(params: params).navigate(context);
  }

  @override
  void deactivate() {
    _updateLastRead(user);
    super.deactivate();
  }
}

class _ChatPageAppBarTitle extends StatelessWidget {
  const _ChatPageAppBarTitle({required this.job});
  final Job job;
  @override
  Widget build(BuildContext context) {
    final isUserCustomer = context.authState.isUserCustomer();

    return DoneAppBarTitle(
      title: Text(
        isUserCustomer
            ? (job.companyName != null
                ? job.companyName!
                : S.of(context).jobsStatusWaiting)
            : job.customerName ?? S.of(context).anonymous,
      ),
      subtitle:
          job.company == null
              ? null
              : CachedWidget(
                child: LastActiveText(isUserCustomer: isUserCustomer, job: job),
              ),
    );
  }
}

class _FloatingActionButton extends StatelessWidget {
  const _FloatingActionButton({
    required this.labelText,
    required this.icon,
    required this.onPressed,
  });

  final VoidCallback onPressed;
  final String labelText;
  final Widget icon;

  ButtonStyle _style(BuildContext context) => ElevatedButton.styleFrom(
    elevation: 5,
    textStyle: Theme.of(context).textTheme.labelMedium,
    backgroundColor: context.doneColors.uiPrimary,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(Margins.large),
    ),
  );

  @override
  Widget build(BuildContext context) => ElevatedButton.icon(
    style: _style(context),
    onPressed: onPressed,
    label: Text(labelText),
    icon: icon,
  );
}
