import 'package:done_auth/done_auth.dart';
import 'package:done_database/done_database.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

class CloseConversationButton extends StatelessWidget {
  const CloseConversationButton({super.key, required this.jobId});
  final String jobId;

  @override
  Widget build(BuildContext context) {
    final companyId = context.authState.company?.id;
    if (companyId == null) return const SizedBox.shrink();
    return DoneButton(
      title: Text(S.of(context).closeConversationTitle),
      style: DoneButtonStyle.negative,
      onPressed:
          () => closeConversation(context, companyId: companyId, jobId: jobId),
    );
  }
}

Future<void> closeConversation(
  BuildContext context, {
  required String companyId,
  required String jobId,
}) async {
  final shouldDelete = await showConfirmationPopup(
    context: context,
    message: Text(S.of(context).closeConversationMessage),
    actionButtonStyle: DoneButtonStyle.negative,
    actionTitle: Text(S.of(context).closeConversationTitle),
    cancelTitle: Text(S.of(context).cancel),
  );
  if (!shouldDelete) return;

  await Mutations.instance
      .companyConversations(companyId)
      .removeConversation(jobId);
}
