import 'package:camera/camera.dart';
import 'package:done_analytics/done_analytics.dart';
import 'package:done_auth/done_auth.dart';
import 'package:done_database/done_database.dart';
import 'package:done_flutter/core/services/chat_service.dart';
import 'package:done_flutter/invoicing/widgets/pdf_invoice_modal.dart';
import 'package:done_flutter/ui/pages/quotes/pdf_quote_modal.dart';
import 'package:done_flutter/ui/widgets/jobs/job_close_actions.dart';

import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:path/path.dart' as p;

/// Shows a dialog to confirm sending a file through chat
Future<void> showSendFileConfirmationDialog({
  required BuildContext context,
  required XFile file,
  required Job job,
}) async {
  final fileExtension = p.extension(file.name);
  final fileType = getFileTypeFromExtension(fileExtension);
  if (fileType == null) return;
  return showDialog<void>(
    barrierDismissible: false,
    context: context,
    builder:
        (_) => _SendChatFileDialog(
          originalContext: context,
          file: file,
          job: job,
          fileType: fileType,
        ),
  );
}

class _SendChatFileDialog extends StatelessWidget {
  const _SendChatFileDialog({
    required this.file,
    required this.job,
    required this.fileType,
    required this.originalContext,
  });

  /// This is used to pass down the origin page context as this dialog will be popped on selection
  ///
  /// The original context is needed for showing further dialog down the flow
  final BuildContext originalContext;
  final XFile file;
  final Job job;
  final ChatItemType fileType;

  @override
  Widget build(BuildContext context) {
    final isUserCraftsman = context.authState.isUserCraftsman();
    final receiverName = isUserCraftsman ? job.customerName : job.companyName;
    return DoneDialog(
      title: Text(
        '${S.of(context).send} ${file.name} ${receiverName.isNullOrEmpty() ? '' : '${S.of(context).to} $receiverName'}?',
      ),
      content: <Widget>[
        DoneButton(
          title: Text(S.of(context).sendFile),
          style: DoneButtonStyle.secondary,
          onPressed: () async {
            await EventLogger.instance.logEvent('confirm_send_file');
            Navigator.of(context).pop();
            await _sendChatFileMessage(context, file, fileType, job);
          },
        ),
        if (isUserCraftsman && fileType == ChatItemType.pdf) ...[
          DoneButton(
            style: DoneButtonStyle.secondary,
            title: Text(S.of(context).uploadPDFQuote),
            onPressed: () async {
              Navigator.pop(context);
              final pdfQuoteModal = PdfQuoteModal(pdfFile: file, job: job);
              await showAdaptivePopup<void>(
                context: context,
                builder: (context) => pdfQuoteModal,
              );
            },
          ),
          _SendPdfInvoiceButton(
            file: file,
            job: job,
            originalContext: originalContext,
          ),
        ],
        DoneButton(
          title: Text(S.of(context).cancel),
          style: DoneButtonStyle.neutral,
          onPressed: () {
            EventLogger.instance.logEvent('cancel_send_file');
            Navigator.pop(context);
          },
        ),
      ].separatedBy(() => const VerticalMargin.small()),
    );
  }

  Future<void> _sendChatFileMessage(
    BuildContext context,
    XFile file,
    ChatItemType fileType,
    Job job,
  ) async {
    final fileExtension = p.extension(file.name);

    await EventLogger.instance.logEvent('sendFileInChat', {
      'type': fileExtension,
    });

    return Chat(job.documentReference).sendItem(
      ChatItem(file, fileType),
      currentUser: context.authState.user!.documentReference,
    );
  }
}

class _SendPdfInvoiceButton extends StatelessWidget {
  const _SendPdfInvoiceButton({
    required this.file,
    required this.job,
    required this.originalContext,
  });

  final XFile file;
  final Job job;
  final BuildContext originalContext;

  @override
  Widget build(BuildContext context) {
    return DoneButton(
      style: DoneButtonStyle.secondary,
      title: Text(S.of(context).uploadPDFInvoice),
      onPressed: () async {
        Navigator.pop(context);
        final pdfInvoiceModal = PdfInvoiceModal(pdfFile: file, job: job);
        if (job.isMarkedAsDone) {
          return showAdaptivePopup(
            context: context,
            builder: (context) => pdfInvoiceModal,
          );
        }

        final shouldMarkAsComplete = await showMarkAsCompletedDialog(
          context: context,
          job: job,
        );

        if (shouldMarkAsComplete) {
          await Mutations.instance.job(job.id).markAsDone();
          return showAdaptivePopup(
            context: originalContext,
            builder: (context) => pdfInvoiceModal,
          );
        }
      },
    );
  }
}
