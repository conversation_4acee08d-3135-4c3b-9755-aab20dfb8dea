import 'package:done_auth/done_auth.dart';
import 'package:done_flutter/core/services/chat_service.dart';
import 'package:done_flutter/ui/widgets/chat/chat_ui_mixin.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

class ChatCardMessage extends StatelessWidget with ChatUiMixin {
  const ChatCardMessage({
    super.key,
    required this.message,
    required this.isPreviousMessageCoalesced,
    required this.child,
  });

  final ChatMessage message;
  final bool isPreviousMessageCoalesced;
  final Widget child;

  @override
  Widget build(BuildContext context) {
    final senderRef = message.sender;
    final isOwnMessage = context.authState.isCurrentUser(senderRef);

    return Container(
      margin: buildMessageMargins(isOwnMessage, isPreviousMessageCoalesced),
      child: Row(
        children: <Widget>[
          if (shouldShowAvatar(
            isOwnMessage: isOwnMessage,
            isPreviousMessageCoalesced: isPreviousMessageCoalesced,
          ))
            buildMessageAvatarWithSpacing(user: senderRef),
          Expanded(
            child: Column(
              crossAxisAlignment:
                  isOwnMessage
                      ? CrossAxisAlignment.end
                      : CrossAxisAlignment.start,
              children: [
                const VerticalMargin(margin: 5),
                child,
                if (!isPreviousMessageCoalesced) ...[
                  Text(
                    getMessageTime(message.createTime, context),
                    textAlign: TextAlign.end,
                    style: TextStyle(
                      fontSize: 11,
                      color: context.doneColors.uiOnPrimary,
                    ),
                  ),
                  const VerticalMargin.small(),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }
}
