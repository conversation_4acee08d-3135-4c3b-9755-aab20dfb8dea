/// Flutter icons Star
/// Copyright (C) 2021 by original authors @ fluttericon.com, fontello.com
/// This font was generated by FlutterIcon.com, which is derived from Fontello.
///
/// To use this font, place it in your fonts/ directory and include the
/// following in your pubspec.yaml
///
/// flutter:
///   fonts:
///    - family:  Star
///      fonts:
///       - asset: fonts/Star.ttf
///
///
///
import 'package:flutter/widgets.dart';

class StarIcons {
  StarIcons._();

  static const _kFontFam = 'Star';

  static const IconData star = IconData(0xe800, fontFamily: _kFontFam);
  static const IconData starHalfFilled = IconData(
    0xe801,
    fontFamily: _kFontFam,
  );
}
