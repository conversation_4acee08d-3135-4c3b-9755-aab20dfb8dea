import 'dart:async';

import 'package:done_auth/done_auth.dart';
import 'package:done_flutter/core/models/navigation_bar.dart';
import 'package:done_database/done_database.dart';
import 'package:done_flutter/core/services/intercom_service.dart';

import 'package:done_flutter/ui/pages/company/offers/company_offers_view.dart';
import 'package:done_flutter/ui/widgets/streamed_count_badge.dart';
import 'package:done_flutter/utils/extensions/models/user_conversations.dart';
import 'package:done_router/done_router.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart' hide Badge;
import 'package:get_it/get_it.dart';

class HomeBottomNavigationBar extends StatelessWidget {
  const HomeBottomNavigationBar({super.key, required this.currentTab});
  final int currentTab;
  @override
  Widget build(BuildContext context) {
    return BottomNavigationBar(
      backgroundColor: context.doneColors.uiLightNeutral,
      elevation: 0,
      type: BottomNavigationBarType.fixed,
      currentIndex: currentTab,
      landscapeLayout: BottomNavigationBarLandscapeLayout.spread,
      selectedLabelStyle:
          NavigationBarData(context: context).selectedLabelStyle,
      unselectedLabelStyle:
          NavigationBarData(context: context).unselectedLabelStyle,
      unselectedItemColor:
          NavigationBarData(context: context).unselectedItemColor,
      selectedItemColor: NavigationBarData(context: context).selectedItemColor,
      onTap: (int index) => context.go(getNavBarItems(context)[index].location),
      items:
          getNavBarItems(context).map((item) {
            return BottomNavigationBarItem(
              icon: BottomNavBarIcon(
                imageAsset: item.iconAsset,
                pageNumber: item.pageNumber,
                currentPage: currentTab,
              ),
              activeIcon: BottomNavBarIcon(
                imageAsset: item.activeIconAsset,
                pageNumber: item.pageNumber,
                currentPage: currentTab,
              ),
              label: item.label,
              tooltip: '',
            );
          }).toList(),
    );
  }
}

class BottomNavBarIcon extends StatelessWidget {
  const BottomNavBarIcon({
    super.key,
    required this.currentPage,
    required this.pageNumber,
    required this.imageAsset,
  });

  final int currentPage;
  final int pageNumber;
  final String imageAsset;

  @override
  Widget build(BuildContext context) {
    final tab = getNavBarItems(context)[pageNumber];
    return Stack(
      key: Key("streamedBottomNav.${tab.key}"),
      children: <Widget>[
        Padding(
          padding: NavigationBarData(context: context).iconEdgeInsets,
          child: Image.asset(
            imageAsset,
            scale: 3.4,
            color:
                currentPage == pageNumber
                    ? context.doneColors.uiPurple
                    : context.doneColors.uiNeutral,
          ),
        ),
        Positioned(
          right: 4,
          child: StreamedCountBadge(
            stream: countStream(context, tab.pageNumber),
            tab: tab.pageNumber,
          ),
        ),
      ],
    );
  }
}

Stream<int>? countStream(BuildContext context, int tabIndex) {
  final user = context.authState.user;
  if (user == null) return null;
  final isCraftsman = user.company != null;
  final conversationsRepository = GetIt.instance<ConversationsRepository>();
  final jobOffersRepository = GetIt.instance<JobOffersRepository>();

  switch (tabIndex) {
    case 0:
      return isCraftsman
          ? conversationsRepository
              .companyMessages(user.company!.id)
              .map((messages) => messages.numberOfUnreadMessages(context))
          : null;
    case 1:
      return isCraftsman
          ? jobOffersRepository
              .companyPendingOffer(user.company!.id)
              .map(
                (offers) =>
                    CompanyOffersView.filterJobs(
                      context,
                      offers,
                      OfferPageType.pending,
                    ).length,
              )
          : null;
    case 2:
      return isCraftsman
          ? null
          : conversationsRepository
              .userConversations(user.documentReference.id)
              .map((messages) => messages.numberOfUnreadMessages(context));
    case 3:
      return GetIt.instance<IntercomHelper>().unreadCount;
    default:
      return null;
  }
}
