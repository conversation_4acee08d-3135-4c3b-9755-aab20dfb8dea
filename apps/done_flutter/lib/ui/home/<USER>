import 'package:done_auth/done_auth.dart';
import 'package:done_flutter/core/models/navigation_bar.dart';
import 'package:done_flutter/core/services/admin_interface_manager.dart';
import 'package:done_flutter/ui/admin/admin_controls_interface.dart';
import 'package:done_flutter/ui/home/<USER>';

import 'package:done_flutter/ui/pages/company/messages/company_active_messages_page.dart';
import 'package:done_flutter/ui/pages/company/offers/company_pending_offers_page.dart';
import 'package:done_flutter/ui/pages/company/profile/craftsman_profile_page.dart';
import 'package:done_flutter/ui/pages/company/projects/pages/projects_overview_page.dart';
import 'package:done_flutter/ui/pages/customer/customer_chat_page.dart';
import 'package:done_flutter/ui/pages/customer/customer_home_page.dart';
import 'package:done_flutter/ui/pages/customer/customer_profile_page.dart';
import 'package:done_flutter/ui/pages/customer/customer_projects_page.dart';
import 'package:done_router/done_router.dart';
import 'package:flutter/foundation.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:done_image/done_image.dart';

class HomePage extends StatelessWidget {
  const HomePage({super.key, required this.initialTab});

  final int initialTab;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isWideLayout = constraints.maxWidth > Layouts.wideLayout;
        final showMenuSideBar = isWideLayout;
        final isUltraWideLayout =
            constraints.maxWidth > Layouts.ultraWideLayout;
        final isSuperUser = context.authState.isSuperUser();
        final adminInterfaceManager = AdminInterfaceManager.instance;

        return FutureBuilder<bool>(
          future: adminInterfaceManager.isAdminInterfaceEnabled,
          builder: (context, snapshot) {
            final isAdminInterfaceEnabled = snapshot.data ?? false;
            final shouldShowAdminInterface =
                isSuperUser && isAdminInterfaceEnabled;
            return Scaffold(
              extendBodyBehindAppBar: true,
              appBar:
                  shouldShowAdminInterface && !isWideLayout
                      ? AppBar(
                        elevation: 0,
                        title: const AdminControlsInterface(),
                        automaticallyImplyLeading: false,
                      )
                      : null,
              extendBody: true,
              body:
                  showMenuSideBar
                      ? _buildBodyWithMenuSideBar(
                        context: context,
                        isUltraWideLayout: isUltraWideLayout,
                        shouldShowAdminInterface: shouldShowAdminInterface,
                      )
                      : _buildBody(context),
              bottomNavigationBar:
                  showMenuSideBar
                      ? null
                      : HomeBottomNavigationBar(currentTab: initialTab),
              floatingActionButtonLocation:
                  kIsWeb && showMenuSideBar
                      ? FloatingActionButtonLocation.startFloat
                      : null,
            );
          },
        );
      },
    );
  }

  Widget _buildBodyWithMenuSideBar({
    required BuildContext context,
    required bool isUltraWideLayout,
    required bool shouldShowAdminInterface,
  }) {
    const menuSideBarWidth = 105.0;
    final barColor =
        context.isDarkMode
            ? Color.lerp(context.doneColors.uiPrimary, Colors.black, 0.2)
            : context.doneColors.uiBg2.withValues(alpha: 0.5);
    return Row(
      children: [
        Container(
          width: menuSideBarWidth,
          color: barColor,
          child: Column(
            children: [
              const VerticalMargin.xxlarge(),
              _buildDoneLogo(),
              const VerticalMargin.xxlarge(),
              _buildMenuSideBar(context),
            ],
          ),
        ),
        SizedBox(
          width: MediaQuery.of(context).size.width - menuSideBarWidth,
          child: Scaffold(
            appBar:
                shouldShowAdminInterface
                    ? AppBar(
                      elevation: 0,
                      title: const AdminControlsInterface(),
                      automaticallyImplyLeading: false,
                    )
                    : null,
            body: PageStorage(
              bucket: PageStorageBucket(),
              child: _buildBody(
                context,
                useMasterDetailLayout: isUltraWideLayout,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDoneLogo() {
    return SizedBox(
      width: 56,
      child: ClipRRect(
        borderRadius: const BorderRadius.all(Radius.circular(8)),
        child: Image.asset(ImageAssets.doneLogo),
      ),
    );
  }

  DoneSideBar _buildMenuSideBar(BuildContext context) {
    return DoneSideBar(
      elevation: 0,
      type: DoneSideBarType.fixed,
      currentIndex: initialTab,
      backgroundColor:
          NavigationBarData(context: context).sideNavBarBackgroundColor,
      selectedLabelStyle:
          NavigationBarData(context: context).selectedLabelStyle,
      unselectedLabelStyle:
          NavigationBarData(context: context).unselectedLabelStyle,
      unselectedItemColor:
          NavigationBarData(context: context).unselectedItemColor,
      selectedItemColor: NavigationBarData(context: context).selectedItemColor,
      onTap: (int index) => context.go(getNavBarItems(context)[index].location),
      items:
          getNavBarItems(context)
              .map(
                (navItem) => _buildSideNavItem(
                  iconAsset: navItem.iconAsset,
                  pageNumber: navItem.pageNumber,
                  label: navItem.label,
                  currentPage: initialTab,
                  activeIconAsset: navItem.activeIconAsset,
                  key: navItem.key,
                ),
              )
              .toList(),
    );
  }

  Widget _buildBody(
    BuildContext context, {
    bool useMasterDetailLayout = false,
  }) {
    final authState = context.authState;
    final isCraftsman = authState.isUserCraftsman();
    switch (initialTab) {
      case 0:
        return isCraftsman
            ? const CompanyActiveMessagesPage()
            : CustomerHomePage();
      case 1:
        return isCraftsman
            ? const CompanyPendingOffersPage()
            : const CustomerProjectsPage();
      case 2:
        return isCraftsman
            ? const ProjectsOverviewPage(
              key: PageStorageKey(ProjectsOverviewPage.keyIdentifier),
            )
            : const CustomerChatPage(
              key: PageStorageKey(CustomerChatPage.keyIdentifier),
            );
      case 3:
        return isCraftsman
            ? const CrafstmanProfilePage()
            : const CustomerProfilePage();
      default:
        return const SizedBox();
    }
  }

  DoneSideBarItem _buildSideNavItem({
    required String iconAsset,
    required int pageNumber,
    required String label,
    required int currentPage,
    String? activeIconAsset,
    Key? key,
  }) {
    return DoneSideBarItem(
      icon: BottomNavBarIcon(
        imageAsset: iconAsset,
        pageNumber: pageNumber,
        currentPage: currentPage,
      ),
      activeIcon:
          activeIconAsset != null
              ? BottomNavBarIcon(
                imageAsset: activeIconAsset,
                pageNumber: pageNumber,
                currentPage: currentPage,
              )
              : null,
      label: label,
    );
  }
}
