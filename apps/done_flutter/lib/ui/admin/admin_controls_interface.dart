import 'package:done_auth/done_auth.dart';
import 'package:done_flutter/core/router/routes/routes_definitions.dart';
import 'package:done_flutter/ui/admin/admin_call_user_modal.dart';
import 'package:done_flutter/ui/admin/admin_view_as_modal.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AdminControlsInterface extends StatelessWidget {
  const AdminControlsInterface({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        DoneButton(
          style: DoneButtonStyle.neutral,
          verticalPadding: Margins.verySmall,
          title: const Text('Call user'),
          onPressed: () {
            showAdaptivePopup<void>(
              context: context,
              builder: (context) => const AdminCallUserModal(),
            );
          },
        ),
        DoneButton(
          style: DoneButtonStyle.neutral,
          verticalPadding: Margins.verySmall,
          title: const Text('View as'),
          onPressed: () {
            showAdaptivePopup<void>(
              context: context,
              builder: (context) => const AdminViewAsModal(),
            );
          },
        ),
        BlocBuilder<AuthCubit, AuthState>(
          builder: (context, state) {
            if (!state.isAuthenticated || (state.user?.company == null)) {
              return const SizedBox.shrink();
            }
            return DoneButton(
              style: DoneButtonStyle.negative,
              verticalPadding: Margins.verySmall,
              title: const Text('Clear'),
              onPressed: () async {
                await BlocProvider.of<AuthCubit>(context).clearCompany();
                const RootRoute().navigate(context);
              },
            );
          },
        ),
      ].separatedBy(() => const HorizontalMargin.small()),
    );
  }
}
