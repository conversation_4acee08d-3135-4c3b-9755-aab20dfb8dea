import 'package:done_flutter/core/services/admin_interface_manager.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';

class AdminInterfaceSwitch extends StatefulWidget {
  const AdminInterfaceSwitch({super.key});

  @override
  State<AdminInterfaceSwitch> createState() => _AdminInterfaceSwitchState();
}

class _AdminInterfaceSwitchState extends State<AdminInterfaceSwitch> {
  bool? _isEnabled;
  @override
  Widget build(BuildContext context) {
    final adminInterfaceManager = AdminInterfaceManager.instance;
    return FutureBuilder<bool>(
      future: adminInterfaceManager.isAdminInterfaceEnabled,
      builder: (context, snapshot) {
        if (!snapshot.hasData) return const SizedBox.shrink();
        final isInterfaceEnabled = snapshot.data!;
        SchedulerBinding.instance.addPostFrameCallback((_) {
          if (mounted) setState(() => _isEnabled = isInterfaceEnabled);
        });
        if (_isEnabled == null) return const SizedBox.shrink();
        final isEnabled = _isEnabled!;
        return Padding(
          padding: const EdgeInsets.only(bottom: Margins.medium),
          child: Row(
            children: [
              Text(
                'Enable admin interface',
                style: Theme.of(context).textTheme.labelMedium!.copyWith(
                  color: context.doneColors.typographyHightContrast,
                ),
              ),
              const Spacer(),
              Switch.adaptive(
                value: isEnabled,
                activeColor: Theme.of(context).colorScheme.primary,
                onChanged: (value) async {
                  final didSet = await adminInterfaceManager.setInterface(
                    enabled: value,
                  );
                  if (didSet && mounted) setState(() => _isEnabled = value);
                },
              ),
            ],
          ),
        );
      },
    );
  }
}
