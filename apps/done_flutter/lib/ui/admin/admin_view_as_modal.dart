import 'package:done_auth/done_auth.dart';
import 'package:done_flutter/core/router/routes/routes_definitions.dart';
import 'package:done_flutter/core/services/meili_search_service.dart';
import 'package:done_flutter/ui/pages/company/search/searchable_mixin.dart';
import 'package:done_flutter/ui/pages/company/search/widgets/done_search_bar.dart';
import 'package:done_flutter/ui/pages/company/search/widgets/search_results_view.dart';
import 'package:done_models/done_models.dart';
import 'package:done_router/done_router.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

class AdminViewAsModal extends StatefulWidget {
  const AdminViewAsModal({super.key});

  @override
  State<AdminViewAsModal> createState() => _AdminViewAsModalState();
}

class _AdminViewAsModalState extends State<AdminViewAsModal>
    with SearchableMixin {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('View as company')),
      body: Column(
        children: [
          const VerticalMargin.small(),
          DoneSearchBar(
            onFocus: () => setSearchingState(true),
            onCancel: () => setSearchingState(false),
            onQueryChanged: updateQuery,
            isSearchActivated: isSearching,
          ),
          Expanded(
            child:
                isSearching
                    ? ViewAsCompanySearchView(
                      query: query,
                      onTap: _onCompanyPicked,
                    )
                    : const Padding(
                      padding: EdgeInsets.only(top: Margins.large),
                      child: Text('Search for a company to view as'),
                    ),
          ),
        ],
      ),
    );
  }

  void _onCompanyPicked(CompanySearchEntry entry) {
    BlocProvider.of<AuthCubit>(
      context,
    ).updateWithCompany(company: entry.reference);
    context.pop();
    const RootRoute().navigate(context);
  }
}

class ViewAsCompanySearchView extends StatelessWidget {
  const ViewAsCompanySearchView({
    Key? key,
    required this.query,
    required this.onTap,
  }) : super(key: key);
  final String query;
  final ValueChanged<CompanySearchEntry> onTap;

  @override
  Widget build(BuildContext context) {
    return SearchResultsView<CompanySearchEntry>(
      isSearching: query.isNotEmpty,
      searchHintText: 'Search for a company by name',
      searchCallback: GetIt.instance<MeiliSearchService>().searchCompanies(
        query,
      ),
      resultsBuilder: (results) {
        // Catch only text based messages.
        final currentUserId = context.authState.user!.documentReference.id;
        final filteredResults = results.where(
          (userResult) => userResult.id != currentUserId,
        );

        return filteredResults
            .map<Widget>(
              (item) => Column(
                children: [
                  ListTile(
                    trailing: Icon(
                      Icons.remove_red_eye,
                      color: context.doneColors.uiOnPrimary,
                    ),
                    tileColor: context.doneColors.uiPrimary,
                    title: Text(
                      item.name,
                      style: Theme.of(context).textTheme.titleLarge!.apply(
                        color: context.doneColors.uiOnPrimary,
                      ),
                    ),
                    subtitle: Text(
                      item.orgNo,
                      style: Theme.of(context).textTheme.bodyLarge!.apply(
                        color: context.doneColors.uiOnPrimary,
                      ),
                    ),
                    onTap: () => onTap(item),
                  ),
                  if (item != filteredResults.last) const Divider(),
                ],
              ),
            )
            .toList();
      },
    );
  }
}
