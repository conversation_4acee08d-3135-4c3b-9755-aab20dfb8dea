import 'package:done_auth/done_auth.dart';
import 'package:done_calls/done_calls.dart';
import 'package:done_flutter/core/services/meili_search_service.dart';
import 'package:done_flutter/ui/pages/company/search/searchable_mixin.dart';
import 'package:done_flutter/ui/pages/company/search/widgets/done_search_bar.dart';
import 'package:done_flutter/ui/pages/company/search/widgets/search_results_view.dart';
import 'package:done_flutter/ui/widgets/call/initiate_call_bottom_sheet.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';

class AdminCallUserModal extends StatefulWidget {
  const AdminCallUserModal({super.key});

  @override
  State<AdminCallUserModal> createState() => _AdminCallUserModalState();
}

class _AdminCallUserModalState extends State<AdminCallUserModal>
    with SearchableMixin {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Call a user')),
      body: Column(
        children: [
          const VerticalMargin.small(),
          DoneSearchBar(
            onFocus: () => setSearchingState(true),
            onCancel: () => setSearchingState(false),
            onQueryChanged: updateQuery,
            isSearchActivated: isSearching,
          ),
          Expanded(
            child:
                isSearching
                    ? CallUserSearchView(
                      query: query,
                      onTap: _onUserSelectedForCall,
                    )
                    : const Padding(
                      padding: EdgeInsets.only(top: Margins.large),
                      child: Text('Search for a user to call'),
                    ),
          ),
        ],
      ),
    );
  }

  Future<void> _onUserSelectedForCall(UserSearchEntry user) async {
    final callee = Callee(user.reference, user.fullName);
    return showCupertinoModalPopup<void>(
      context: context,
      builder:
          (context) => InititateCallBottomSheet(
            callee: callee,
            source: 'Admin',
            job: null,
          ),
    );
  }
}

class CallUserSearchView extends StatelessWidget {
  const CallUserSearchView({Key? key, required this.query, required this.onTap})
    : super(key: key);
  final String query;
  final ValueChanged<UserSearchEntry> onTap;

  @override
  Widget build(BuildContext context) {
    return SearchResultsView<UserSearchEntry>(
      isSearching: query.isNotEmpty,
      searchHintText: 'Search for a company by name',
      searchCallback: GetIt.instance<MeiliSearchService>().searchUsers(query),
      resultsBuilder: (results) {
        // Catch only text based messages.
        final currentUserId = context.authState.user!.documentReference.id;
        final filteredResults = results.where(
          (userResult) => userResult.id != currentUserId,
        );

        return filteredResults
            .map<Widget>(
              (item) => Column(
                children: [
                  ListTile(
                    trailing: Icon(
                      Icons.call,
                      color: context.doneColors.uiOnPrimary,
                    ),
                    tileColor: context.doneColors.uiPrimary,
                    title: Text(
                      item.fullName,
                      style: Theme.of(context).textTheme.titleLarge!.apply(
                        color: context.doneColors.uiOnPrimary,
                      ),
                    ),
                    subtitle: Text(
                      item.phoneNumber,
                      style: Theme.of(context).textTheme.bodyLarge!.apply(
                        color: context.doneColors.uiOnPrimary,
                      ),
                    ),
                    onTap: () => onTap(item),
                  ),
                  if (item != filteredResults.last) const Divider(),
                ],
              ),
            )
            .toList();
      },
    );
  }
}
