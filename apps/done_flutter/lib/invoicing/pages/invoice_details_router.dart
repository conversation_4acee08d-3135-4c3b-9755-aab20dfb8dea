import 'package:cloud_firestore/cloud_firestore.dart';

import 'package:done_flutter/invoicing/pages/invoice_details_page.dart';
import 'package:done_flutter/ui/pages/error_page.dart';
import 'package:done_flutter/ui/pages/view_pdf/pdf_router_page.dart';
import 'package:done_flutter/utils/extensions/enums/invoice.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

/// Loads an invoice and shows either a InvoiceDetailsPage or a ViewPDFPage depending on if
/// the invoice was created in app or sent as file.
class InvoiceDetailsRouter extends StatelessWidget {
  const InvoiceDetailsRouter({
    super.key,
    required this.invoiceReference,
    required this.showGoToProjectAction,
    this.job,
  });

  final DocumentReference<MinimalInvoiceInfo> invoiceReference;
  final bool showGoToProjectAction;
  final Job? job;

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<DocumentSnapshot<MinimalInvoiceInfo>>(
      // We have to user `snapshots()` here and `DocumentSnapshot`
      // as we don't know if it will be a full invoice or just InvoiceCardInfo
      stream: invoiceReference.snapshots(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const LoadingPage();
        }

        if (snapshot.hasError) {
          return ErrorPage(error: snapshot.error);
        }

        final invoice = snapshot.data!.data()!;

        if (invoice.type == InvoiceType.pdf) {
          // Load URL and show PDF page
          return PdfRouterPage(
            pdfFilePath: invoice.pdfFilePath!,
            title: Text(invoice.title(context)),
          );
        }

        return InvoiceDetailsPage(
          invoice: invoice as Invoice,
          job: job,
          showGoToProjectAction: showGoToProjectAction,
        );
      },
    );
  }
}
