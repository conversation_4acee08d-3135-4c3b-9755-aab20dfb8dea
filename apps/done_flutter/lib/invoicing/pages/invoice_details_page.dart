import 'package:cloud_functions/cloud_functions.dart';
import 'package:done_auth/done_auth.dart';
import 'package:done_database/done_database.dart';
import 'package:done_deductions/done_deductions.dart';
import 'package:done_flutter/core/router/routes/project_routes.dart';
import 'package:done_flutter/invoicing/widgets/send_invoice_error_dialog.dart';
import 'package:done_flutter/invoicing/widgets/swish_payment_bottom_sheet.dart';
import 'package:done_flutter/ui/pages/company/shared_generator/widgets/send_to_customer_confirmation_popup.dart';
import 'package:done_flutter/ui/pages/company/shared_details/widgets/details_summary_section.dart';
import 'package:done_flutter/ui/pages/company/shared_details/widgets/line_item_detail_entry.dart';
import 'package:done_flutter/ui/widgets/cards/company_card.dart';
import 'package:done_flutter/utils/extensions/enums/admin_status.dart';
import 'package:done_flutter/utils/extensions/enums/invoice.dart';
import 'package:done_flutter/utils/extensions/string_extensions.dart';
import 'package:done_flutter/utils/pdf_util.dart';
import 'package:done_flutter/utils/first_build_callback.dart';
import 'package:done_models/done_models.dart';
import 'package:done_flutter/ui/widgets/cards/customer_card.dart';
import 'package:done_flutter/ui/widgets/expandable_text.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:get_it/get_it.dart';
import 'package:logger/logger.dart';

class InvoiceDetailsPage extends StatelessWidget {
  const InvoiceDetailsPage({
    super.key,
    required this.invoice,
    this.job,
    this.showGoToProjectAction = true,
  });

  final Invoice invoice;
  final Job? job;
  final bool showGoToProjectAction;

  @override
  Widget build(BuildContext context) {
    return FirstBuildCallback(
      onFirstBuild: () => _markInvoiceAsViewedByCustomer(context, invoice),
      child: Scaffold(
        appBar: AppBar(
          titleSpacing: -8,
          centerTitle: false,
          title: DoneAppBarTitle(
            title: Text(invoice.title(context)),
            subtitle:
                invoice.number != null
                    ? Text(
                      invoice.number!,
                      style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                        color: context.doneColors.typographyLowContrast,
                        fontWeight: FontWeight.bold,
                      ),
                    )
                    : null,
          ),
          actions: <Widget>[
            if (!invoice.isDraft)
              PopupMenuButton<_InvoiceDetailAction>(
                onSelected: (action) => action.onSelected(context, invoice),
                itemBuilder:
                    (BuildContext context) =>
                        _actions(context, invoice)
                            .map(
                              (action) => PopupMenuItem<_InvoiceDetailAction>(
                                value: action,
                                child: Text(action.title(context)),
                              ),
                            )
                            .toList(),
              ),
          ],
          elevation: 0,
        ),
        body: _buildList(context, invoice),
        bottomSheet: _buildBottomSheet(context, invoice),
      ),
    );
  }

  Widget? _buildBottomSheet(BuildContext context, Invoice invoice) {
    if (invoice.allowSending && (job?.isInteractive ?? true)) {
      return SendTotalBottomSheet(
        totalAmount: invoice.toPay,
        onSendPressed: () => _sendInvoice(context, invoice),
        showAsBottomSheet: true,
      );
    }

    if (context.authState.isUserCustomer() &&
        invoice.status == InvoiceStatus.sent) {
      return SwishPaymentBottomSheet(invoice: invoice);
    }
    return null;
  }

  Widget _buildList(BuildContext context, Invoice invoice) {
    final authState = context.authState;

    if (!authState.isAuthenticated) return CenteredProgressIndicator();
    final hasBottomSheet =
        (invoice.allowSending && (job?.isInteractive ?? true)) ||
        (context.authState.isUserCustomer() &&
            invoice.status == InvoiceStatus.sent);

    return SafeArea(
      child: Scrollbar(
        child: ListView(
          primary: true,
          padding: const EdgeInsets.symmetric(
            horizontal: Margins.xlarge,
            vertical: Margins.large,
          ),
          children:
              [
                if (authState.isUserCraftsman() &&
                    deductionBannerAvailableAdminStatuses.contains(
                      invoice.adminStatus,
                    ))
                  _buildInvoiceDeductedBanner(context, invoice),
                _buildStatus(context, invoice),
                if (!invoice.isDraft && invoice.paymentInstructions != null)
                  _PaymentInstructions(
                    invoice.paymentInstructions!,
                    invoice.toPay,
                  ),
                _buildCustomerCard(context, invoice),
                DoneDetailsSection(
                  title: S.of(context).serviceProvider,
                  titleStyle: Theme.of(context).textTheme.titleLarge!,
                  children: [
                    CompanyCard(
                      company: invoice.company,
                      onTap: () {
                        final params = InvoiceCompanyProfileRouteParams(
                          companyId: invoice.company.id,
                          invoiceId: invoice.id,
                          projectId: invoice.job.id,
                        );
                        InvoiceCompanyProfileRoute(
                          params: params,
                          extra: invoice.company,
                        ).navigate(context);
                      },
                    ),
                  ],
                ),
                _buildWorkSpecifications(context, invoice.workDescription!),
                if (!invoice.otherInformation.isNullOrEmpty())
                  _buildOtherInformation(context, invoice.otherInformation!),
                DoneDetailsSection(
                  title: S.of(context).priceSpecification,
                  titleStyle: Theme.of(context).textTheme.titleLarge!,
                  children:
                      invoice.lineItems
                          .map(
                            (e) => LineItemDetailsEntry(
                              item: e,
                              addVat: !invoice.pricesIncludeVat,
                              options: invoice.calculationOptions,
                            ),
                          )
                          .toList(),
                ),
                DetailsSummarySection(
                  amountIncludingVat: invoice.amountIncVat,
                  fullTaxDeduction: invoice.fullTaxDeduction,
                  totalNetAfterTaxDeductions: invoice.toPay,
                  centsRounding: invoice.centsRounding,
                ),
                if (!invoice.isDraft) ...[
                  _buildActionButtons(context, invoice),
                  _InvoiceActivitySection(invoice: invoice),
                ],
                if (hasBottomSheet)
                  const VerticalMargin(
                    margin: 100,
                  ), // Compensate for bottom sheet
              ].separatedBy(() => const VerticalMargin.xlarge()).toList(),
        ),
      ),
    );
  }

  Widget _buildStatus(BuildContext context, Invoice invoice) {
    return HighlightedInfoView(
      text: Text(
        invoice
            .detailedInformationText(
              context,
              invoice.isOverdue,
              timestamp: invoice.eventTimestampFromStatus(),
            )
            .capitalize(),
        style: Theme.of(
          context,
        ).textTheme.bodyMedium!.apply(color: context.doneColors.uiOnPrimary),
      ),
      icon: Text(
        invoice.statusIconEmoji(context, invoice.isOverdue),
        style: Theme.of(
          context,
        ).textTheme.bodyMedium!.apply(color: context.doneColors.uiOnPrimary),
      ),
      backgroundColor: invoice.detailedStatusBgColor(
        invoice.isOverdue,
        context,
      ),
    );
  }

  Widget _buildInvoiceDeductedBanner(BuildContext context, Invoice invoice) {
    return HighlightedInfoView(
      text: Text(invoice.adminStatus!.statusText(context)),
      backgroundColor: invoice.detailedStatusBgColor(
        invoice.isOverdue,
        context,
      ),
    );
  }

  Widget _buildCustomerCard(BuildContext context, Invoice invoice) {
    final customer = invoice.customer;
    final customerWithSensitiveData =
        customer.clone()..sensitiveCustomerData = invoice.customerSensitiveData;
    return DoneDetailsSection(
      title: S.of(context).customer,
      titleStyle: Theme.of(context).textTheme.titleLarge!,
      children: [
        CustomerCard(
          onTap: () {
            final params = InvoiceCustomerProfileRouteParams(
              customerId: invoice.customer.documentReference.id,
              invoiceId: invoice.id,
              projectId: invoice.job.id,
            );
            InvoiceCustomerProfileRoute(
              params: params,
              extra: invoice.customer,
            ).navigate(context);
          },
          customer: customerWithSensitiveData,
          hasSubtitle: false,
        ),
      ],
    );
  }

  Widget _buildWorkSpecifications(BuildContext context, String workSpecs) {
    return DoneDetailsSection(
      title: S.of(context).workSpecification,
      titleStyle: Theme.of(context).textTheme.titleLarge!,
      dense: true,
      children: [DoneExpandableText(workSpecs, maxLines: 10)],
    );
  }

  Widget _buildOtherInformation(BuildContext context, String otherInformation) {
    return DoneDetailsSection(
      title: S.of(context).otherInformation,
      titleStyle: Theme.of(context).textTheme.titleLarge!,
      dense: true,
      children: [
        MultilineMarkdownBody(
          data: otherInformation,
          styleSheet: MarkdownStyleSheet(
            h2: Theme.of(context).textTheme.labelMedium,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons(BuildContext context, Invoice invoice) {
    return Column(
      children: _actions(context, invoice)
          .map<Widget>(
            (action) => DoneButton(
              title: Text(action.title(context)),
              onPressed: () => action.onSelected(context, invoice),
              style: DoneButtonStyle.neutral,
            ),
          )
          .separatedBy(() => const VerticalMargin.medium()),
    );
  }

  List<_InvoiceDetailAction> _actions(
    BuildContext context,
    Invoice invoice,
  ) => [
    _InvoiceDetailAction(
      title: (context) => S.of(context).viewAsPdf,
      onSelected: _viewInvoiceAsPdf,
    ),
    if (showGoToProjectAction)
      _InvoiceDetailAction(
        title: (context) => S.of(context).showProject,
        onSelected: (context, invoice) {
          final params = ProjectDetailsRouteParams(projectId: invoice.job.id);
          ProjectDetailsRoute(params: params).navigate(context);
        },
      ),
  ];

  Future<void> _sendInvoice(BuildContext context, Invoice invoice) async {
    final shouldSend = await showSendToCustomerConfirmationPopup(
      context,
      '${invoice.sendInvoiceText(context)}?',
      invoice.customer.fullNameWithFallback(context),
      invoice.toPay,
    );

    if (shouldSend) {
      try {
        await FirebaseFunctions.instanceFor(
          region: 'europe-west1',
        ).httpsCallable('sendInvoice').call<void>({"invoiceId": invoice.id});
        Navigator.of(context).pop(true);
      } catch (e) {
        await showDialog<void>(
          context: context,
          builder:
              (context) => SendInvoiceErrorDialog(error: e, invoice: invoice),
        );
        GetIt.instance<Logger>().e("Error log", error: e);
      }
    }
  }

  Future<void> _viewInvoiceAsPdf(BuildContext context, Invoice invoice) {
    if (invoice.events?.viewedAsPDFByCustomer == null) {
      Mutations.instance
          .invoice(invoice.documentReference)
          .markAsViewedAsPdfByCustomer();
    }
    return viewAsPdf(
      context: context,
      storagePath: invoice.pdfFilePath!,
      pushRoute: (url) {
        final params = ViewInvoicePdfRouteParams(
          projectId: invoice.job.id,
          invoiceId: invoice.id,
          url: url,
        );
        ViewInvoicePdfRoute(params: params).navigate(context);
      },
    );
  }

  /// Updates the timestamp for ViewedByCustomer if it was never viewed
  Future<void> _markInvoiceAsViewedByCustomer(
    BuildContext context,
    Invoice invoice,
  ) async {
    if (context.authState.isUserCustomer() &&
        invoice.events?.viewedByCustomer == null) {
      return Mutations.instance
          .invoice(invoice.documentReference)
          .markAsViewedByCustomer();
    }
  }
}

/// Displays payment instructions
class _PaymentInstructions extends StatelessWidget {
  const _PaymentInstructions(this.paymentInstructions, this.amountToPay);

  final InvoicePaymentInstructions paymentInstructions;
  final double amountToPay;

  @override
  Widget build(BuildContext context) {
    final instructions = {
      S.of(context).amountToPay: getPriceString(amountToPay),
      'Bankgiro': paymentInstructions.bankgiroNumber,
      'OCR': paymentInstructions.ocrNumber,
      'Swish number': paymentInstructions.swishNumber,
    }..removeNullValues();

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        border: Border.all(color: context.doneColors.uiBg1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: instructions.entries
            .map<Widget>(
              (entry) => Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: LabeledValue(
                  label: entry.key,
                  value: entry.value,
                  showCopyButton: true,
                ),
              ),
            )
            .separatedBy(() => const Divider()),
      ),
    );
  }
}

/// Shows activity section for the invoice throughout its journey, from creation to payment or refuted.
class _InvoiceActivitySection extends StatelessWidget {
  const _InvoiceActivitySection({required this.invoice});

  final Invoice invoice;

  @override
  Widget build(BuildContext context) {
    // TODO - invoicing: improve some translations?
    // TODO - payment: add payment tracking events (they will be based on payments property of invoice)
    final invoiceEvents = {
      S.of(context).created: invoice.events?.sent,
      S.of(context).opened: invoice.events?.viewedByCustomer,
      S.of(context).viewedAsPDF: invoice.events?.viewedAsPDFByCustomer,
      '${S.of(context).reminder} 1': invoice.events?.reminder1,
      '${S.of(context).reminder} 2': invoice.events?.reminder2,
      S.of(context).finalReminder: invoice.events?.finalReminder,
      S.of(context).refuted: invoice.events?.refuted,
      S.of(context).creditLoss: invoice.events?.markedAsCreditLoss,
      S.of(context).credited: invoice.events?.credited,
      S.of(context).paidInPart: invoice.events?.paidInPart,
      S.of(context).paid: invoice.events?.fullyPaid,
    }..removeNullValues();

    return DoneDetailsSection(
      title: S.of(context).activity,
      titleStyle: Theme.of(context).textTheme.titleLarge!,
      children: invoiceEvents.entries
          .map<Widget>(
            (e) => LabeledValue(
              label: e.key,
              value: TimeFormatter.getHumanReadableDate(
                e.value?.toDate(),
                context,
              ),
              direction: LabeledValueAxis.horizontal,
              valueStyle: Theme.of(context).textTheme.bodyLarge!.apply(
                color: context.doneColors.typographyMediumContrast,
              ),
            ),
          )
          .separatedBy(() => const VerticalMargin.medium()),
    );
  }
}

class _InvoiceDetailAction {
  const _InvoiceDetailAction({required this.title, required this.onSelected});

  final String Function(BuildContext) title;
  final void Function(BuildContext, Invoice) onSelected;
}
