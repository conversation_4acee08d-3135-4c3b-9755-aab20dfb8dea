import 'package:done_deductions/done_deductions.dart';
import 'package:done_flutter/utils/extensions/enums/company_invoice.dart';
import 'package:done_models/done_models.dart';
import 'package:done_router/done_router.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:done_localizations/done_localizations.dart';

class CompanyInvoicesOverviewPage extends StatelessWidget {
  const CompanyInvoicesOverviewPage({super.key, required this.invoices});

  final List<CompanyInvoice> invoices;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        titleSpacing: -8,
        title: Text(S.of(context).invoices),
        centerTitle: false,
        elevation: 0,
      ),
      body:
          invoices.isEmpty
              ? const _InvoicesEmptyState()
              : Scrollbar(
                child: ListView.separated(
                  primary: true,
                  padding: const EdgeInsets.only(bottom: 32, top: 16),
                  separatorBuilder:
                      (_, __) => const Padding(
                        padding: EdgeInsets.only(left: 12),
                        child: Divider(),
                      ),
                  itemBuilder:
                      (context, index) =>
                          _CompanyInvoiceOverviewTile(invoice: invoices[index]),
                  itemCount: invoices.length,
                ),
              ),
    );
  }
}

class _CompanyInvoiceOverviewTile extends StatelessWidget {
  const _CompanyInvoiceOverviewTile({required this.invoice});
  final CompanyInvoice invoice;
  @override
  Widget build(BuildContext context) {
    return ListTile(
      onTap: () {
        GoRouter.of(context).go('./pdf?path=${invoice.fileUrl}');
      },
      dense: true,
      title: Padding(
        padding: const EdgeInsets.only(bottom: Margins.verySmall),
        child: InvoiceHeaderInfo(title: Text(invoice.invoiceNumber)),
      ),
      horizontalTitleGap: 0,
      subtitle: Text(
        TimeFormatter.getShortHumanReadableDate(
          invoice.invoiceTime.toDate(),
          context,
        ),
        style: Theme.of(context).textTheme.bodyMedium!.copyWith(
          color: context.doneColors.typographyMediumContrast,
        ),
      ),
      trailing: _InvoiceDateAndTotalInfo(invoice: invoice),
      contentPadding: const EdgeInsets.symmetric(horizontal: 14),
    );
  }
}

/// Shows the invoice status chips info which shows current status
class _InvoiceStatusChip extends StatelessWidget {
  const _InvoiceStatusChip({required this.invoice});
  final CompanyInvoice invoice;
  @override
  Widget build(BuildContext context) {
    return Wrap(
      spacing: 4,
      children: [
        DonePlainBadge(
          padding: 6,
          color: invoice.chipColor(context),
          label: Text(
            invoice.informationText(context),
            style: invoice.textStyle(context),
          ),
        ),
      ],
    );
  }
}

/// Shows the due date of the invoice and total amount of invoice with tax info chip
class _InvoiceDateAndTotalInfo extends StatelessWidget {
  const _InvoiceDateAndTotalInfo({required this.invoice});
  final CompanyInvoice invoice;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Text(
          getPriceString(invoice.amount / 100),
          style: Theme.of(context).textTheme.titleLarge,
        ),
        const VerticalMargin.verySmall(),
        _InvoiceStatusChip(invoice: invoice),
      ],
    );
  }
}

class _InvoicesEmptyState extends StatelessWidget {
  const _InvoicesEmptyState();
  @override
  Widget build(BuildContext context) {
    return EmptyState(
      description: Text(S.of(context).activeInvoicesEmptyState),
    );
  }
}
