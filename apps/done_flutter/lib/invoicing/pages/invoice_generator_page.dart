import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:done_auth/done_auth.dart';
import 'package:done_booking/done_booking.dart';

import 'package:done_database/done_database.dart';
import 'package:done_flutter/core/router/routes/project_routes.dart';

import 'package:done_flutter/invoicing/pages/invoice_details_router.dart';
import 'package:done_flutter/invoicing/widgets/invoice_basis_sent_dialog.dart';
import 'package:done_flutter/ui/pages/company/shared_generator/widgets/costs_bottom_sheet.dart';
import 'package:done_flutter/ui/pages/company/shared_generator/widgets/costs_bottom_sheet_header.dart';
import 'package:done_flutter/ui/pages/company/shared_generator/widgets/delete_draft_dialog.dart';
import 'package:done_flutter/ui/pages/company/shared_generator/widgets/generator_app_bar.dart';
import 'package:done_flutter/ui/pages/company/shared_generator/widgets/generator_multiline_field.dart';
import 'package:done_flutter/ui/pages/company/shared_generator/widgets/generator_vat_info.dart';
import 'package:done_flutter/ui/pages/company/shared_generator/widgets/line_items_section.dart';
import 'package:done_flutter/ui/widgets/cards/customer_card.dart';
import 'package:done_flutter/ui/widgets/jobs/job_close_actions.dart';
import 'package:done_flutter/utils/extensions/enums/invoice.dart';
import 'package:done_flutter/utils/first_build_callback.dart';
import 'package:done_flutter/utils/text_field_utilities.dart';

import 'package:logger/logger.dart';
import 'package:get_it/get_it.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_models/done_models.dart';
import 'package:expandable_bottom_sheet/expandable_bottom_sheet.dart';

/// Shows invoice generator screen for invoice generation before sending it to customer
class InvoiceGeneratorPage extends StatefulWidget {
  const InvoiceGeneratorPage({
    super.key,
    required this.job,
    required this.invoice,
    required this.quote,
    this.articleGroup,
  });

  final Job job;
  final MinimalInvoiceInfo? invoice;
  final Quote? quote;
  final JobArticleGroup? articleGroup;

  @override
  _InvoiceGeneratorPageState createState() => _InvoiceGeneratorPageState();
}

class _InvoiceGeneratorPageState extends State<InvoiceGeneratorPage> {
  Invoice? _invoice;

  Future<void> _initializeInvoice(DefaultQuoteSettings settings) async {
    if (widget.invoice == null) {
      return _createNewInvoice(settings);
    }

    final invoice = await GetIt.instance<InvoicesRepository>().fetchInvoice(
      widget.invoice!.id,
    );
    setState(() {
      _invoice = invoice;
    });
  }

  /// creates a new raw quote on the backend and passes this data onward to the quote generator form
  Future<void> _createNewInvoice(DefaultQuoteSettings settings) async {
    final contact = context.authState.getUserName(
      context,
      anonymousText: S.of(context).anonymous,
    );
    final newInvoice = await GetIt.instance<InvoicesRepository>()
        .createNewDraftInvoice(
          job: widget.job,
          contact: contact,
          settings: settings,
          quote: widget.quote,
          articleGroup: widget.articleGroup,
        );

    setState(() {
      _invoice = newInvoice;
    });
  }

  @override
  Widget build(BuildContext context) {
    final bookingFlowConfigStream =
        GetIt.instance<BookingRepository>().bookingFlowConfig();
    return StreamBuilder<DefaultQuoteSettings>(
      stream: GetIt.instance<FinancialGeneratorRepository>()
          .generatorQuoteSettings(widget.job, bookingFlowConfigStream),
      builder: (
        BuildContext context,
        AsyncSnapshot<DefaultQuoteSettings> snapshot,
      ) {
        if (!snapshot.hasData) return CenteredProgressIndicator();

        if (_invoice == null) {
          return FirstBuildCallback(
            onFirstBuild: () => _initializeInvoice(snapshot.data!),
            child: CenteredProgressIndicator(),
          );
        }

        return _InvoiceGeneratorForm(
          invoice: _invoice!,
          job: widget.job,
          articleGroup: widget.articleGroup,
          defaultQuoteSettings: snapshot.data!,
          isNewlyCreatedDraft: widget.invoice == null,
        );
      },
    );
  }
}

class _InvoiceGeneratorForm extends StatefulWidget {
  const _InvoiceGeneratorForm({
    required this.invoice,
    required this.isNewlyCreatedDraft,
    required this.defaultQuoteSettings,
    required this.job,
    required this.articleGroup,
  });

  final Invoice invoice;
  final bool isNewlyCreatedDraft;
  final DefaultQuoteSettings defaultQuoteSettings;
  final Job job;
  final JobArticleGroup? articleGroup;

  @override
  __InvoiceGeneratorFormState createState() => __InvoiceGeneratorFormState();
}

class __InvoiceGeneratorFormState extends State<_InvoiceGeneratorForm> {
  late Invoice _invoice;
  bool _hasChanges = false;
  ScrollController scrollController = ScrollController();
  late GlobalKey<FormState> _invoiceFormKey;
  late GlobalKey<ExpandableBottomSheetState> _bottomSheetKey;
  late GlobalKey<AnimatedListState> lineItemsListKey;
  late TextEditingController _dateController;

  @override
  void initState() {
    super.initState();
    _invoice = widget.invoice;
    scrollController = ScrollController();
    _invoiceFormKey = GlobalKey<FormState>();
    _bottomSheetKey = GlobalKey<ExpandableBottomSheetState>();
    lineItemsListKey = GlobalKey<AnimatedListState>();
    _dateController = txtCtrlFromValue(
      TimeFormatter.toYearMonthNumberDayFormat(
        _invoice.dueDate?.toDate() ??
            DateTime.now().add(const Duration(days: defaultInvoiceDueDateDays)),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      // FIXME: Rewrite to use PopScope instead
      // ignore: deprecated_member_use
      child: WillPopScope(
        onWillPop: () async => onCancel(context),
        child: Scaffold(
          backgroundColor: context.doneColors.uiPrimary,
          appBar: GeneratorAppBar(
            backgroundColor: context.doneColors.purple,
            onCancel: () async {
              final shouldNavigateBack = await onCancel(context);
              if (shouldNavigateBack) Navigator.of(context).pop();
            },
            title: _invoice.createInvoiceText(context),
          ),
          resizeToAvoidBottomInset: false,
          body: LayoutBuilder(
            builder: (context, constraints) {
              return Align(
                child:
                    (constraints.maxWidth > Layouts.ultraWideLayout)
                        ? _buildBodyForUltraWideLayout()
                        : _buildBodyForNormalLayout(),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildBodyForNormalLayout() {
    return ColoredBox(
      color: context.doneColors.uiPrimary,
      child: ExpandableBottomSheet(
        key: _bottomSheetKey,
        background: _buildForm(),
        persistentHeader: CostsBottomSheetHeader(
          onContinuePressed: _saveChanges,
          continueEnabled: _invoice.isValid,
          amountToBePaid: _invoice.toPay,
          onTap: () {
            if (_bottomSheetKey.currentState!.expansionStatus ==
                ExpansionStatus.contracted) {
              _bottomSheetKey.currentState!.expand();
            } else {
              _bottomSheetKey.currentState!.contract();
            }
          },
        ),
        expandableContent: CostsBottomSheet(
          lineItems: _invoice.lineItems,
          pricesIncludeVat: _invoice.pricesIncludeVat,
          calculationOptions: _invoice.calculationOptions,
        ),
      ),
    );
  }

  Widget _buildBodyForUltraWideLayout() {
    return Row(
      children: [
        Expanded(flex: 2, child: _buildForm(isUltraWideLayout: true)),
        const HorizontalMargin.large(),
        Expanded(
          child: ColoredBox(
            color: context.doneColors.uiBgSheet,
            child: Column(
              children: [
                const VerticalMargin.small(),
                GeneratorVatInfo(
                  isCentered: true,
                  pricesIncludeVat: _invoice.pricesIncludeVat,
                ),
                CostsBottomSheet(
                  lineItems: _invoice.lineItems,
                  pricesIncludeVat: _invoice.pricesIncludeVat,
                  calculationOptions: _invoice.calculationOptions,
                ),
                CostsBottomSheetHeader(
                  onContinuePressed: _saveChanges,
                  continueEnabled: _invoice.isValid,
                  amountToBePaid: _invoice.toPay,
                  isUltraWideLayout: true,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildForm({bool isUltraWideLayout = false}) {
    final screenHeight = MediaQuery.of(context).size.height;
    final bottomSheetHeaderHeight = screenHeight / 7;

    //* when keyboard is shown the bottom sheet header has a glitch
    //* this is used to hide BottomSheetHeader when keyboard is shown
    final bottomPadding =
        MediaQuery.of(context).viewInsets.bottom >
                0 // Keyboard visible?
            ? MediaQuery.of(context).viewInsets.bottom
            : bottomSheetHeaderHeight + MediaQuery.of(context).padding.bottom;

    final customerWithSensitiveData =
        _invoice.customer
          ..sensitiveCustomerData = _invoice.customerSensitiveData;
    return LayoutMargins(
      child: Form(
        key: _invoiceFormKey,
        child: Scrollbar(
          controller: scrollController,
          child: ListView(
            controller: scrollController,
            shrinkWrap: true,
            addRepaintBoundaries: false,
            children: <Widget>[
              const VerticalMargin.large(),
              if ((widget.job.tags?.contains(JobTag.express) ?? false))
                const AutoMargins(child: ExpressJobCard()),
              AutoMargins(
                child: CustomerCard(
                  customer: customerWithSensitiveData,
                  onTap: () {
                    final params = InvoiceCustomerProfileRouteParams(
                      customerId: widget.job.customer.id,
                      invoiceId: _invoice.id,
                      projectId: widget.job.id,
                    );
                    InvoiceCustomerProfileRoute(
                      params: params,
                    ).navigate(context);
                  },
                ),
              ),
              AutoMargins(
                child: GeneratorVatInfo(
                  pricesIncludeVat: _invoice.pricesIncludeVat,
                ),
              ),
              AutoMargins(
                child: GeneratorMultilineField(
                  label: S.of(context).workDescription,
                  onChange: (description) {
                    setState(() {
                      _hasChanges = true;
                      _invoice = _invoice.copyWith(
                        workDescription: description,
                      );
                    });
                  },
                  isRequired: true,
                  minLines: 5,
                  initialValue: _invoice.workDescription,
                ),
              ),
              if (_invoice.type == InvoiceType.generated) ...[
                AutoMargins(
                  child: InkWell(
                    onTap: () => _selectDueDate(context),
                    child: DoneTextField(
                      enabled: false,
                      controller: _dateController,
                      label: S.of(context).due,
                    ),
                  ),
                ),
                const VerticalMargin.verySmall(),
              ],
              LineItemsSection(
                lineItems: _invoice.lineItems,
                articleGroupRef:
                    _invoice.lockedArticleGroup ??
                    widget.articleGroup?.reference,
                defaultQuoteSettings: widget.defaultQuoteSettings,
                lineItemBuilder: ({article, type}) {
                  if (article != null)
                    return InvoiceLineItem.newItemFromArticle(article);

                  return InvoiceLineItem.newItemForType(
                    type!,
                    widget.defaultQuoteSettings,
                  );
                },
                onChange: () {
                  setState(() {
                    _invoice.recalculateValues();
                    _hasChanges = true;
                  });
                },
              ),
              AutoMargins(
                child: GeneratorMultilineField(
                  label: S.of(context).otherInformation,
                  onChange: (otherInfo) {
                    setState(() {
                      if (!_hasChanges) {
                        _hasChanges = true;
                      }
                      _invoice = _invoice.copyWith(otherInformation: otherInfo);
                    });
                  },
                  initialValue: _invoice.otherInformation,
                ),
              ),
              if (!widget.isNewlyCreatedDraft)
                AutoMargins(
                  child: DoneButton(
                    title: Text(S.of(context).deleteDraft),
                    style: DoneButtonStyle.negative,
                    onPressed: () async {
                      final shouldDelete = await showDeleteDraftConfirmDialog(
                        context,
                      );
                      if (shouldDelete) {
                        await Mutations.instance
                            .invoice(_invoice.documentReference)
                            .delete();
                        Navigator.of(context).pop();
                      }
                    },
                  ),
                ),
              VerticalMargin(
                margin: isUltraWideLayout ? Margins.small : bottomPadding,
              ),
            ].separatedBy(() => const VerticalMargin.medium()),
          ),
        ),
      ),
    );
  }

  Future<void> _selectDueDate(BuildContext context) async {
    final picked = await showDatePicker(
      helpText: "",
      context: context,
      firstDate: DateTime.now().add(const Duration(days: 1)),
      lastDate: DateTime.now().add(const Duration(days: 182)),
      initialDate: DateTime.now().add(
        const Duration(days: defaultInvoiceDueDateDays),
      ),
    );
    if (picked == null) return;
    setState(() {
      if (!_hasChanges) {
        _hasChanges = true;
      }
      _dateController.text = TimeFormatter.toYearMonthNumberDayFormat(picked);

      _invoice = _invoice.copyWith(dueDate: Timestamp.fromDate(picked));
    });
  }

  Future<bool> _saveChanges() async {
    if (!_invoiceFormKey.currentState!.validate()) return false;

    try {
      final invoiceRef = _invoice.documentReference;

      await Mutations.instance.invoice(invoiceRef).update(_invoice);

      if (_invoice.type == InvoiceType.basis) {
        final shouldContinue = await showPendingQuoteCancellationDialog(
          context: context,
          job: widget.job,
        );
        if (!shouldContinue) return false;
      }

      final isInvoiceSent = await Navigator.of(context).push(
        MaterialPageRoute<bool>(
          builder:
              (context) => InvoiceDetailsRouter(
                invoiceReference: invoiceRef,
                job: widget.job,
                showGoToProjectAction: false,
              ),
        ),
      );
      if (isInvoiceSent ?? false) {
        Navigator.of(context).pop();
        if (_invoice.type == InvoiceType.basis) {
          await showAdaptivePopup<void>(
            context: context,
            builder: (context) => InvoiceBasisSentDialog(job: widget.job),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            DoneSnackBar(
              variant: SnackBarVariant.success,
              title: Text(
                widget.invoice.creationSuccessSnackbarMessage(context),
              ),
              context: context,
            ),
          );
        }
      }

      return true;
    } catch (e) {
      GetIt.instance<Logger>().e(e);
      final snackBar = SnackBar(
        content: Text(S.of(context).errorOccuredTryAgain),
      );
      ScaffoldMessenger.of(context).showSnackBar(snackBar);
      return false;
    }
  }

  @override
  void dispose() {
    scrollController.dispose();
    _dateController.dispose();
    super.dispose();
  }

  Future<bool> onCancel(BuildContext context) async {
    if (_hasChanges) {
      return _promptKeepingDraft(context);
    } else if (widget.isNewlyCreatedDraft) {
      // New, unchanged draft. Delete.
      await Mutations.instance.invoice(_invoice.documentReference).delete();
    }
    return true;
  }

  /// Prompts the user with a dialog asking if user wants to keep draft, delete
  /// it or continue editing, after cancelling the modal.
  ///
  /// Returns if navigation should proceed or not.
  Future<bool> _promptKeepingDraft(BuildContext context) async {
    final selection = await showShouldDeleteDraftDialog(context);
    final invoiceRef = _invoice.documentReference;
    if (selection == LeavingGeneratorSelection.deleteDraft) {
      await Mutations.instance.invoice(invoiceRef).delete();
    } else if (selection == LeavingGeneratorSelection.saveDraft) {
      await Mutations.instance.invoice(invoiceRef).update(_invoice);
    }

    return (selection != LeavingGeneratorSelection.cancel);
  }
}
