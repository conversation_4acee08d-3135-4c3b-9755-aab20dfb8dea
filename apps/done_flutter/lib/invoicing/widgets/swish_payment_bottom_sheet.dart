import 'package:done_deductions/done_deductions.dart';
import 'package:done_flutter/invoicing/widgets/swish_payment_button.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

/// A bottom sheet showing [SwishPaymentButton]
class SwishPaymentBottomSheet extends StatelessWidget {
  const SwishPaymentBottomSheet({required this.invoice, super.key});

  final Invoice invoice;

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final bottomSheetHeaderHeight = screenHeight / 10;

    return Container(
      padding: EdgeInsets.only(
        bottom: Margins.small + MediaQuery.of(context).padding.bottom,
      ),
      height: bottomSheetHeaderHeight,
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: context.doneColors.uiPrimary,
        border: Border.all(color: context.doneColors.uiBg1),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: SwishPaymentButton(
          invoice: invoice,
          redirectLocation: _invoiceInstitutionRequestRoute(
            '{{requestId}}',
          ).location(context),
          onRequestCreated: (context, requestId) {
            _invoiceInstitutionRequestRoute(requestId).navigate(context);
          },
        ),
      ),
    );
  }

  InvoiceInstitutionRequestRoute _invoiceInstitutionRequestRoute(
    String requestId,
  ) {
    final params = InvoiceInstitutionRequestRouteParams(
      projectId: invoice.job.id,
      invoiceId: invoice.id,
      requestId: requestId,
    );
    return InvoiceInstitutionRequestRoute(params: params);
  }
}
