import 'package:done_auth/done_auth.dart';
import 'package:done_database/done_database.dart';
import 'package:done_image/done_image.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_models/done_models.dart';
import 'package:done_router/done_router.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';

typedef SwishPaymentOnRequestCreated =
    void Function(BuildContext context, String requestId);

/// A Material Design "Swish button" to initiate swish payments.
///
/// When adding Swish widgets to the UI, special care should be taken to
/// the official [Swish design guide](https://developer.swish.nu/documentation/guidelines).
//
class SwishPaymentButton extends StatelessWidget {
  const SwishPaymentButton({
    super.key,
    required this.invoice,
    required this.onRequestCreated,
    required this.redirectLocation,
  });

  final Invoice invoice;
  final SwishPaymentOnRequestCreated onRequestCreated;
  final String redirectLocation;

  @override
  Widget build(BuildContext context) {
    return DoneButton(
      style: DoneButtonStyle.neutral,
      title: Row(
        children: [
          Text(S.of(context).payWith),
          const HorizontalMargin.small(),
          Image.asset(
            context.isDarkMode
                ? ImageAssets.swishLogoSecondaryDark
                : ImageAssets.swishLogoSecondaryLight,
            height: Margins.xlarge,
          ),
        ],
      ),
      onPressed: () async {
        final request = await GetIt.instance<InstitutionRequestRepository>()
            .createInstitutionRequest(
              type: InstitutionRequestType.requestSwishPayment,
              requesterId: context.authState.user!.documentReference.id,
              sensitive: {},
              input: {
                "invoiceId": invoice.id,
                'redirect':
                    "${DoneRouter.appBaseUrlScheme}://${Uri.decodeFull(redirectLocation)}",
              },
            );

        onRequestCreated(context, request.documentReference.id);
      },
    );
  }
}
