import 'package:cloud_functions/cloud_functions.dart';
import 'package:done_flutter/utils/extensions/enums/invoice.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:done_localizations/done_localizations.dart';

class SendInvoiceErrorDialog extends StatelessWidget {
  const SendInvoiceErrorDialog({
    super.key,
    required this.error,
    required this.invoice,
  });

  final dynamic error;
  final BaseInvoice invoice;

  @override
  Widget build(BuildContext context) {
    return DoneDialog(
      title: Text(invoice.sendingFailedTitle(context)),
      content: [
        Text(_messageFromError(context)),
        const VerticalMargin.small(),
        DoneButton(
          style: DoneButtonStyle.neutral,
          title: Text(S.of(context).dismiss),
          onPressed: () async {
            Navigator.of(context).pop();
          },
        ),
      ],
    );
  }

  String _messageFromError(BuildContext context) {
    final badConnection =
        error is FirebaseFunctionsException &&
        // FirebaseFunctionsException code for no internet connection
        (error as FirebaseFunctionsException).code == '-1009';
    if (badConnection) {
      return S.of(context).badConnectionOnRequestMessage;
    } else {
      return invoice.sendingFailedMessage(context);
    }
  }
}
