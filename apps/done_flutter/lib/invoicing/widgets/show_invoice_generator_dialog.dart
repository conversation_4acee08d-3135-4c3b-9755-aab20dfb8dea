import 'package:done_flutter/invoicing/pages/invoice_generator_page.dart';
import 'package:done_flutter/ui/pages/company/shared_generator/widgets/show_generator_modal.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

/// Shows invoice generator screen for invoice generation before sending it to customer
///
/// if [invoice] is left as `null` a new invoice will be created
Future<void> showInvoiceGenerator({
  required BuildContext context,
  required Job job,
  MinimalInvoiceInfo? invoice,
  Quote? quote,
  JobArticleGroup? articleGroup,
}) async {
  final screenWidth = MediaQuery.of(context).size.width;
  final isUltraWideLayout = screenWidth > Layouts.ultraWideLayout;

  isUltraWideLayout
      ? await Navigator.push(
        context,
        MaterialPageRoute<void>(
          builder:
              (context) => InvoiceGeneratorPage(
                job: job,
                invoice: invoice,
                quote: quote,
                articleGroup: articleGroup,
              ),
        ),
      )
      : _showInvoiceGeneratorModalBottomSheet(
        context,
        job,
        invoice,
        quote,
        articleGroup,
      );
}

Future<void> _showInvoiceGeneratorModalBottomSheet(
  BuildContext context,
  Job job,
  MinimalInvoiceInfo? invoice,
  Quote? quote,
  JobArticleGroup? articleGroup,
) async {
  await showGeneratorModal(
    context: context,
    child: InvoiceGeneratorPage(
      job: job,
      invoice: invoice,
      quote: quote,
      articleGroup: articleGroup,
    ),
  );
}
