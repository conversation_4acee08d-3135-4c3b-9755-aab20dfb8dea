import 'package:done_image/done_image.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

class InvoiceBasisSentDialog extends StatelessWidget {
  const InvoiceBasisSentDialog({required this.job, super.key});

  final Job job;

  @override
  Widget build(BuildContext context) {
    return DonePopup(
      content: [
        Column(
          children: [
            Center(child: Image.asset(ImageAssets.reportDone, scale: 2)),
            Text(
              S.of(context).invoiceBasisSentTitle,
              style: Theme.of(context).textTheme.titleLarge,
              textAlign: TextAlign.center,
            ),
            Text(
              job.network.id == donePartnerId
                  ? S.of(context).invoiceBasisSentMessage
                  : S.of(context).invoiceBasisSentMessageOtherNetwork,
              style: Theme.of(context).textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
            <PERSON><PERSON><PERSON><PERSON>(
              style: <PERSON>ButtonStyle.secondary,
              title: Text(S.of(context).actionGreat),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ].separatedBy(() => const VerticalMargin.medium()),
        ),
      ],
    );
  }
}
