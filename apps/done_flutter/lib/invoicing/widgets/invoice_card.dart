import 'package:done_auth/done_auth.dart';
import 'package:done_deductions/done_deductions.dart';
import 'package:done_flutter/core/router/routes/project_routes.dart';
import 'package:done_flutter/invoicing/widgets/swish_payment_button.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_flutter/invoicing/widgets/show_invoice_generator_dialog.dart';

import 'package:done_flutter/utils/extensions/enums/invoice.dart';
import 'package:done_flutter/utils/pdf_util.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

class InvoiceCard extends StatelessWidget {
  const InvoiceCard({
    super.key,
    required this.invoice,
    required this.job,
    this.isChatItem = false,
  });

  final MinimalInvoiceInfo invoice;
  final Job job;
  final bool isChatItem;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (!isChatItem) ...[
          Text(
            TimeFormatter.getShortHumanReadableDate(
              invoice.events?.sent?.toDate() ?? invoice.createTime?.toDate(),
              context,
            ),
            style: Theme.of(context).textTheme.bodyLarge!.apply(
              color: context.doneColors.typographyMediumContrast,
            ),
          ),
          const VerticalMargin.verySmall(),
        ],
        ContentCard(
          title: _invoiceTitle(context),
          subTitle: Text(
            invoice.informationText(context, invoice.isOverdue),
            style: Theme.of(context).textTheme.bodyMedium!.apply(
              color: context.doneColors.uiOnPrimary,
            ),
          ),
          onTap: () => _showInvoice(context),
          backgroundColor: invoice.cardBgColor(invoice.isOverdue, context),
          height:
              (invoice.deductionTypes != null &&
                      invoice.deductionTypes!.length > 1)
                  ? 88
                  : 74,
          leading: DeductionTypesBadge(deductionTypes: invoice.deductionTypes),
          actions:
              invoice.status != InvoiceStatus.draft ? _actions(context) : null,
        ),
      ],
    );
  }

  String _invoiceTitle(BuildContext context) {
    final invoiceTitle =
        invoice.type == InvoiceType.basis
            ? S.of(context).invoiceBasis
            : S.of(context).invoice;
    return "$invoiceTitle (${getPriceString(invoice.calculatedValues!.totalNetAfterDeductions / 100)})";
  }

  Future<void> _showInvoice(BuildContext context) async {
    final showGenerator =
        (invoice.status == InvoiceStatus.draft &&
            invoice.type == InvoiceType.generated) ||
        (invoice.basisStatus == InvoiceBasisStatus.draft &&
            invoice.type == InvoiceType.basis);
    if (showGenerator)
      return showInvoiceGenerator(context: context, invoice: invoice, job: job);

    switch (invoice.type) {
      case InvoiceType.basis:
      case InvoiceType.generated:
        final params = InvoiceDetailsRouteParams(
          invoiceId: invoice.id,
          projectId: job.id,
        );
        return InvoiceDetailsRoute(params: params).navigate(context);

      case InvoiceType.pdf:
        await viewAsPdf(
          context: context,
          storagePath: invoice.pdfFilePath!,
          pushRoute: (url) {
            final params = ViewInvoicePdfRouteParams(
              projectId: job.id,
              invoiceId: invoice.id,
              url: url,
            );
            ViewInvoicePdfRoute(params: params).navigate(context);
          },
        );
        break;
    }
  }

  List<Widget> _actions(BuildContext context) {
    final authState = context.authState;

    final showSwishPaymentButton =
        !isChatItem &&
        authState.isUserCustomer() &&
        invoice.type == InvoiceType.generated &&
        invoice.status == InvoiceStatus.sent;

    return [
      if (showSwishPaymentButton)
        Padding(
          padding: const EdgeInsets.only(bottom: Margins.medium),
          child: SwishPaymentButton(
            invoice: invoice as Invoice,
            redirectLocation: _projectInstitutionRequestRoute(
              '{{requestId}}',
            ).location(context),
            onRequestCreated: (context, requestId) {
              _projectInstitutionRequestRoute(requestId).navigate(context);
            },
          ),
        ),
    ];
  }

  ProjectInstitutionRequestRoute _projectInstitutionRequestRoute(
    String requestId,
  ) {
    final params = ProjectInstitutionRequestRouteParams(
      projectId: job.id,
      requestId: requestId,
    );
    return ProjectInstitutionRequestRoute(params: params);
  }
}
