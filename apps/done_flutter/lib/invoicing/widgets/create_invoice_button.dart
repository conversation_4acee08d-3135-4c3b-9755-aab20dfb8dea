import 'package:done_flutter/invoicing/widgets/create_invoice_dialog.dart';
import 'package:done_flutter/utils/extensions/enums/quote_creation_type.dart';
import 'package:done_flutter/utils/extensions/models/job.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

/// Button for creating a new invoice
class CreateInvoiceButton extends StatelessWidget {
  const CreateInvoiceButton({super.key, required this.job});

  final Job job;

  @override
  Widget build(BuildContext context) {
    return DoneButton(
      style:
          job.isInvoiceBasisSent || job.isInvoicePaid
              ? DoneButtonStyle.neutral
              : DoneButtonStyle.secondary,
      title: Text(job.createInvoiceLabel(context)),
      onPressed: () async {
        return showCreateInvoiceDialog(context: context, job: job);
      },
    );
  }
}

Future<void> showCreateInvoiceDialog({
  required BuildContext context,
  required Job job,
}) async {
  if (job.quoteCreationTypes.length == 1) {
    return job.quoteCreationTypes.first.invoiceAction(context, job);
  }
  return showAdaptivePopup<void>(
    builder:
        (dialogContext) =>
            CreateInvoiceDialog(job: job, originalContext: context),
    context: context,
  );
}
