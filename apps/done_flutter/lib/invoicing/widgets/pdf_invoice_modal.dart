import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:cross_file/cross_file.dart';
import 'package:done_auth/done_auth.dart';
import 'package:done_flutter/core/services/firebase_storage_service.dart';
import 'package:done_database/done_database.dart';

import 'package:done_localizations/done_localizations.dart';
import 'package:done_flutter/invoicing/widgets/send_invoice_error_dialog.dart';
import 'package:done_flutter/ui/widgets/deduction_type_selector.dart';
import 'package:done_flutter/utils/text_field_utilities.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';

class PdfInvoiceModal extends StatefulWidget {
  const PdfInvoiceModal({super.key, required this.pdfFile, required this.job});

  final XFile pdfFile;
  final Job job;

  @override
  State<PdfInvoiceModal> createState() => _PdfInvoiceModalState();
}

class _PdfInvoiceModalState extends State<PdfInvoiceModal> {
  late final GlobalKey<FormState> _pdfInvoiceFormKey;
  late final TextEditingController _dateController;
  late final DocumentReference<BaseInvoice> _invoiceRef;
  late final Reference? _fileRef;
  late bool _isUploading;
  late DateTime _dueDate;
  late DeductionType _deductionType;
  int? _amountToPay;

  @override
  void initState() {
    super.initState();
    _pdfInvoiceFormKey = GlobalKey<FormState>();
    _deductionType = DeductionType.rot;
    _invoiceRef =
        FirebaseFirestore.instance
            .collection('invoices')
            .doc()
            .withBaseInvoiceConverter;
    _dueDate = DateTime.now().add(
      const Duration(days: defaultInvoiceDueDateDays),
    );
    _dateController = txtCtrlFromValue(
      TimeFormatter.toYearMonthNumberDayFormat(_dueDate),
    );
    _isUploading = true;
    _uploadPdf();
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _pdfInvoiceFormKey,
      autovalidateMode: AutovalidateMode.onUserInteraction,
      child: SingleChildScrollView(
        child: DonePopup(
          title: Text(S.of(context).uploadPDFInvoice),
          subtitle: Text(widget.pdfFile.name),
          content: [
            DeductionTypeSelector(
              currentDeductionType: _deductionType,
              deductionTypes: DeductionType.values,
              onChange: (type) {
                setState(() => _deductionType = type);
              },
            ),
            const VerticalMargin.medium(),
            TextFormField(
              keyboardType: TextInputType.number,
              style: Theme.of(
                context,
              ).textTheme.bodyMedium!.apply(color: context.doneColors.uiBlack),
              onChanged: (change) {
                setState(() => _amountToPay = int.tryParse(change));
              },
              validator: FormValidators.compose(context, [
                FormValidators.required,
                FormValidators.validAmount,
                FormValidators.validInt,
              ]),
              decoration: DoneFormFieldHolder.inputDecoration(
                label: Text(S.of(context).toPayAfterTax),
                textStyle: Theme.of(context).textTheme.bodyLarge,
                context: context,
              ),
            ),
            const VerticalMargin.medium(),

            InkWell(
              onTap: () => _selectDueDate(context),
              child: DoneTextField(
                enabled: false,
                controller: _dateController,
                label: S.of(context).due,
              ),
            ),
            const VerticalMargin.medium(),
            DoneAsyncAction(
              action: _sendPdfInvoice,
              builder:
                  (context, actionOrNull, isLoading) => DoneButton(
                    title:
                        _isUploading || isLoading
                            ? const DoneAdaptiveLoadingIndicator()
                            : Text(S.of(context).sendInvoice),
                    style: DoneButtonStyle.secondary,
                    onPressed: _isUploading ? null : actionOrNull,
                  ),
            ),
            const VerticalMargin.medium(),
            DoneButton(
              title: Text(S.of(context).cancel),
              style: DoneButtonStyle.neutral,
              onPressed: () {
                if (!_isUploading && _fileRef != null) {
                  _fileRef.delete();
                }
                Navigator.of(context).pop();
              },
            ),

            // To compensate for soft-keyboard on mobile
            VerticalMargin(margin: MediaQuery.of(context).viewInsets.bottom),
          ],
        ),
      ),
    );
  }

  Future<void> _uploadPdf() async {
    _fileRef = await GetIt.instance<FirebaseStorageService>().uploadPdf(
      widget.pdfFile,
      _invoiceRef,
      widget.job.documentReference,
    );

    setState(() => _isUploading = false);
  }

  Future<void> _sendPdfInvoice() async {
    if (_pdfInvoiceFormKey.currentState!.validate()) {
      final senderName = context.authState.user!.fullName;
      final invoiceInfo = _createInvoiceInfo();
      final invoice = await GetIt.instance<InvoicesRepository>()
          .createPdfDraftInvoice(
            widget.job,
            senderName ?? S.of(context).anonymous,
            invoiceInfo,
          );

      try {
        await FirebaseFunctions.instanceFor(
          region: 'europe-west1',
        ).httpsCallable('sendInvoice').call<void>({"invoiceId": invoice.id});
        Navigator.of(context).pop();
      } catch (e) {
        return showDialog<void>(
          context: context,
          builder:
              (context) => SendInvoiceErrorDialog(error: e, invoice: invoice),
        );
      }
    }
  }

  MinimalInvoiceInfo _createInvoiceInfo() {
    return MinimalInvoiceInfo(
      pdfFilePath: _fileRef?.fullPath,
      id: _invoiceRef.id,
      documentReference: _invoiceRef,
      status: InvoiceStatus.draft,
      deductionTypes: [_deductionType],
      dueDate: Timestamp.fromDate(_dueDate),
      invoiceDate: null,
      type: InvoiceType.pdf,
      calculatedValues: BaseCentValues(
        totalNetAfterDeductions: _amountToPay! * 100,
      ),
    );
  }

  Future<void> _selectDueDate(BuildContext context) async {
    final picked = await showDatePicker(
      helpText: "",
      context: context,
      firstDate: DateTime.now().add(const Duration(days: 1)),
      lastDate: DateTime.now().add(const Duration(days: 182)),
      initialDate: DateTime.now().add(
        const Duration(days: defaultInvoiceDueDateDays),
      ),
    );
    if (picked == null) return;
    setState(() {
      _dueDate = picked;
      _dateController.text = TimeFormatter.toYearMonthNumberDayFormat(picked);
    });
  }

  @override
  void dispose() {
    _dateController.dispose();
    super.dispose();
  }
}
