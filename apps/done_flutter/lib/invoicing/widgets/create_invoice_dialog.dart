import 'package:done_flutter/ui/pages/company/shared_generator/generator_type.dart';
import 'package:done_flutter/utils/extensions/enums/quote_creation_type.dart';
import 'package:done_flutter/utils/extensions/models/job.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:flutter/material.dart';

/// Used to let the user choose which invoice type to send
///
/// Current options are new invoice or send PDF invoice
class CreateInvoiceDialog extends StatelessWidget {
  const CreateInvoiceDialog({
    super.key,
    required this.job,
    required this.originalContext,
  });

  final Job job;

  /// This is used to pass down the origin page context as this dialog will be popped on selection
  ///
  /// The original context is needed for showing further dialog down the flow
  final BuildContext originalContext;

  @override
  Widget build(BuildContext context) {
    return DonePopup(
      title: Text(job.createInvoiceLabel(context)),
      content: [
        const VerticalMargin.verySmall(),
        ...job.quoteCreationTypes.map(
          (type) => type.button(originalContext, GeneratorType.invoice, job),
        ),
        DoneButton(
          title: Text(S.of(context).cancel),
          style: DoneButtonStyle.neutral,
          onPressed: () => Navigator.pop(context),
        ),
      ].separatedBy(() => const VerticalMargin.small()),
    );
  }
}
