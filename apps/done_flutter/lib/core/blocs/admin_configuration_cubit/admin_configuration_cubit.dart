import 'dart:async';

import 'package:done_database/done_database.dart';

import 'package:done_models/done_models.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

class AdminConfigurationCubit extends Cubit<AdminConfiguration?> {
  AdminConfigurationCubit() : super(null) {
    _adminConfigStream = GetIt.instance<BookingRepository>()
        .adminConfiguration()
        .listen(_handleAdminConfigChanges);
  }
  late final StreamSubscription<AdminConfiguration> _adminConfigStream;

  void _handleAdminConfigChanges(AdminConfiguration config) {
    emit(config);
  }

  @override
  Future<void> close() async {
    await _adminConfigStream.cancel();
    return super.close();
  }
}
