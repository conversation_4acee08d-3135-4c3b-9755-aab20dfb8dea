import 'package:flutter_bloc/flutter_bloc.dart';

/// A [Cubit] that holds the configuration for the current session.
class SessionConfigurationCubit extends Cubit<SessionConfiguration> {
  SessionConfigurationCubit() : super(SessionConfiguration.initial());

  /// Updates the state to indicate that the user has been prompted to take critical actions.
  void markCriticalActionDialogAsPrompted() {
    emit(state.copyWith(didPromptCriticalActionDialog: true));
  }
}

/// A class that holds the configuration for the current session.
class SessionConfiguration {
  SessionConfiguration({required this.didPromptCriticalActionDialog});
  factory SessionConfiguration.initial() {
    return SessionConfiguration(didPromptCriticalActionDialog: false);
  }

  /// Indicates whether the user has been prompted to take critical actions.
  final bool didPromptCriticalActionDialog;

  SessionConfiguration copyWith({bool? didPromptCriticalActionDialog}) {
    return SessionConfiguration(
      didPromptCriticalActionDialog:
          didPromptCriticalActionDialog ?? this.didPromptCriticalActionDialog,
    );
  }
}
