import 'package:flutter_bloc/flutter_bloc.dart';

class HomeTabsBadgeCubit extends Cubit<Map<int, int>> {
  HomeTabsBadgeCubit({required int tabsCount})
    : super(
        List.generate(
          tabsCount,
          (index) => index,
        ).asMap().map((key, value) => MapEntry(value, 0)),
      );

  void updateCountFor(int tab, int count) {
    state[tab] = count;
    emit(Map.from(state));
  }
}
