import 'package:flutter_bloc/flutter_bloc.dart';

/// This cubit holds the state of the item selection of each home tab
/// For example, for chat page and project page it holds the job id for each tab
///
/// It exists to preserve this information throughout app rebuilds
class HomeSelectionCubit extends Cubit<Map<int, String?>> {
  HomeSelectionCubit({required int tabsCount})
    : super(
        List.generate(
          tabsCount,
          (index) => index,
        ).asMap().map((key, value) => MapEntry(value, null)),
      );

  void updateSelectionFor(Enum tab, String selection) {
    state[tab.index] = selection;
    emit(Map.from(state));
  }
}
