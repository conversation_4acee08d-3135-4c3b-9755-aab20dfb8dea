import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

/// This holds the [Key] of currently expanded line item
///
/// If the value is `null` that means no line items are expanded
class ExpandedLineItemCubit extends Cubit<Key?> {
  ExpandedLineItemCubit() : super(null);

  /// Updates the [Key] of the current expanded line item
  ///
  /// If the current [state] is that same [key], we should emit `null` to collapse it
  void updateExpanded(Key? key) {
    if (state == key) {
      emit(null);
    } else {
      emit(key);
    }
  }
}
