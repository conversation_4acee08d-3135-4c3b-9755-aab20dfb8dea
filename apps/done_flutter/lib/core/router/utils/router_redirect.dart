import 'package:done_auth/done_auth.dart';
import 'package:done_flutter/core/router/routes/routes_definitions.dart';
import 'package:done_router/done_router.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

/// A redirect guard that redirects to login page if not authenticated or home if authenticated
String? routerAuthRedirect(BuildContext context, GoRouterState state) {
  final isUnauthenticated = context.authState.isUnauthenticated;
  final baseLoginPath = state.namedLocation(AuthRoutes.loginRoute.name);

  final currentLocation = state.matchedLocation;
  final onboardingPath = state.namedLocation(AppRoutes.onboardingRoute.name);
  final rootPath = state.namedLocation(AppRoutes.rootRoute.name);
  final isInitialLocation =
      currentLocation == baseLoginPath ||
      currentLocation == rootPath ||
      currentLocation == onboardingPath;

  // URL-encode full uri to preserve query params
  final toLocation =
      isInitialLocation ? null : Uri.encodeComponent(state.uri.toString());
  final loginPath = state.namedLocation(
    AuthRoutes.loginRoute.name,
    queryParameters: {if (toLocation != null) 'to': toLocation},
  );

  final isLoggingIn =
      state.matchedLocation == loginPath ||
      state.matchedLocation == onboardingPath;
  if (isUnauthenticated && isLoggingIn) return null;

  if (isUnauthenticated) return kIsWeb ? loginPath : onboardingPath;

  if (!isUnauthenticated && isLoggingIn) {
    // Handle redirect from login page on web
    if (!kIsWeb) return rootPath;

    // Remember that [to] parameter is URL-encoded
    final toParameter = state.uri.queryParameters['to'];
    return Uri.decodeComponent(toParameter ?? rootPath);
  }

  return null;
}
