/// Converts a [Uri] from deeplinking to corresponding location
///
/// If [uri] is `null` we return nothing
///
/// If [uri]'s `path` is a legacy path we return corresponding new location,
/// otherwise we return [uri]'s `path` as the location
String? convertUriToLocation(Uri? uri) {
  if (uri == null) return null;
  if (!_legacyLocations.contains(uri.path)) return uri.path;
  return _legacyToNewPath(
    _LegacyPath.values.firstWhere((p) => p.location == uri.path),
    uri.queryParameters,
  );
}

enum _LegacyPath {
  jobDetail,
  chat,
  rotrut,
  invoiceDetail,
  jobOffer,
  quoteDetail,
  paintingCalculator,
  floorSandingCalculator,
}

String _legacyToNewPath(_LegacyPath path, Map<String, String> params) {
  switch (path) {
    case _LegacyPath.jobDetail:
      return '/projects/${params['jobId']}';
    case _LegacyPath.chat:
      return '/projects/${params['jobId']}/chat';
    case _LegacyPath.rotrut:
      return '/check-rot-rut';
    case _LegacyPath.invoiceDetail:
      return '/projects/${params['jobId']}/invoices/${params['invoiceId']}';
    case _LegacyPath.jobOffer:
      return '/job-offers/${params['jobOfferId']}';
    case _LegacyPath.quoteDetail:
      return '/projects/${params['jobId']}/quotes/${params['quoteId']}';
    case _LegacyPath.paintingCalculator:
      return '/services/painter/calculators/painting';
    case _LegacyPath.floorSandingCalculator:
      return '/services/flooring/calculators/floor-sanding';
  }
}

final _legacyLocations = _LegacyPath.values.map((e) => e.location);

extension LegacyLocation on _LegacyPath {
  String get location {
    switch (this) {
      case _LegacyPath.jobDetail:
        return '/jobDetail';
      case _LegacyPath.chat:
        return '/chat';
      case _LegacyPath.rotrut:
        return '/rotrut';
      case _LegacyPath.invoiceDetail:
        return '/invoice/detail';
      case _LegacyPath.jobOffer:
        return '/jobOffer';
      case _LegacyPath.quoteDetail:
        return '/quote/detail';
      case _LegacyPath.paintingCalculator:
        return '/paintingCalculator';
      case _LegacyPath.floorSandingCalculator:
        return '/floorSandingCalculator';
    }
  }
}
