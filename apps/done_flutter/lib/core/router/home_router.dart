import 'package:done_analytics/done_analytics.dart';
import 'package:done_calls/done_calls.dart';
import 'package:done_flutter/core/configuration/app_config.dart';
import 'package:done_auth/done_auth.dart';
import 'package:done_flutter/ui/home/<USER>';
import 'package:done_flutter/ui/widgets/review_checker.dart';
import 'package:done_flutter/utils/company_tos_checker_util.dart';
import 'package:done_flutter/utils/first_build_callback.dart';
import 'package:done_models/done_models.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';

/// The home page for done app, it decides which screen to return based on [tabIndex] and current [AuthState]
class HomeRouter extends StatelessWidget {
  const HomeRouter({required this.tabIndex});
  final int tabIndex;

  @override
  Widget build(BuildContext context) {
    final authState = context.authState;

    if (!authState.isAuthenticated) return CenteredProgressIndicator();
    return FirstBuildCallback(
      onFirstBuild: () => _initialSetup(context),
      child: _initialPageFor(context, authState.user!),
    );
  }

  Widget _initialPageFor(BuildContext context, User user) {
    final isCompany = user.company != null;

    return isCompany
        ? CompanyToSChecker(child: HomePage(initialTab: tabIndex))
        : CustomerReviewReminder(child: HomePage(initialTab: tabIndex));
  }

  // TODO: Move setup to done app setup so it gets called only after user is authenticated once
  Future<void> _initialSetup(BuildContext context) async {
    if (!context.mounted) return;
    if (context.authState.isAnonymous) return;
    final vapidKey = AppConfig.of(context).flavorType.vapidKey;
    await MessagingPermissionsHandler.promptPushToken(vapidKey: vapidKey);
    final shouldLogFacebookEvents =
        !kIsWeb &&
        context.authState.hasValidUserDocument &&
        context.authState.isUserCustomer() &&
        GetIt.instance<AnalyticsRemoteConfigService>()
            .isFacebookTrackingEnabled;

    if (shouldLogFacebookEvents) {
      if (!context.mounted) return;
      final userId = context.authState.user!.documentReference.id;
      await EventLogger.instance.enableFacebookTracking(
        context: context,
        userId: userId,
      );
    }
  }
}
