part of 'routes_definitions.dart';

final class JobOfferPageRouteParams extends TypedRouteParams {
  JobOfferPageRouteParams({required this.jobOfferId, this.jobOfferType});

  @override
  factory JobOfferPageRouteParams.fromState(GoRouterState state) {
    final jobOfferId = state.pathParameters['jobOfferId']!;
    final jobOfferType = state.enumQueryParam(
      'jobOfferType',
      OfferPageType.values,
    );
    return JobOfferPageRouteParams(
      jobOfferId: jobOfferId,
      jobOfferType: jobOfferType,
    );
  }

  final String jobOfferId;
  final OfferPageType? jobOfferType;

  @override
  Map<String, String> get pathParams => {'jobOfferId': jobOfferId};

  @override
  Map<String, String> get queryParams => {
    if (jobOfferType != null) 'jobOfferType': jobOfferType!.name,
  };
}

final class JobOfferPhotoRouteParams extends TypedRouteParams {
  JobOfferPhotoRouteParams({required this.jobOfferId, this.initialIndex});

  @override
  factory JobOfferPhotoRouteParams.fromState(GoRouterState state) {
    final jobOfferId = state.pathParameters['jobOfferId']!;
    final initialIndex = state.intQueryParam('initialIndex');
    return JobOfferPhotoRouteParams(
      jobOfferId: jobOfferId,
      initialIndex: initialIndex,
    );
  }

  final String jobOfferId;
  final int? initialIndex;

  @override
  Map<String, String> get pathParams => {'jobOfferId': jobOfferId};

  @override
  Map<String, String> get queryParams => {
    if (initialIndex != null) 'initialIndex': initialIndex.toString(),
  };
}

final class TermsOfServiceRouteParams extends TypedRouteParams {
  TermsOfServiceRouteParams({required this.tosId});

  @override
  factory TermsOfServiceRouteParams.fromState(GoRouterState state) {
    final tosId = state.pathParameters['tosId']!;
    return TermsOfServiceRouteParams(tosId: tosId);
  }

  final String tosId;

  @override
  Map<String, String> get pathParams => {'tosId': tosId};
}

final class FixedPriceJobsRouteParams extends TypedRouteParams {
  FixedPriceJobsRouteParams({
    this.onlyShowActiveForCompany = false,
    this.enableTogglingActiveFixedPriceJobs = false,
  });

  @override
  factory FixedPriceJobsRouteParams.fromState(GoRouterState state) {
    final onlyShowActiveForCompany = state.boolQueryParam(
      'onlyShowActiveForCompany',
    );
    final enableTogglingActiveFixedPriceJobs = state.boolQueryParam(
      'enableTogglingActiveFixedPriceJobs',
    );
    return FixedPriceJobsRouteParams(
      onlyShowActiveForCompany: onlyShowActiveForCompany,
      enableTogglingActiveFixedPriceJobs: enableTogglingActiveFixedPriceJobs,
    );
  }

  final bool onlyShowActiveForCompany;
  final bool enableTogglingActiveFixedPriceJobs;

  @override
  Map<String, String> get queryParams => {
    'onlyShowActiveForCompany': onlyShowActiveForCompany.toString(),
    'enableTogglingActiveFixedPriceJobs':
        enableTogglingActiveFixedPriceJobs.toString(),
  };
}

final class ServiceDetailsRouteParams extends TypedRouteParams {
  ServiceDetailsRouteParams({required this.service});

  @override
  factory ServiceDetailsRouteParams.fromState(GoRouterState state) {
    final service = state.pathParameters['service']!;
    return ServiceDetailsRouteParams(service: service);
  }

  final String service;

  @override
  Map<String, String> get pathParams => {'service': service};
}

final class CompanyUserDetailRouteParams extends TypedRouteParams {
  CompanyUserDetailRouteParams({required this.userId});

  @override
  factory CompanyUserDetailRouteParams.fromState(GoRouterState state) {
    final userId = state.pathParameters['userId']!;
    return CompanyUserDetailRouteParams(userId: userId);
  }

  final String userId;

  @override
  Map<String, String> get pathParams => {'userId': userId};
}

final class ViewPriceListRouteParams extends TypedRouteParams {
  ViewPriceListRouteParams({required this.priceListId});

  @override
  factory ViewPriceListRouteParams.fromState(GoRouterState state) {
    final priceListId = state.pathParameters['priceListId']!;
    return ViewPriceListRouteParams(priceListId: priceListId);
  }

  final String priceListId;

  @override
  Map<String, String> get pathParams => {'priceListId': priceListId};
}

// Project routes params
