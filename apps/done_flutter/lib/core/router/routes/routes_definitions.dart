import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:done_auth/done_auth.dart';
import 'package:done_flutter/core/models/navigation_bar.dart';
import 'package:done_flutter/core/router/home_router.dart';
import 'package:done_flutter/ui/pages/chat/price_list_page.dart';
import 'package:done_flutter/ui/pages/company/fixed_price/fixed_price_jobs_categories.dart';
import 'package:done_flutter/ui/pages/company/job_reviews/job_reviews.dart';
import 'package:done_flutter/ui/pages/company/offers/company_answered_offers_page.dart';
import 'package:done_flutter/ui/pages/company/offers/company_offers_view.dart';
import 'package:done_flutter/ui/pages/company/offers/job_offer_page.dart';
import 'package:done_flutter/ui/pages/company/profile/company_invoices_page.dart';
import 'package:done_flutter/ui/pages/company/profile/company_profile.dart';
import 'package:done_flutter/ui/pages/company/profile/edit_company_profile.dart';
import 'package:done_flutter/ui/pages/company/projects/company_closed_projects.dart';
import 'package:done_flutter/ui/pages/company/tos/tos.dart';
import 'package:done_flutter/ui/pages/company/tos/tos_blocker.dart';
import 'package:done_flutter/ui/pages/company/users/company_add_user_page.dart';
import 'package:done_flutter/ui/pages/company/users/company_user_details.dart';
import 'package:done_flutter/ui/pages/company/users/company_users_page.dart';
import 'package:done_flutter/ui/pages/customer/calculators/floor_sanding_calculator.dart';
import 'package:done_flutter/ui/pages/customer/calculators/painting_calculator.dart';
import 'package:done_flutter/ui/pages/customer/customer_home_category_detail.dart';
import 'package:done_flutter/ui/pages/customer/customer_info_page.dart';
import 'package:done_flutter/ui/pages/customer/edit_customer_profile_page.dart';
import 'package:done_flutter/ui/pages/onboarding.dart';
import 'package:done_flutter/ui/pages/view_pdf/pdf_router_page.dart';
import 'package:done_flutter/ui/widgets/settings/user_settings_page.dart';
import 'package:done_image/done_image.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_models/done_models.dart';
import 'package:done_router/done_router.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';

// params
part 'routes_params.dart';

// names

enum AppRoutes {
  rootRoute,
  moreRoute,
  onboardingRoute,
  jobOffersRoute,
  jobOfferPageRoute,
  jobOfferPhotoRoute,
  deductionsBalanceRequestStatusRoute,
  checkDeductionsBalanceRoute,
  customerChatRoute,
  profileRoute,
  editProfileRoute,
  viewCustomerProfileRoute,
  closedProjectsRoute,
  answeredJobOffersRoute,
  termsOfServiceBlockerRoute,
  termsOfServiceRoute,
  quoteSettingsRoute,
  userSettingsRoute,
  fixedPriceJobsRoute,
  serviceDetailsRoute,
  paintingCalculatorRoute,
  floorSandingCalculateRoute,
  companyInvoicesRoute,
  companyInvoicePdfRoute,
  companyReviewsRoute,
  companyUsersRoute,
  companyAddUserRoute,
  companyUserDetailRoute,
  viewPriceListRoute,
}

// builders

CustomTransitionPage<void> rootRoutePageBuilder(
  BuildContext context,
  GoRouterState state,
) {
  final authState = context.authState;
  if (authState.user == null)
    return const NoTransitionPage(child: LoadingPage());

  final tabIndex =
      authState.isUserCustomer()
          ? CustomerHomeTabs.explore.index
          : CompanyHomeTabs.messages.index;

  return NoTransitionPage(child: HomeRouter(tabIndex: tabIndex));
}

CustomTransitionPage<void> moreRoutePageBuilder(
  BuildContext context,
  GoRouterState state,
) {
  return const NoTransitionPage(child: HomeRouter(tabIndex: 3));
}

Widget onboardingRouteBuilder(BuildContext context, GoRouterState state) {
  return Overlay(
    initialEntries: [
      OverlayEntry(builder: (context) => const OnBoardingScreen()),
    ],
  );
}

CustomTransitionPage<void> jobOffersRoutePageBuilder(
  BuildContext context,
  GoRouterState state,
) {
  return const NoTransitionPage(child: HomeRouter(tabIndex: 1));
}

Widget jobOfferPageRouteBuilder(BuildContext context, GoRouterState state) {
  final params = JobOfferPageRouteParams.fromState(state);

  return JobOfferPage(
    offerId: params.jobOfferId,
    offerType: params.jobOfferType ?? OfferPageType.pending,
  );
}

Widget jobOfferPhotoRouteBuilder(BuildContext context, GoRouterState state) {
  final params = JobOfferPhotoRouteParams.fromState(state);

  final dataSource = ImageUrlsPhotoDataSource(
    imageUrls: state.extra! as List<ImageUrls>,
    initialIndex: params.initialIndex ?? 0,
  );
  return PhotoPage(dataSource: dataSource);
}

CustomTransitionPage<void> customerChatRoutePageBuilder(
  BuildContext context,
  GoRouterState state,
) {
  final authState = context.authState;
  if (authState.user == null)
    return const NoTransitionPage(child: LoadingPage());

  return NoTransitionPage(
    child: HomeRouter(tabIndex: CustomerHomeTabs.chat.index),
  );
}

Page<void> profileRoutePageBuilder(BuildContext context, GoRouterState state) {
  final authState = context.authState;
  final user = authState.user;
  if (user == null) return const NoTransitionPage(child: LoadingPage());

  return authState.isUserCraftsman()
      ? MaterialPage(child: CompanyProfilePage(companyRef: user.company!))
      : NoTransitionPage(
        child: HomeRouter(tabIndex: CustomerHomeTabs.profile.index),
      );
}

Page<void> editProfileRoutePageBuilder(
  BuildContext context,
  GoRouterState state,
) {
  final authState = context.authState;
  if (authState.user == null)
    return const NoTransitionPage(child: LoadingPage());

  return authState.isUserCraftsman()
      ? const MaterialPage<void>(child: EditCompanyProfilePage())
      : doneModalRouter(const EditCustomerProfilePage());
}

Widget viewCustomerProfileRouteBuilder(
  BuildContext context,
  GoRouterState state,
) {
  final authState = context.authState;
  if (authState.user == null) return const LoadingPage();

  return CustomerInfoPage(customerId: authState.user!.documentReference.id);
}

Widget closedProjectsRouteBuilder(BuildContext context, GoRouterState state) {
  final authState = context.authState;
  if (!authState.isAuthenticated) return const LoadingPage();

  return const CompanyProjectsClosed();
}

Widget answeredJobOffersRouteBuilder(
  BuildContext context,
  GoRouterState state,
) {
  return const CompanyAnsweredOffersPage();
}

MaterialPage<void> termsOfServiceBlockerRoutePageBuilder(
  BuildContext context,
  GoRouterState state,
) {
  return const MaterialPage(
    child: TermsOfServiceBlockerPage(),
    fullscreenDialog: true,
  );
}

MaterialPage<void> termsOfServiceRoutePageBuilder(
  BuildContext context,
  GoRouterState state,
) {
  final params = TermsOfServiceRouteParams.fromState(state);
  final terms =
      FirebaseFirestore.instance
          .collection('companyTerms')
          .doc(params.tosId)
          .withTermsOfServiceConverter;

  return MaterialPage(
    child: TermsOfServicePage(terms: terms),
    fullscreenDialog: true,
  );
}

Widget userSettingsRouteBuilder(BuildContext context, GoRouterState state) {
  return UserSettingsPage();
}

Widget fixedPriceJobsRouteBuilder(BuildContext context, GoRouterState state) {
  final params = FixedPriceJobsRouteParams.fromState(state);
  final onListItemTap =
      state.extra as void Function(FixedPriceJob, BuildContext)?;
  return FixedPriceJobsCategoriesPage(
    enableTogglingActiveFixedPriceJobs:
        params.enableTogglingActiveFixedPriceJobs,
    onlyShowActiveForCompany: params.onlyShowActiveForCompany,
    onListItemTap: onListItemTap,
  );
}

CustomTransitionPage<void> viewPriceListRoutePageBuilder(
  BuildContext context,
  GoRouterState state,
) {
  final params = ViewPriceListRouteParams.fromState(state);
  final articleGroupRef =
      FirebaseFirestore.instance
          .collection('articleGroups')
          .doc(params.priceListId)
          .withArticleGroupConverter;
  return doneModalRouter(PriceListPage(articleGroupRef: articleGroupRef));
}

Widget serviceDetailsRouteBuilder(BuildContext context, GoRouterState state) {
  final params = ServiceDetailsRouteParams.fromState(state);
  return CustomerHomeCategoryDetail(service: serviceTypeFrom(params.service)!);
}

Widget paintingCalculatorRouteBuilder(
  BuildContext context,
  GoRouterState state,
) {
  return const PaintingCalculatorPage();
}

Widget floorSandingCalculateRouteBuilder(
  BuildContext context,
  GoRouterState state,
) {
  return const FloorSandingCalculatorPage();
}

Widget companyInvoicesRouteBuilder(BuildContext context, GoRouterState state) {
  return const CompanyInvoicesPage();
}

Widget companyInvoicePdfRouteBuilder(
  BuildContext context,
  GoRouterState state,
) {
  return PdfRouterPage(
    pdfFilePath: state.stringQueryParam('path')!,
    title: Text(S.of(context).invoice),
  );
}

Widget companyReviewsRouteBuilder(BuildContext context, GoRouterState state) {
  return const JobReviews();
}

Widget companyUsersRouteBuilder(BuildContext context, GoRouterState state) {
  return const CompanyUsersPage();
}

CustomTransitionPage<void> companyAddUserRoutePageBuilder(
  BuildContext context,
  GoRouterState state,
) {
  return doneModalRouter(const CompanyAddUserPage());
}

CustomTransitionPage<void> companyUserDetailRoutePageBuilder(
  BuildContext context,
  GoRouterState state,
) {
  final params = CompanyUserDetailRouteParams.fromState(state);
  final userRef =
      FirebaseFirestore.instance
          .collection('users')
          .doc(params.userId)
          .withUserConverter;
  return doneModalRouter(CompanyUserDetailPage(userRef: userRef));
}

// definitions
final class RootRoute extends NoParamsTypedRoute {
  const RootRoute();

  @override
  String get name => AppRoutes.rootRoute.name;
}

final class MoreRoute extends NoParamsTypedRoute {
  const MoreRoute();

  @override
  String get name => AppRoutes.moreRoute.name;
}

final class OnboardingRoute extends NoParamsTypedRoute {
  const OnboardingRoute();

  @override
  String get name => AppRoutes.onboardingRoute.name;
}

final class JobOffersRoute extends NoParamsTypedRoute {
  const JobOffersRoute();

  @override
  String get name => AppRoutes.jobOffersRoute.name;
}

final class JobOfferPageRoute
    extends ParamsTypedRoute<JobOfferPageRouteParams> {
  JobOfferPageRoute({required super.params});

  @override
  String get name => AppRoutes.jobOfferPageRoute.name;
}

final class JobOfferPhotoRoute
    extends TypedRoute<JobOfferPhotoRouteParams, List<ImageUrls>?> {
  JobOfferPhotoRoute({required super.params, super.extra});

  @override
  String get name => AppRoutes.jobOfferPhotoRoute.name;
}

final class CustomerChatRoute extends NoParamsTypedRoute {
  const CustomerChatRoute();

  @override
  String get name => AppRoutes.customerChatRoute.name;
}

final class ProfileRoute extends NoParamsTypedRoute {
  const ProfileRoute();

  @override
  String get name => AppRoutes.profileRoute.name;
}

final class EditProfileRoute extends NoParamsTypedRoute {
  const EditProfileRoute();

  @override
  String get name => AppRoutes.editProfileRoute.name;
}

final class ViewCustomerProfileRoute extends NoParamsTypedRoute {
  const ViewCustomerProfileRoute();

  @override
  String get name => AppRoutes.viewCustomerProfileRoute.name;
}

final class ClosedProjectsRoute extends NoParamsTypedRoute {
  const ClosedProjectsRoute();

  @override
  String get name => AppRoutes.closedProjectsRoute.name;
}

final class AnsweredJobOffersRoute extends NoParamsTypedRoute {
  const AnsweredJobOffersRoute();

  @override
  String get name => AppRoutes.answeredJobOffersRoute.name;
}

final class TermsOfServiceBlockerRoute extends NoParamsTypedRoute {
  const TermsOfServiceBlockerRoute();

  @override
  String get name => AppRoutes.termsOfServiceBlockerRoute.name;
}

final class TermsOfServiceRoute
    extends ParamsTypedRoute<TermsOfServiceRouteParams> {
  const TermsOfServiceRoute({required super.params});

  @override
  String get name => AppRoutes.termsOfServiceRoute.name;
}

final class QuoteSettingsRoute extends NoParamsTypedRoute {
  const QuoteSettingsRoute();

  @override
  String get name => AppRoutes.quoteSettingsRoute.name;
}

final class UserSettingsRoute extends NoParamsTypedRoute {
  const UserSettingsRoute();

  @override
  String get name => AppRoutes.userSettingsRoute.name;
}

final class FixedPriceJobsRoute
    extends TypedRoute<FixedPriceJobsRouteParams, dynamic> {
  FixedPriceJobsRoute({required super.params, super.extra});

  @override
  String get name => AppRoutes.fixedPriceJobsRoute.name;
}

final class ServiceDetailsRoute
    extends ParamsTypedRoute<ServiceDetailsRouteParams> {
  const ServiceDetailsRoute({required super.params});

  @override
  String get name => AppRoutes.serviceDetailsRoute.name;
}

final class PaintingCalculatorRoute extends NoParamsTypedRoute {
  const PaintingCalculatorRoute();

  @override
  String get name => AppRoutes.paintingCalculatorRoute.name;
}

final class FloorSandingCalculateRoute extends NoParamsTypedRoute {
  const FloorSandingCalculateRoute();

  @override
  String get name => AppRoutes.floorSandingCalculateRoute.name;
}

final class CompanyInvoicesRoute extends NoParamsTypedRoute {
  const CompanyInvoicesRoute();

  @override
  String get name => AppRoutes.companyInvoicesRoute.name;
}

final class CompanyReviewsRoute extends NoParamsTypedRoute {
  const CompanyReviewsRoute();

  @override
  String get name => AppRoutes.companyReviewsRoute.name;
}

final class CompanyUsersRoute extends NoParamsTypedRoute {
  const CompanyUsersRoute();

  @override
  String get name => AppRoutes.companyUsersRoute.name;
}

final class CompanyAddUserRoute extends NoParamsTypedRoute {
  const CompanyAddUserRoute();

  @override
  String get name => AppRoutes.companyAddUserRoute.name;
}

final class CompanyUserDetailRoute
    extends ParamsTypedRoute<CompanyUserDetailRouteParams> {
  const CompanyUserDetailRoute({required super.params});

  @override
  String get name => AppRoutes.companyUserDetailRoute.name;
}

final class ViewPriceListRoute
    extends ParamsTypedRoute<ViewPriceListRouteParams> {
  const ViewPriceListRoute({required super.params});

  @override
  String get name => AppRoutes.viewPriceListRoute.name;
}
