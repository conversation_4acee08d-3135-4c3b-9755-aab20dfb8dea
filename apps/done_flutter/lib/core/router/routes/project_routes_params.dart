part of 'project_routes.dart';

final class ProjectsSectionRouteParams extends TypedRouteParams {
  ProjectsSectionRouteParams({required this.section});

  @override
  factory ProjectsSectionRouteParams.fromState(GoRouterState state) {
    final sectionName = state.pathParameters['section']!;
    final section = ProjectsSection.values.byName(sectionName);
    return ProjectsSectionRouteParams(section: section);
  }

  final ProjectsSection section;

  @override
  Map<String, String> get pathParams => {'section': section.name};
}

final class ProjectDetailsRouteParams extends TypedRouteParams {
  ProjectDetailsRouteParams({required this.projectId});

  @override
  factory ProjectDetailsRouteParams.fromState(GoRouterState state) {
    final projectId = state.pathParameters['projectId']!;
    return ProjectDetailsRouteParams(projectId: projectId);
  }

  final String projectId;

  @override
  Map<String, String> get pathParams => {'projectId': projectId};
}

final class ChatPageRouteParams extends TypedRouteParams {
  ChatPageRouteParams({required this.projectId, this.showProjectLink = false});

  @override
  factory ChatPageRouteParams.fromState(GoRouterState state) {
    final projectId = state.pathParameters['projectId']!;
    final showProjectLink = state.boolQueryParam('showProjectLink');
    return ChatPageRouteParams(
      projectId: projectId,
      showProjectLink: showProjectLink,
    );
  }

  final String projectId;
  final bool showProjectLink;

  @override
  Map<String, String> get pathParams => {'projectId': projectId};

  @override
  Map<String, String> get queryParams => {
    'showProjectLink': showProjectLink.toString(),
  };
}

final class ProjectPhotoRouteParams extends TypedRouteParams {
  ProjectPhotoRouteParams({required this.projectId, this.initialIndex});

  @override
  factory ProjectPhotoRouteParams.fromState(GoRouterState state) {
    final projectId = state.pathParameters['projectId']!;
    final initialIndex = state.intQueryParam('initialIndex');
    return ProjectPhotoRouteParams(
      projectId: projectId,
      initialIndex: initialIndex,
    );
  }

  final String projectId;
  final int? initialIndex;

  @override
  Map<String, String> get pathParams => {'projectId': projectId};

  @override
  Map<String, String> get queryParams => {
    if (initialIndex != null) 'initialIndex': initialIndex.toString(),
  };
}

final class ProjectInstitutionRequestRouteParams extends TypedRouteParams {
  ProjectInstitutionRequestRouteParams({
    required this.projectId,
    required this.requestId,
  });

  @override
  factory ProjectInstitutionRequestRouteParams.fromState(GoRouterState state) {
    final projectId = state.pathParameters['projectId']!;
    final requestId = state.pathParameters['requestId']!;
    return ProjectInstitutionRequestRouteParams(
      projectId: projectId,
      requestId: requestId,
    );
  }

  final String projectId;
  final String requestId;

  @override
  Map<String, String> get pathParams => {
    'projectId': projectId,
    'requestId': requestId,
  };
}

final class ProjectReviewPhotoRouteParams extends TypedRouteParams {
  ProjectReviewPhotoRouteParams({required this.projectId, this.initialIndex});

  @override
  factory ProjectReviewPhotoRouteParams.fromState(GoRouterState state) {
    final projectId = state.pathParameters['projectId']!;
    final initialIndex = state.intQueryParam('initialIndex');
    return ProjectReviewPhotoRouteParams(
      projectId: projectId,
      initialIndex: initialIndex,
    );
  }

  final String projectId;
  final int? initialIndex;

  @override
  Map<String, String> get pathParams => {'projectId': projectId};

  @override
  Map<String, String> get queryParams => {
    if (initialIndex != null) 'initialIndex': initialIndex.toString(),
  };
}

final class VideoPlayerRouteParams extends TypedRouteParams {
  VideoPlayerRouteParams({
    required this.videoRef,
    this.aspectRatio,
    required this.projectId,
  });

  @override
  factory VideoPlayerRouteParams.fromState(GoRouterState state) {
    final videoRef = state.pathParameters['videoRef']!;
    final projectId = state.pathParameters['projectId']!;
    final aspectRatio = state.numQueryParam('aspectRatio');
    return VideoPlayerRouteParams(
      videoRef: videoRef,
      aspectRatio: aspectRatio,
      projectId: projectId,
    );
  }

  final String videoRef;
  final String projectId;
  final num? aspectRatio;

  @override
  Map<String, String> get pathParams => {
    'videoRef': videoRef,
    'projectId': projectId,
  };

  @override
  Map<String, String> get queryParams => {
    if (aspectRatio != null) 'aspectRatio': aspectRatio.toString(),
  };
}

final class ViewChatPdfRouteParams extends TypedRouteParams {
  ViewChatPdfRouteParams({required this.projectId, required this.url});

  @override
  factory ViewChatPdfRouteParams.fromState(GoRouterState state) {
    final projectId = state.pathParameters['projectId']!;
    final url = state.stringQueryParam('url')!;
    return ViewChatPdfRouteParams(projectId: projectId, url: url);
  }

  final String projectId;
  final String url;

  @override
  Map<String, String> get pathParams => {'projectId': projectId};

  @override
  Map<String, String> get queryParams => {'url': url};
}

final class ViewProjectPdfRouteParams extends TypedRouteParams {
  ViewProjectPdfRouteParams({required this.projectId, required this.url});

  @override
  factory ViewProjectPdfRouteParams.fromState(GoRouterState state) {
    final projectId = state.pathParameters['projectId']!;
    final url = state.stringQueryParam('url')!;
    return ViewProjectPdfRouteParams(projectId: projectId, url: url);
  }

  final String projectId;
  final String url;

  @override
  Map<String, String> get pathParams => {'projectId': projectId};

  @override
  Map<String, String> get queryParams => {'url': url};
}

final class ViewProjectPriceListRouteParams extends TypedRouteParams {
  ViewProjectPriceListRouteParams({
    required this.projectId,
    required this.priceListId,
  });

  @override
  factory ViewProjectPriceListRouteParams.fromState(GoRouterState state) {
    final projectId = state.pathParameters['projectId']!;
    final priceListId = state.pathParameters['priceListId']!;
    return ViewProjectPriceListRouteParams(
      projectId: projectId,
      priceListId: priceListId,
    );
  }

  final String projectId;
  final String priceListId;

  @override
  Map<String, String> get pathParams => {
    'projectId': projectId,
    'priceListId': priceListId,
  };
}

final class ViewProjectInstallationInfoRouteParams extends TypedRouteParams {
  ViewProjectInstallationInfoRouteParams({
    required this.projectId,
    required this.installationInfoId,
  });

  @override
  factory ViewProjectInstallationInfoRouteParams.fromState(
    GoRouterState state,
  ) {
    final projectId = state.pathParameters['projectId']!;
    final installationInfoId = state.pathParameters['installationInfoId']!;
    return ViewProjectInstallationInfoRouteParams(
      projectId: projectId,
      installationInfoId: installationInfoId,
    );
  }

  final String projectId;
  final String installationInfoId;

  @override
  Map<String, String> get pathParams => {
    'projectId': projectId,
    'installationInfoId': installationInfoId,
  };
}

final class ViewInvoicePdfRouteParams extends TypedRouteParams {
  ViewInvoicePdfRouteParams({
    required this.projectId,
    required this.invoiceId,
    required this.url,
  });

  @override
  factory ViewInvoicePdfRouteParams.fromState(GoRouterState state) {
    final projectId = state.pathParameters['projectId']!;
    final invoiceId = state.pathParameters['invoiceId']!;
    final url = state.stringQueryParam('url')!;
    return ViewInvoicePdfRouteParams(
      projectId: projectId,
      invoiceId: invoiceId,
      url: url,
    );
  }

  final String projectId;
  final String invoiceId;
  final String url;

  @override
  Map<String, String> get pathParams => {
    'projectId': projectId,
    'invoiceId': invoiceId,
  };

  @override
  Map<String, String> get queryParams => {'url': url};
}

final class ViewQuotePdfRouteParams extends TypedRouteParams {
  ViewQuotePdfRouteParams({
    required this.projectId,
    required this.quoteId,
    required this.url,
  });

  @override
  factory ViewQuotePdfRouteParams.fromState(GoRouterState state) {
    final projectId = state.pathParameters['projectId']!;
    final quoteId = state.pathParameters['quoteId']!;
    final url = state.stringQueryParam('url')!;
    return ViewQuotePdfRouteParams(
      projectId: projectId,
      quoteId: quoteId,
      url: url,
    );
  }

  final String projectId;
  final String quoteId;
  final String url;

  @override
  Map<String, String> get pathParams => {
    'projectId': projectId,
    'quoteId': quoteId,
  };

  @override
  Map<String, String> get queryParams => {'url': url};
}

final class ProjectCustomerProfileRouteParams extends TypedRouteParams {
  ProjectCustomerProfileRouteParams({
    required this.customerId,
    required this.projectId,
  });

  @override
  factory ProjectCustomerProfileRouteParams.fromState(GoRouterState state) {
    final customerId = state.pathParameters['customerId']!;
    final projectId = state.pathParameters['projectId']!;
    return ProjectCustomerProfileRouteParams(
      customerId: customerId,
      projectId: projectId,
    );
  }

  final String customerId;
  final String projectId;

  @override
  Map<String, String> get pathParams => {
    'customerId': customerId,
    'projectId': projectId,
  };
}

final class QuoteCustomerProfileRouteParams extends TypedRouteParams {
  QuoteCustomerProfileRouteParams({
    required this.quoteId,
    required this.customerId,
    required this.projectId,
  });

  @override
  factory QuoteCustomerProfileRouteParams.fromState(GoRouterState state) {
    final customerId = state.pathParameters['customerId']!;
    final projectId = state.pathParameters['projectId']!;
    final quoteId = state.pathParameters['quoteId']!;
    return QuoteCustomerProfileRouteParams(
      quoteId: quoteId,
      customerId: customerId,
      projectId: projectId,
    );
  }

  final String customerId;
  final String projectId;
  final String quoteId;

  @override
  Map<String, String> get pathParams => {
    'customerId': customerId,
    'projectId': projectId,
    'quoteId': quoteId,
  };
}

final class InvoiceCustomerProfileRouteParams extends TypedRouteParams {
  InvoiceCustomerProfileRouteParams({
    required this.invoiceId,
    required this.customerId,
    required this.projectId,
  });

  @override
  factory InvoiceCustomerProfileRouteParams.fromState(GoRouterState state) {
    final customerId = state.pathParameters['customerId']!;
    final projectId = state.pathParameters['projectId']!;
    final invoiceId = state.pathParameters['invoiceId']!;
    return InvoiceCustomerProfileRouteParams(
      customerId: customerId,
      projectId: projectId,
      invoiceId: invoiceId,
    );
  }

  final String customerId;
  final String projectId;
  final String invoiceId;

  @override
  Map<String, String> get pathParams => {
    'customerId': customerId,
    'projectId': projectId,
    'invoiceId': invoiceId,
  };
}

final class ProjectCompanyProfileRouteParams extends TypedRouteParams {
  ProjectCompanyProfileRouteParams({
    required this.companyId,
    required this.projectId,
  });

  @override
  factory ProjectCompanyProfileRouteParams.fromState(GoRouterState state) {
    final companyId = state.pathParameters['companyId']!;
    final projectId = state.pathParameters['projectId']!;
    return ProjectCompanyProfileRouteParams(
      companyId: companyId,
      projectId: projectId,
    );
  }

  final String companyId;
  final String projectId;

  @override
  Map<String, String> get pathParams => {
    'companyId': companyId,
    'projectId': projectId,
  };
}

final class QuoteCompanyProfileRouteParams extends TypedRouteParams {
  QuoteCompanyProfileRouteParams({
    required this.companyId,
    required this.quoteId,
    required this.projectId,
  });

  @override
  factory QuoteCompanyProfileRouteParams.fromState(GoRouterState state) {
    final companyId = state.pathParameters['companyId']!;
    final projectId = state.pathParameters['projectId']!;
    final quoteId = state.pathParameters['quoteId']!;
    return QuoteCompanyProfileRouteParams(
      companyId: companyId,
      quoteId: quoteId,
      projectId: projectId,
    );
  }

  final String companyId;
  final String quoteId;
  final String projectId;

  @override
  Map<String, String> get pathParams => {
    'companyId': companyId,
    'quoteId': quoteId,
    'projectId': projectId,
  };
}

final class InvoiceCompanyProfileRouteParams extends TypedRouteParams {
  InvoiceCompanyProfileRouteParams({
    required this.companyId,
    required this.invoiceId,
    required this.projectId,
  });

  @override
  factory InvoiceCompanyProfileRouteParams.fromState(GoRouterState state) {
    final companyId = state.pathParameters['companyId']!;
    final projectId = state.pathParameters['projectId']!;
    final invoiceId = state.pathParameters['invoiceId']!;
    return InvoiceCompanyProfileRouteParams(
      companyId: companyId,
      invoiceId: invoiceId,
      projectId: projectId,
    );
  }

  final String companyId;
  final String invoiceId;
  final String projectId;

  @override
  Map<String, String> get pathParams => {
    'companyId': companyId,
    'invoiceId': invoiceId,
    'projectId': projectId,
  };
}

final class QuoteDetailsRouteParams extends TypedRouteParams {
  QuoteDetailsRouteParams({required this.projectId, required this.quoteId});

  @override
  factory QuoteDetailsRouteParams.fromState(GoRouterState state) {
    final projectId = state.pathParameters['projectId']!;
    final quoteId = state.pathParameters['quoteId']!;
    return QuoteDetailsRouteParams(projectId: projectId, quoteId: quoteId);
  }

  final String projectId;
  final String quoteId;

  @override
  Map<String, String> get pathParams => {
    'projectId': projectId,
    'quoteId': quoteId,
  };
}

final class InvoiceDetailsRouteParams extends TypedRouteParams {
  InvoiceDetailsRouteParams({
    required this.invoiceId,
    required this.projectId,
    this.showGoToProjectAction = false,
  });

  @override
  factory InvoiceDetailsRouteParams.fromState(GoRouterState state) {
    final projectId = state.pathParameters['projectId']!;
    final invoiceId = state.pathParameters['invoiceId']!;
    final showGoToProjectAction = state.boolQueryParam('showGoToProjectAction');
    return InvoiceDetailsRouteParams(
      projectId: projectId,
      invoiceId: invoiceId,
      showGoToProjectAction: showGoToProjectAction,
    );
  }

  final String projectId;
  final String invoiceId;
  final bool showGoToProjectAction;

  @override
  Map<String, String> get pathParams => {
    'projectId': projectId,
    'invoiceId': invoiceId,
  };

  @override
  Map<String, String> get queryParams => {
    'showGoToProjectAction': showGoToProjectAction.toString(),
  };
}

final class CompanyReviewRouteParams extends TypedRouteParams {
  CompanyReviewRouteParams({
    required this.projectId,
    required this.companyId,
    this.withCamera = false,
  });

  @override
  factory CompanyReviewRouteParams.fromState(GoRouterState state) {
    final projectId = state.pathParameters['projectId']!;
    final companyId = state.pathParameters['companyId']!;
    final withCamera = state.boolQueryParam('withCamera');
    return CompanyReviewRouteParams(
      projectId: projectId,
      companyId: companyId,
      withCamera: withCamera,
    );
  }

  final String projectId;
  final String companyId;
  final bool withCamera;

  @override
  Map<String, String> get pathParams => {
    'projectId': projectId,
    'companyId': companyId,
  };

  @override
  Map<String, String> get queryParams => {'withCamera': withCamera.toString()};
}

final class CompanyReviewPhotoRouteParams extends TypedRouteParams {
  CompanyReviewPhotoRouteParams({
    required this.projectId,
    required this.companyId,
    required this.path,
  });

  @override
  factory CompanyReviewPhotoRouteParams.fromState(GoRouterState state) {
    final projectId = state.pathParameters['projectId']!;
    final companyId = state.pathParameters['companyId']!;
    final path = state.pathParameters['path']!;
    return CompanyReviewPhotoRouteParams(
      projectId: projectId,
      companyId: companyId,
      path: path,
    );
  }

  final String projectId;
  final String companyId;
  final String path;

  @override
  Map<String, String> get pathParams => {
    'projectId': projectId,
    'companyId': companyId,
    'path': path,
  };
}

final class NewJobReportRouteParams extends TypedRouteParams {
  NewJobReportRouteParams({
    required this.projectId,
    required this.reportFormPath,
    required this.type,
  });

  @override
  factory NewJobReportRouteParams.fromState(GoRouterState state) {
    final projectId = state.pathParameters['projectId']!;
    final reportFormPath = state.stringQueryParam('reportFormPath')!;
    final type = state.enumQueryParam('type', InstallationReportType.values)!;
    return NewJobReportRouteParams(
      projectId: projectId,
      reportFormPath: reportFormPath,
      type: type,
    );
  }

  final String projectId;
  final String reportFormPath;
  final InstallationReportType type;

  @override
  Map<String, String> get pathParams => {'projectId': projectId};

  @override
  Map<String, String> get queryParams => {
    'reportFormPath': reportFormPath,
    'type': type.name,
  };
}

final class JobReportRouteParams extends TypedRouteParams {
  JobReportRouteParams({
    required this.projectId,
    required this.reportId,
    required this.reportFormPath,
  });

  @override
  factory JobReportRouteParams.fromState(GoRouterState state) {
    final projectId = state.pathParameters['projectId']!;
    final reportId = state.pathParameters['reportId']!;
    final reportFormPath = state.stringQueryParam('reportFormPath')!;
    return JobReportRouteParams(
      projectId: projectId,
      reportId: reportId,
      reportFormPath: reportFormPath,
    );
  }

  final String projectId;
  final String reportId;
  final String reportFormPath;

  @override
  Map<String, String> get pathParams => {
    'projectId': projectId,
    'reportId': reportId,
  };

  @override
  Map<String, String> get queryParams => {'reportFormPath': reportFormPath};
}

final class JobReportStatusRouteParams extends TypedRouteParams {
  JobReportStatusRouteParams({required this.projectId, required this.reportId});

  @override
  factory JobReportStatusRouteParams.fromState(GoRouterState state) {
    final projectId = state.pathParameters['projectId']!;
    final reportId = state.pathParameters['reportId']!;
    return JobReportStatusRouteParams(projectId: projectId, reportId: reportId);
  }

  final String projectId;
  final String reportId;

  @override
  Map<String, String> get pathParams => {
    'projectId': projectId,
    'reportId': reportId,
  };
}

final class ViewReportPdfRouteParams extends TypedRouteParams {
  ViewReportPdfRouteParams({
    required this.projectId,
    required this.reportId,
    required this.url,
  });

  @override
  factory ViewReportPdfRouteParams.fromState(GoRouterState state) {
    final projectId = state.pathParameters['projectId']!;
    final reportId = state.pathParameters['reportId']!;
    final url = state.stringQueryParam('url')!;
    return ViewReportPdfRouteParams(
      projectId: projectId,
      reportId: reportId,
      url: url,
    );
  }

  final String projectId;
  final String reportId;
  final String url;

  @override
  Map<String, String> get pathParams => {
    'projectId': projectId,
    'reportId': reportId,
  };

  @override
  Map<String, String> get queryParams => {'url': url};
}
