import 'package:done_auth/done_auth.dart';
import 'package:done_booking/done_booking.dart';
import 'package:done_deductions/done_deductions.dart';
import 'package:done_flutter/core/router/routes/project_routes.dart';
import 'package:done_flutter/core/router/routes/routes_definitions.dart';
import 'package:done_router/done_router.dart';

final appRoutes = [
  GoRoute(
    path: '/',
    name: AppRoutes.rootRoute.name,
    pageBuilder: rootRoutePageBuilder,
    routes: [
      GoRoute(
        path: 'profile',
        name: AppRoutes.profileRoute.name,
        pageBuilder: profileRoutePageBuilder,
        routes: [
          GoRoute(
            path: 'edit',
            name: AppRoutes.editProfileRoute.name,
            pageBuilder: editProfileRoutePageBuilder,
          ),
          GoRoute(
            path: 'view',
            name: AppRoutes.viewCustomerProfileRoute.name,
            builder: viewCustomerProfileRouteBuilder,
          ),
        ],
      ),
      GoRoute(
        path: 'tos/not-signed',
        name: AppRoutes.termsOfServiceBlockerRoute.name,
        pageBuilder: termsOfServiceBlockerRoutePageBuilder,
      ),
      GoRoute(
        path: 'check-rot-rut',
        name: DoneDeductionsRoutes.checkDeductionsBalanceRoute.name,
        builder: checkDeductionsBalanceRouteBuilder,
        routes: [
          GoRoute(
            path: ':requestId/status',
            name: DoneDeductionsRoutes.deductionsBalanceRequestStatusRoute.name,
            pageBuilder: deductionsBalanceRequestStatusRoutePageBuilder,
          ),
        ],
      ),
      GoRoute(
        path: 'price-list/:priceListId',
        name: AppRoutes.viewPriceListRoute.name,
        pageBuilder: viewPriceListRoutePageBuilder,
      ),
      GoRoute(
        path: 'services/:service',
        name: AppRoutes.serviceDetailsRoute.name,
        builder: serviceDetailsRouteBuilder,
        routes: [
          GoRoute(
            path: 'create-booking',
            name: DoneBookingRoutes.createBookingRoute.name,
            pageBuilder: createBookingRoutePageBuilder,
            routes: [
              GoRoute(
                path: 'photo',
                name: DoneBookingRoutes.createBookingPhotoRoute.name,
                builder: createBookingPhotoRouteBuilder,
              ),
            ],
          ),
          GoRoute(
            path: 'calculators/painting',
            name: AppRoutes.paintingCalculatorRoute.name,
            builder: paintingCalculatorRouteBuilder,
          ),
          GoRoute(
            path: 'calculators/floor-sanding',
            name: AppRoutes.floorSandingCalculateRoute.name,
            builder: floorSandingCalculateRouteBuilder,
          ),
        ],
      ),
    ],
  ),
  GoRoute(
    path: '/more',
    name: AppRoutes.moreRoute.name,
    pageBuilder: moreRoutePageBuilder,
    routes: [
      GoRoute(
        path: 'users',
        name: AppRoutes.companyUsersRoute.name,
        builder: companyUsersRouteBuilder,
        routes: [
          GoRoute(
            path: 'add',
            name: AppRoutes.companyAddUserRoute.name,
            pageBuilder: companyAddUserRoutePageBuilder,
          ),
          GoRoute(
            path: ':userId',
            name: AppRoutes.companyUserDetailRoute.name,
            pageBuilder: companyUserDetailRoutePageBuilder,
          ),
        ],
      ),
      GoRoute(
        path: 'user-settings',
        name: AppRoutes.userSettingsRoute.name,
        builder: userSettingsRouteBuilder,
      ),
      GoRoute(
        path: 'tos/:tosId',
        name: AppRoutes.termsOfServiceRoute.name,
        pageBuilder: termsOfServiceRoutePageBuilder,
      ),
      GoRoute(
        path: 'fixed-prices',
        name: AppRoutes.fixedPriceJobsRoute.name,
        builder: fixedPriceJobsRouteBuilder,
        routes: [
          GoRoute(
            path: ':id',
            name: DoneBookingRoutes.fixedPriceJobDetailsRoute.name,
            builder: fixedPriceJobDetailsRouteBuilder,
          ),
        ],
      ),
      GoRoute(
        path: 'reviews',
        name: AppRoutes.companyReviewsRoute.name,
        builder: companyReviewsRouteBuilder,
      ),
      GoRoute(
        path: 'company-invoices',
        name: AppRoutes.companyInvoicesRoute.name,
        builder: companyInvoicesRouteBuilder,
        routes: [
          GoRoute(
            path: 'pdf',
            name: AppRoutes.companyInvoicePdfRoute.name,
            builder: companyInvoicePdfRouteBuilder,
          ),
        ],
      ),
    ],
  ),
  GoRoute(
    path: '/login',
    name: AuthRoutes.loginRoute.name,
    builder: loginRouteBuilder,
  ),
  GoRoute(
    path: '/onboarding',
    name: AppRoutes.onboardingRoute.name,
    builder: onboardingRouteBuilder,
  ),
  GoRoute(
    path: '/job-offers',
    name: AppRoutes.jobOffersRoute.name,
    pageBuilder: jobOffersRoutePageBuilder,
    routes: [
      GoRoute(
        path: 'answered',
        name: AppRoutes.answeredJobOffersRoute.name,
        builder: answeredJobOffersRouteBuilder,
      ),
      GoRoute(
        path: ':jobOfferId',
        name: AppRoutes.jobOfferPageRoute.name,
        builder: jobOfferPageRouteBuilder,
        routes: [
          GoRoute(
            path: 'photo',
            name: AppRoutes.jobOfferPhotoRoute.name,
            builder: jobOfferPhotoRouteBuilder,
          ),
        ],
      ),
    ],
  ),
  GoRoute(
    path: '/chats',
    name: AppRoutes.customerChatRoute.name,
    pageBuilder: customerChatRoutePageBuilder,
  ),
  GoRoute(
    path: '/projects',
    name: ProjectRoutes.projectsRoute.name,
    pageBuilder: projectsRoutePageBuilder,
    routes: [
      GoRoute(
        path: 'all',
        name: ProjectRoutes.allProjectsRoute.name,
        builder: allProjectsRouteBuilder,
      ),
      GoRoute(
        path: 'closed',
        name: AppRoutes.closedProjectsRoute.name,
        builder: closedProjectsRouteBuilder,
      ),
      GoRoute(
        path: 'section/:section',
        name: ProjectRoutes.projectsSectionRoute.name,
        builder: projectsSectionRouteBuilder,
      ),
      GoRoute(
        path: ':projectId',
        name: ProjectRoutes.projectDetailsRoute.name,
        builder: projectDetailsRouteBuilder,
        routes: [
          GoRoute(
            path: 'institution-request/:requestId',
            name: ProjectRoutes.projectInstitutionRequestRoute.name,
            pageBuilder: projectInstitutionRequestRoutePageBuilder,
          ),
          GoRoute(
            path: 'photo',
            name: ProjectRoutes.projectPhotoRoute.name,
            builder: projectPhotoRouteBuilder,
          ),
          GoRoute(
            path: 'review-photo',
            name: ProjectRoutes.projectReviewPhotoRoute.name,
            builder: projectReviewPhotoRouteBuilder,
          ),
          GoRoute(
            path: 'customers/:customerId',
            name: ProjectRoutes.projectCustomerProfileRoute.name,
            builder: projectCustomerProfileRouteBuilder,
          ),
          GoRoute(
            path: 'companies/:companyId',
            name: ProjectRoutes.projectCompanyProfileRoute.name,
            builder: projectCompanyProfileRouteBuilder,
          ),
          GoRoute(
            path: 'pdf',
            name: ProjectRoutes.viewProjectPdfRoute.name,
            builder: viewProjectPdfRouteBuilder,
          ),
          GoRoute(
            path: 'price-list/:priceListId',
            name: ProjectRoutes.viewProjectPriceListRoute.name,
            pageBuilder: viewProjectPriceListRoutePageBuilder,
          ),
          GoRoute(
            path: 'installation-info/:installationInfoId',
            name: ProjectRoutes.viewProjectInstallationInfoRoute.name,
            pageBuilder: viewProjectInstallationInfoRoutePageBuilder,
          ),
          GoRoute(
            path: 'chat',
            name: ProjectRoutes.chatPageRoute.name,
            builder: chatPageRouteBuilder,
            routes: [
              GoRoute(
                path: 'video/:videoRef',
                name: ProjectRoutes.videoPlayerRoute.name,
                builder: videoPlayerRouteBuilder,
              ),
              GoRoute(path: 'photo', builder: chatPhotoRouteBuilder),
              GoRoute(
                path: 'pdf',
                name: ProjectRoutes.viewChatPdfRoute.name,
                builder: viewChatPdfRouteBuilder,
              ),
            ],
          ),
          GoRoute(
            path: 'quotes/:quoteId',
            name: ProjectRoutes.quoteDetailsRoute.name,
            builder: quoteDetailsRouteBuilder,
            routes: [
              GoRoute(
                path: 'companies/:companyId',
                name: ProjectRoutes.quoteCompanyProfileRoute.name,
                builder: quoteCompanyProfileRouteBuilder,
              ),
              GoRoute(
                path: 'customers/:customerId',
                name: ProjectRoutes.quoteCustomerProfileRoute.name,
                builder: quoteCustomerProfileRouteBuilder,
              ),
              GoRoute(
                path: 'pdf',
                name: ProjectRoutes.viewQuotePdfRoute.name,
                builder: viewQuotePdfRouteBuilder,
              ),
            ],
          ),
          GoRoute(
            path: 'invoices/:invoiceId',
            name: ProjectRoutes.invoiceDetailsRoute.name,
            builder: invoiceDetailsRouteBuilder,
            routes: [
              GoRoute(
                path: 'companies/:companyId',
                name: ProjectRoutes.invoiceCompanyProfileRoute.name,
                builder: invoiceCompanyProfileRouteBuilder,
              ),
              GoRoute(
                path: 'customers/:customerId',
                name: ProjectRoutes.invoiceCustomerProfileRoute.name,
                builder: invoiceCustomerProfileRouteBuilder,
              ),
              GoRoute(
                path: 'pdf',
                name: ProjectRoutes.viewInvoicePdfRoute.name,
                builder: viewInvoicePdfRouteBuilder,
              ),
              GoRoute(
                path: 'institution-request/:requestId',
                name: DoneDeductionsRoutes.invoiceInstitutionRequestRoute.name,
                pageBuilder: invoiceInstitutionRequestRoutePageBuilder,
              ),
            ],
          ),
          GoRoute(
            path: 'report/new',
            name: ProjectRoutes.newJobReportRoute.name,
            pageBuilder: newJobReportRoutePageBuilder,
          ),
          GoRoute(
            path: 'report/:reportId',
            name: ProjectRoutes.jobReportRoute.name,
            pageBuilder: jobReportRoutePageBuilder,
            routes: [
              GoRoute(
                path: 'pdf',
                name: ProjectRoutes.viewReportPdfRoute.name,
                builder: viewReportPdfRouteBuilder,
              ),
              GoRoute(
                path: 'status',
                name: ProjectRoutes.jobReportStatusRoute.name,
                pageBuilder: jobReportStatusRoutePageBuilder,
              ),
            ],
          ),
          GoRoute(
            path: 'company-review/:companyId',
            name: ProjectRoutes.companyReviewRoute.name,
            pageBuilder: companyReviewRoutePageBuilder,
            routes: [
              GoRoute(
                path: 'photos',
                name: ProjectRoutes.companyReviewPhotoRoute.name,
                builder: companyReviewPhotoRouteBuilder,
              ),
            ],
          ),
        ],
      ),
    ],
  ),
  GoRoute(
    path: '/jobDetail',
    redirect: (_, state) => '/projects/${state.uri.queryParameters['jobId']}',
  ),
  GoRoute(
    path: '/chat',
    redirect:
        (_, state) => '/projects/${state.uri.queryParameters['jobId']}/chat',
  ),
  GoRoute(
    path: '/invoice/detail',
    redirect:
        (_, state) =>
            '/projects/${state.uri.queryParameters['jobId']}/invoices/${state.uri.queryParameters['invoiceId']}',
  ),
  GoRoute(
    path: '/jobOffer',
    redirect:
        (_, state) => '/job-offers/${state.uri.queryParameters['jobOfferId']}',
  ),
  GoRoute(
    path: '/quote/detail',
    redirect:
        (_, state) =>
            '/projects/${state.uri.queryParameters['jobId']}/quotes/${state.uri.queryParameters['quoteId']}',
  ),
];
