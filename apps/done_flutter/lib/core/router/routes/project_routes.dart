import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:done_auth/done_auth.dart';
import 'package:done_database/done_database.dart';
import 'package:done_deductions/done_deductions.dart';
import 'package:done_flutter/core/router/home_router.dart';
import 'package:done_flutter/invoicing/pages/invoice_details_router.dart';
import 'package:done_flutter/ui/pages/chat/chat_page.dart';
import 'package:done_flutter/ui/pages/chat/price_list_page.dart';
import 'package:done_flutter/ui/pages/chat/video_player_page.dart';
import 'package:done_flutter/ui/pages/company/profile/company_profile.dart';
import 'package:done_flutter/ui/pages/company/projects/pages/installation_information_page.dart';
import 'package:done_flutter/ui/pages/company/projects/pages/projects_list_page.dart';
import 'package:done_flutter/ui/pages/customer/customer_info_page.dart';
import 'package:done_flutter/ui/pages/job/job_detail_page.dart';
import 'package:done_flutter/ui/pages/quotes/quote_detail_router.dart';
import 'package:done_flutter/ui/pages/reviews/company/company_review_camera.dart';
import 'package:done_flutter/ui/pages/reviews/company/company_review_sheet.dart';
import 'package:done_flutter/ui/pages/view_pdf/view_pdf_page.dart';
import 'package:done_flutter/ui/widgets/progress/reports/job_report_form.dart';
import 'package:done_image/done_image.dart';
import 'package:done_models/done_models.dart';
import 'package:done_router/done_router.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';

// params
part 'project_routes_params.dart';

// names

enum ProjectRoutes {
  projectsRoute,
  allProjectsRoute,
  projectsSectionRoute,
  projectDetailsRoute,
  chatPageRoute,
  projectPhotoRoute,
  projectInstitutionRequestRoute,
  projectReviewPhotoRoute,
  videoPlayerRoute,
  viewChatPdfRoute,
  viewProjectPdfRoute,
  viewProjectPriceListRoute,
  viewProjectInstallationInfoRoute,
  viewInvoicePdfRoute,
  viewQuotePdfRoute,
  projectCustomerProfileRoute,
  quoteCustomerProfileRoute,
  invoiceCustomerProfileRoute,
  projectCompanyProfileRoute,
  quoteCompanyProfileRoute,
  invoiceCompanyProfileRoute,
  quoteDetailsRoute,
  invoiceDetailsRoute,
  companyReviewRoute,
  companyReviewPhotoRoute,
  newJobReportRoute,
  jobReportRoute,
  jobReportStatusRoute,
  viewReportPdfRoute,
}

// builders

NoTransitionPage<void> projectsRoutePageBuilder(
  BuildContext context,
  GoRouterState state,
) {
  final isCraftsman = context.authState.isUserCraftsman();
  final tabIndex = isCraftsman ? 2 : 1;
  return NoTransitionPage(child: HomeRouter(tabIndex: tabIndex));
}

Widget allProjectsRouteBuilder(BuildContext context, GoRouterState state) {
  final jobs = state.extra! as List<Job>;
  return ProjectsListPage(jobs: jobs);
}

Widget projectsSectionRouteBuilder(BuildContext context, GoRouterState state) {
  final params = ProjectsSectionRouteParams.fromState(state);
  final jobs = state.extra as List<Job>?;
  return ProjectsListPage(section: params.section, jobs: jobs);
}

Widget projectDetailsRouteBuilder(BuildContext context, GoRouterState state) {
  final params = ProjectDetailsRouteParams.fromState(state);
  final jobRef =
      FirebaseFirestore.instance
          .collection('jobs')
          .doc(params.projectId)
          .withJobConverter;

  return JobDetailPage(jobRef: jobRef);
}

Widget chatPageRouteBuilder(BuildContext context, GoRouterState state) {
  final params = ChatPageRouteParams.fromState(state);
  final jobRef =
      FirebaseFirestore.instance
          .collection('jobs')
          .doc(params.projectId)
          .withJobConverter;

  return ChatPage(jobRef: jobRef, showProjectLink: params.showProjectLink);
}

Widget projectPhotoRouteBuilder(BuildContext context, GoRouterState state) {
  final params = ProjectPhotoRouteParams.fromState(state);
  final dataSource = ImageUrlsPhotoDataSource(
    imageUrls: state.extra! as List<ImageUrls>,
    initialIndex: params.initialIndex ?? 0,
  );
  return PhotoPage(dataSource: dataSource);
}

CustomTransitionPage<void> projectInstitutionRequestRoutePageBuilder(
  BuildContext context,
  GoRouterState state,
) {
  final params = ProjectInstitutionRequestRouteParams.fromState(state);
  final requestStream = GetIt.instance<InstitutionRequestRepository>()
      .institutionRequest(params.requestId);
  return doneModalRouter(LiveRequestStatusModal(requestStream: requestStream));
}

Widget projectReviewPhotoRouteBuilder(
  BuildContext context,
  GoRouterState state,
) {
  final params = ProjectReviewPhotoRouteParams.fromState(state);
  final dataSource = UrlsPhotoDataSource(
    paths: state.extra! as List<String>,
    initialIndex: params.initialIndex ?? 0,
  );
  return PhotoPage(dataSource: dataSource);
}

Widget chatPhotoRouteBuilder(BuildContext context, GoRouterState state) {
  final dataSource = ChatPhotoDataSource(
    projectId: state.pathParameters['projectId']!,
    initialPath: state.stringQueryParam('initialPath')!,
  );
  return PhotoPage(dataSource: dataSource);
}

Widget videoPlayerRouteBuilder(BuildContext context, GoRouterState state) {
  final params = VideoPlayerRouteParams.fromState(state);
  return FutureBuilder<String>(
    future: getDownloadUrl(params.videoRef),
    builder: (context, snapshot) {
      if (!snapshot.hasData) return const LoadingPage();
      return VideoPlayerPage(
        videoRef: snapshot.data!,
        aspectRatio: params.aspectRatio ?? 1,
      );
    },
  );
}

Widget viewChatPdfRouteBuilder(BuildContext context, GoRouterState state) {
  final params = ViewChatPdfRouteParams.fromState(state);
  return ViewPdfPage(url: params.url);
}

Widget viewProjectPdfRouteBuilder(BuildContext context, GoRouterState state) {
  final params = ViewProjectPdfRouteParams.fromState(state);
  return ViewPdfPage(url: params.url);
}

CustomTransitionPage<void> viewProjectPriceListRoutePageBuilder(
  BuildContext context,
  GoRouterState state,
) {
  final params = ViewProjectPriceListRouteParams.fromState(state);
  final articleGroupRef =
      FirebaseFirestore.instance
          .collection('articleGroups')
          .doc(params.priceListId)
          .withArticleGroupConverter;
  return doneModalRouter(PriceListPage(articleGroupRef: articleGroupRef));
}

CustomTransitionPage<void> viewProjectInstallationInfoRoutePageBuilder(
  BuildContext context,
  GoRouterState state,
) {
  final params = ViewProjectInstallationInfoRouteParams.fromState(state);
  final jobRef =
      FirebaseFirestore.instance
          .collection('jobs')
          .doc(params.projectId)
          .withJobConverter;
  return doneModalRouter(
    InstallationInformationPage(
      jobRef: jobRef,
      installationInfoId: params.installationInfoId,
      jobNotes: state.extra as JobNotes?,
    ),
  );
}

Widget viewInvoicePdfRouteBuilder(BuildContext context, GoRouterState state) {
  final params = ViewInvoicePdfRouteParams.fromState(state);
  return ViewPdfPage(url: params.url);
}

Widget viewQuotePdfRouteBuilder(BuildContext context, GoRouterState state) {
  final params = ViewQuotePdfRouteParams.fromState(state);
  return ViewPdfPage(url: params.url);
}

Widget projectCustomerProfileRouteBuilder(
  BuildContext context,
  GoRouterState state,
) {
  final params = ProjectCustomerProfileRouteParams.fromState(state);
  return CustomerInfoPage(customerId: params.customerId);
}

Widget quoteCustomerProfileRouteBuilder(
  BuildContext context,
  GoRouterState state,
) {
  final params = QuoteCustomerProfileRouteParams.fromState(state);
  return CustomerInfoPage(customerId: params.customerId);
}

Widget invoiceCustomerProfileRouteBuilder(
  BuildContext context,
  GoRouterState state,
) {
  final params = InvoiceCustomerProfileRouteParams.fromState(state);
  final customer = state.extra as User?;
  return CustomerInfoPage(customer: customer, customerId: params.customerId);
}

Widget projectCompanyProfileRouteBuilder(
  BuildContext context,
  GoRouterState state,
) {
  final params = ProjectCompanyProfileRouteParams.fromState(state);
  final companyRef =
      FirebaseFirestore.instance
          .collection('companies')
          .doc(params.companyId)
          .withCompanyConverter;

  return CompanyProfilePage(companyRef: companyRef);
}

Widget quoteCompanyProfileRouteBuilder(
  BuildContext context,
  GoRouterState state,
) {
  final params = QuoteCompanyProfileRouteParams.fromState(state);
  final companyRef =
      FirebaseFirestore.instance
          .collection('companies')
          .doc(params.companyId)
          .withCompanyConverter;

  return CompanyProfilePage(companyRef: companyRef);
}

Widget invoiceCompanyProfileRouteBuilder(
  BuildContext context,
  GoRouterState state,
) {
  final params = InvoiceCompanyProfileRouteParams.fromState(state);
  final companyRef =
      FirebaseFirestore.instance
          .collection('companies')
          .doc(params.companyId)
          .withCompanyConverter;
  final company = state.extra as Company?;
  return CompanyProfilePage(company: company, companyRef: companyRef);
}

Widget quoteDetailsRouteBuilder(BuildContext context, GoRouterState state) {
  final params = QuoteDetailsRouteParams.fromState(state);
  final quoteReference =
      FirebaseFirestore.instance
          .collection('jobs')
          .doc(params.projectId)
          .collection('quotes')
          .doc(params.quoteId)
          .withQuoteConverter;

  return QuoteDetailRouter(quoteReference: quoteReference);
}

Widget invoiceDetailsRouteBuilder(BuildContext context, GoRouterState state) {
  final params = InvoiceDetailsRouteParams.fromState(state);
  final invoiceReference =
      FirebaseFirestore.instance
          .collection('invoices')
          .doc(params.invoiceId)
          .withInvoiceConverter;

  return InvoiceDetailsRouter(
    invoiceReference: invoiceReference,
    showGoToProjectAction: params.showGoToProjectAction,
  );
}

Page<void> companyReviewRoutePageBuilder(
  BuildContext context,
  GoRouterState state,
) {
  final params = CompanyReviewRouteParams.fromState(state);
  return kIsWeb || !params.withCamera
      ? doneModalRouter(
        CompanyReviewSheet(
          jobId: params.projectId,
          companyId: params.companyId,
        ),
      )
      : MaterialPage(
        child: CameraPage(jobId: params.projectId, companyId: params.companyId),
      );
}

Widget companyReviewPhotoRouteBuilder(
  BuildContext context,
  GoRouterState state,
) {
  final params = CompanyReviewPhotoRouteParams.fromState(state);
  final dataSource = LocalPhotoDataSource(params.path);
  return PhotoPage(dataSource: dataSource);
}

CustomTransitionPage<void> newJobReportRoutePageBuilder(
  BuildContext context,
  GoRouterState state,
) {
  final params = NewJobReportRouteParams.fromState(state);

  return doneModalRouter(
    JobReportForm(
      reportFormPath: params.reportFormPath,
      jobId: params.projectId,
      type: params.type,
    ),
  );
}

CustomTransitionPage<void> jobReportRoutePageBuilder(
  BuildContext context,
  GoRouterState state,
) {
  final params = JobReportRouteParams.fromState(state);
  return doneModalRouter(
    JobReportForm(
      reportFormPath: params.reportFormPath,
      reportId: params.reportId,
      jobId: params.projectId,
    ),
  );
}

CustomTransitionPage<void> jobReportStatusRoutePageBuilder(
  BuildContext context,
  GoRouterState state,
) {
  final params = JobReportStatusRouteParams.fromState(state);
  final requestStream = GetIt.instance<JobReportsRepository>().jobReport(
    params.projectId,
    params.reportId,
  );
  return doneModalRouter(LiveRequestStatusModal(requestStream: requestStream));
}

Widget viewReportPdfRouteBuilder(BuildContext context, GoRouterState state) {
  final params = ViewReportPdfRouteParams.fromState(state);
  return ViewPdfPage(url: params.url);
}
// routes

final class ProjectsRoute extends NoParamsTypedRoute {
  const ProjectsRoute();

  @override
  String get name => ProjectRoutes.projectsRoute.name;
}

final class AllProjectsRoute extends ExtraTypedRoute<List<Job>> {
  AllProjectsRoute({required super.extra});

  @override
  String get name => ProjectRoutes.allProjectsRoute.name;
}

final class ProjectsSectionRoute
    extends TypedRoute<ProjectsSectionRouteParams, List<Job>?> {
  ProjectsSectionRoute({required super.params, super.extra});

  @override
  String get name => ProjectRoutes.projectsSectionRoute.name;
}

final class ProjectDetailsRoute
    extends ParamsTypedRoute<ProjectDetailsRouteParams> {
  const ProjectDetailsRoute({required super.params});

  @override
  String get name => ProjectRoutes.projectDetailsRoute.name;
}

final class ChatPageRoute extends ParamsTypedRoute<ChatPageRouteParams> {
  const ChatPageRoute({required super.params});

  @override
  String get name => ProjectRoutes.chatPageRoute.name;
}

final class ProjectPhotoRoute
    extends TypedRoute<ProjectPhotoRouteParams, List<ImageUrls>?> {
  ProjectPhotoRoute({required super.params, super.extra});

  @override
  String get name => ProjectRoutes.projectPhotoRoute.name;
}

final class ProjectInstitutionRequestRoute
    extends ParamsTypedRoute<ProjectInstitutionRequestRouteParams> {
  const ProjectInstitutionRequestRoute({required super.params});

  @override
  String get name => ProjectRoutes.projectInstitutionRequestRoute.name;
}

final class ProjectReviewPhotoRoute
    extends TypedRoute<ProjectReviewPhotoRouteParams, List<String>?> {
  ProjectReviewPhotoRoute({required super.params, super.extra});

  @override
  String get name => ProjectRoutes.projectReviewPhotoRoute.name;
}

final class VideoPlayerRoute extends ParamsTypedRoute<VideoPlayerRouteParams> {
  const VideoPlayerRoute({required super.params});

  @override
  String get name => ProjectRoutes.videoPlayerRoute.name;
}

final class ViewChatPdfRoute extends ParamsTypedRoute<ViewChatPdfRouteParams> {
  const ViewChatPdfRoute({required super.params});

  @override
  String get name => ProjectRoutes.viewChatPdfRoute.name;
}

final class ViewProjectPdfRoute
    extends ParamsTypedRoute<ViewProjectPdfRouteParams> {
  const ViewProjectPdfRoute({required super.params});

  @override
  String get name => ProjectRoutes.viewProjectPdfRoute.name;
}

final class ViewProjectPriceListRoute
    extends ParamsTypedRoute<ViewProjectPriceListRouteParams> {
  const ViewProjectPriceListRoute({required super.params});

  @override
  String get name => ProjectRoutes.viewProjectPriceListRoute.name;
}

final class ViewProjectInstallationInfoRoute
    extends TypedRoute<ViewProjectInstallationInfoRouteParams, JobNotes?> {
  ViewProjectInstallationInfoRoute({required super.params, super.extra});

  @override
  String get name => ProjectRoutes.viewProjectInstallationInfoRoute.name;
}

final class ViewInvoicePdfRoute
    extends ParamsTypedRoute<ViewInvoicePdfRouteParams> {
  const ViewInvoicePdfRoute({required super.params});

  @override
  String get name => ProjectRoutes.viewInvoicePdfRoute.name;
}

final class ViewQuotePdfRoute
    extends ParamsTypedRoute<ViewQuotePdfRouteParams> {
  const ViewQuotePdfRoute({required super.params});

  @override
  String get name => ProjectRoutes.viewQuotePdfRoute.name;
}

final class ProjectCustomerProfileRoute
    extends ParamsTypedRoute<ProjectCustomerProfileRouteParams> {
  const ProjectCustomerProfileRoute({required super.params});

  @override
  String get name => ProjectRoutes.projectCustomerProfileRoute.name;
}

final class QuoteCustomerProfileRoute
    extends ParamsTypedRoute<QuoteCustomerProfileRouteParams> {
  const QuoteCustomerProfileRoute({required super.params});

  @override
  String get name => ProjectRoutes.quoteCustomerProfileRoute.name;
}

final class InvoiceCustomerProfileRoute
    extends TypedRoute<InvoiceCustomerProfileRouteParams, User?> {
  InvoiceCustomerProfileRoute({required super.params, super.extra});

  @override
  String get name => ProjectRoutes.invoiceCustomerProfileRoute.name;
}

final class ProjectCompanyProfileRoute
    extends ParamsTypedRoute<ProjectCompanyProfileRouteParams> {
  const ProjectCompanyProfileRoute({required super.params});

  @override
  String get name => ProjectRoutes.projectCompanyProfileRoute.name;
}

final class QuoteCompanyProfileRoute
    extends ParamsTypedRoute<QuoteCompanyProfileRouteParams> {
  const QuoteCompanyProfileRoute({required super.params});

  @override
  String get name => ProjectRoutes.quoteCompanyProfileRoute.name;
}

final class InvoiceCompanyProfileRoute
    extends TypedRoute<InvoiceCompanyProfileRouteParams, Company?> {
  InvoiceCompanyProfileRoute({required super.params, super.extra});

  @override
  String get name => ProjectRoutes.invoiceCompanyProfileRoute.name;
}

final class QuoteDetailsRoute
    extends ParamsTypedRoute<QuoteDetailsRouteParams> {
  const QuoteDetailsRoute({required super.params});

  @override
  String get name => ProjectRoutes.quoteDetailsRoute.name;
}

final class InvoiceDetailsRoute
    extends ParamsTypedRoute<InvoiceDetailsRouteParams> {
  const InvoiceDetailsRoute({required super.params});

  @override
  String get name => ProjectRoutes.invoiceDetailsRoute.name;
}

final class CompanyReviewRoute
    extends ParamsTypedRoute<CompanyReviewRouteParams> {
  const CompanyReviewRoute({required super.params});

  @override
  String get name => ProjectRoutes.companyReviewRoute.name;
}

final class CompanyReviewPhotoRoute
    extends ParamsTypedRoute<CompanyReviewPhotoRouteParams> {
  const CompanyReviewPhotoRoute({required super.params});

  @override
  String get name => ProjectRoutes.companyReviewPhotoRoute.name;
}

final class NewJobReportRoute
    extends ParamsTypedRoute<NewJobReportRouteParams> {
  const NewJobReportRoute({required super.params});

  @override
  String get name => ProjectRoutes.newJobReportRoute.name;
}

final class JobReportRoute extends ParamsTypedRoute<JobReportRouteParams> {
  const JobReportRoute({required super.params});

  @override
  String get name => ProjectRoutes.jobReportRoute.name;
}

final class JobReportStatusRoute
    extends ParamsTypedRoute<JobReportStatusRouteParams> {
  const JobReportStatusRoute({required super.params});

  @override
  String get name => ProjectRoutes.jobReportStatusRoute.name;
}

final class ViewReportPdfRoute
    extends ParamsTypedRoute<ViewReportPdfRouteParams> {
  const ViewReportPdfRoute({required super.params});

  @override
  String get name => ProjectRoutes.viewReportPdfRoute.name;
}
