import 'dart:async';

import 'package:done_router/done_router.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:app_links/app_links.dart';

class DeepLinkingHandler extends StatefulWidget {
  const DeepLinkingHandler({
    required this.child,
    this.blackListedDomains = const [],
    super.key,
  });

  /// Domains that we don't want to be handled automatically through deep linking, mainly we should
  /// put firebase dynamic links domains here.
  final List<String> blackListedDomains;
  final Widget child;

  @override
  State<DeepLinkingHandler> createState() => _DeepLinkingHandlerState();
}

class _DeepLinkingHandlerState extends State<DeepLinkingHandler> {
  StreamSubscription<Uri?>? _deepLinkSubscription;
  AppLinks? _appLinks;

  @override
  void initState() {
    super.initState();
    // No need to handle deep-linking on web, it's being done automatically by router, Also:
    // As the Web URL cannot be changed without restarting the application, link
    // streams are unimplemented on this platform.
    if (!kIsWeb) {
      _initDeepLinks();
    }
  }

  /// Initializes handling of deep links.
  /// Any deep link that is received while the app was cold will be handled.
  Future<void> _initDeepLinks() async {
    _appLinks = AppLinks();

    final initialLink = await _appLinks?.getInitialLink();
    if (initialLink != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        await _handleDeepLink(initialLink);
      });
    }

    _deepLinkSubscription = _appLinks?.uriLinkStream.listen(_handleDeepLink);
  }

  /// Handles the deep link and navigates to the appropriate screen.
  Future<void> _handleDeepLink(Uri uri) async {
    if (widget.blackListedDomains.contains(uri.host)) return;
    final router = GetIt.instance<DoneRouter>().router;
    return URLRouter.instance.open(uri, router: router);
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }

  @override
  void dispose() {
    _deepLinkSubscription?.cancel();
    super.dispose();
  }
}
