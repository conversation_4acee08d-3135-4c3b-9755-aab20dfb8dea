import 'dart:math' as math;

import 'package:done_image/done_image.dart';
import 'package:done_router/done_router.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:done_localizations/done_localizations.dart';

class RoutingErrorPage extends StatelessWidget {
  const RoutingErrorPage({super.key});

  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final maxWidth = math.min(width - 50, 500);
    return Scaffold(
      body: SafeArea(
        child: Center(
          child: ConstrainedBox(
            constraints: BoxConstraints(maxWidth: maxWidth.toDouble()),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                Image.asset(ImageAssets.brokenPage, scale: 1.5),
                const VerticalMargin.large(),
                Text(
                  S.of(context).navigationErrorTitle,
                  style: Theme.of(context).textTheme.headlineSmall,
                  textAlign: TextAlign.center,
                ),
                const VerticalMargin.medium(),
                Text(
                  S.of(context).navigationErrorDescription,
                  style: Theme.of(context).textTheme.bodyLarge,
                  textAlign: TextAlign.center,
                ),
                const VerticalMargin.medium(),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 64),
                  child: DoneButton(
                    style: DoneButtonStyle.secondary,
                    title: Text(S.of(context).goToHome),
                    onPressed: () => GoRouter.of(context).go('/'),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
