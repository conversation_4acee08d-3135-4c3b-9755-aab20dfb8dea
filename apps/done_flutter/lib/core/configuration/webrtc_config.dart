/// This class includes setup constants for webrtc
class DoneWebrtcConfig {
  static final iceServers = {
    'sdpSemantics': 'unified-plan',
    'iceServers': [
      {
        'urls': 'stun:turn.doneservices.se',
        'username': 'done',
        'credential': 'A145896A94104B52BB78B86A6118A1DD',
      },
      {
        'urls': 'turn:turn.doneservices.se',
        'username': 'done',
        'credential': 'A145896A94104B52BB78B86A6118A1DD',
      },
      {'urls': 'stun:stun.l.google.com:19302'},
    ],
  };

  static final peerConnectionConstraints = {
    'mandatory': <String, dynamic>{},
    'optional': [
      {'DtlsSrtpKeyAgreement': true},
    ],
  };

  static final sessionConstraints = {
    'mandatory': {'OfferToReceiveAudio': true, 'OfferToReceiveVideo': true},
    'optional': <dynamic>[],
  };

  static final mediaConstraints = {
    'audio': true,
    'video': {
      'mandatory': {
        'minWidth': '960',
        'minHeight': '720',
        'minFrameRate': '30',
      },
      'facingMode': 'user',
      'optional': <String>[],
    },
  };
}
