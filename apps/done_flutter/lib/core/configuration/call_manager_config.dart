import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:done_analytics/done_analytics.dart';
import 'package:done_calls/done_calls.dart';
import 'package:done_flutter/core/services/remote_config_service.dart';
import 'package:done_flutter/ui/pages/call/outgoing_call.dart';
import 'package:done_flutter/ui/pages/call/unsupported_call_dialog.dart';
import 'package:done_models/done_models.dart';
import 'package:done_router/done_router.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:overlay_support/overlay_support.dart';

import 'package:done_flutter/ui/pages/call/incoming_call_dialog.dart';
import 'package:done_flutter/ui/pages/call/video.dart';
import 'package:done_flutter/ui/widgets/call_disconnected_notification.dart';
import 'package:done_flutter/ui/widgets/dialogs/missing_call_permissions_dialog.dart';

class CallManagerConfig {
  static VideoCallService get callService {
    final configuredServiceRaw =
        GetIt.instance<RemoteConfigService>().videoCallService;
    return videoCallServiceFrom(configuredServiceRaw);
  }

  static Widget incomingCallBuilder(
    OnAnswer onAnswer,
    CallKeepCallData request,
  ) {
    return IncomingCallDialog(onAnswer: onAnswer, request: request);
  }

  static void onCallDisconnected() {
    showOverlayNotification(
      (context) => const CallDisconnectedNotification(),
      duration: const Duration(seconds: 4),
    );
  }

  static void onCallPermissionsDenied() {
    final navigatorKey =
        GetIt.instance<DoneRouter>().router.routerDelegate.navigatorKey;

    // Return false and show a prompt dialog to open app settings if they are still not granted
    showDialog<void>(
      // Using navigatorKey.currentContext as context passed from [CallManager] can sometimes not contain Navigator.
      context: navigatorKey.currentContext!,
      builder: (context) => const MissingCallPermissionsDialog(),
    );
  }

  static Widget ongoingCallBuilder(
    VideoCall? call,
    bool isMinimized,
    void Function() onMinimize,
    void Function() onMinimizedCallTap,
  ) {
    return VideoPage(
      call: call!,
      isMinimized: isMinimized,
      onMinimize: () {
        EventLogger.instance.logEvent('minimizeCall');
        onMinimize();
      },
      onMinimizedCallTap: () {
        EventLogger.instance.logEvent('maximizeCall');
        onMinimizedCallTap();
      },
    );
  }

  static Widget outgoingCallBuilder(
    VideoCall? call,
    DocumentReference<User> remoteUser,
    bool isMinimized,
    void Function() onMinimize,
    void Function() onMinimizedCallTap,
  ) {
    return OutgoingCall(
      call: call,
      remoteUser: call?.remoteUser ?? remoteUser,
      isMinimized: isMinimized,
      onMinimize: () {
        EventLogger.instance.logEvent('minimizeCall');
        onMinimize();
      },
      onMinimizedCallTap: () {
        EventLogger.instance.logEvent('maximizeCall');
        onMinimizedCallTap();
      },
    );
  }

  static Widget unsupportedCallBuilder(
    VoidCallback clearCall,
    AsyncCallback declineCall,
  ) {
    return UnsupportedCallDialog(
      clearCall: clearCall,
      declineCall: declineCall,
    );
  }
}
