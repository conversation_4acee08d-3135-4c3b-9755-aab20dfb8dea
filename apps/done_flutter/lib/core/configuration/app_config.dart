import 'package:done_flutter/core/configuration/intercom_config.dart';
import 'package:done_flutter/core/configuration/meili_search_config.dart';
import 'package:done_flutter/core/configuration/third_party_app_keys.dart';
import 'package:done_flutter/generated/firebase_options/firebase_options_dev.dart';
import 'package:done_flutter/generated/firebase_options/firebase_options_prod.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';

/// Contains the current build flavor.
///
/// An inherited widget used to retrieve the current build flavor and
/// it's associated configuration values.
class AppConfig extends InheritedWidget {
  const AppConfig({required this.flavorType, required super.child});

  final FlavorType flavorType;

  static AppConfig of(BuildContext context) {
    try {
      return context.dependOnInheritedWidgetOfExactType<AppConfig>()!;
    } catch (e) {
      throw Exception('AppConfig not found in widget tree');
    }
  }

  @override
  bool updateShouldNotify(InheritedWidget oldWidget) => false;
}

enum FlavorType { dev, prod, local }

extension FlavorTypeValues on FlavorType {
  /// Returns `true` if the app should show a debug banner for this flavor.
  bool get displayDebugBanner {
    switch (this) {
      case FlavorType.dev:
      case FlavorType.local:
        return true;
      case FlavorType.prod:
        return false;
    }
  }

  /// Returns the `vapidKey` required for firebase messaging on web depending on current flavor
  /// Web setup documentation https://firebase.google.com/docs/cloud-messaging/flutter/client#web
  String get vapidKey {
    switch (this) {
      case FlavorType.dev:
      case FlavorType.local:
        return ThirdPartyAppKeys.vapidKeyDev;
      case FlavorType.prod:
        return ThirdPartyAppKeys.vapidKeyProd;
    }
  }

  /// Returns the `clientId` required for OAuth google sign in on web depending on current flavor
  /// It can be accessed/setup here https://console.cloud.google.com/apis/credentials
  String get googleWebClientId {
    switch (this) {
      case FlavorType.dev:
      case FlavorType.local:
        return '704360355803-r01q6so69hiv7j3j68itj1oadg85ttm1.apps.googleusercontent.com';
      case FlavorType.prod:
        return '865533511519-iirdlsb551kkq7gkmkse30buuuc14g49.apps.googleusercontent.com';
    }
  }

  /// Returns the `appId` required for Agora call services on current flavor
  String get agoraAppId {
    switch (this) {
      case FlavorType.dev:
      case FlavorType.local:
        return '7532eea501b0446a981ea1d56809df69';
      case FlavorType.prod:
        return 'b930de55f0b34f70a9d1840e14c6b3ba';
    }
  }

  FirebaseOptions? get firebaseOptions =>
      this == FlavorType.prod
          ? ProdFirebaseOptions.currentPlatform
          : DevFirebaseOptions.currentPlatform;

  MeiliSearchConfig get meiliSearchConfig =>
      this == FlavorType.prod
          ? MeiliSearchConfigProd()
          : MeiliSearchConfigDev();

  IntercomConfig get intercomConfig =>
      this == FlavorType.prod
          ? const IntercomConfig.defaultIntercomConfig()
          : const IntercomConfig.testIntercomConfig();
}
