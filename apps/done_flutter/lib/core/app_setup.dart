import 'dart:ui';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:done_analytics/done_analytics.dart';
import 'package:done_auth/done_auth.dart';
import 'package:done_booking/done_booking.dart';
import 'package:done_calls/done_calls.dart';
import 'package:done_database/done_database.dart';
import 'package:done_flutter/core/configuration/app_config.dart';
import 'package:done_flutter/core/lib/calls_config.dart';
import 'package:done_flutter/core/lib/logger_config.dart';
import 'package:done_flutter/core/router/routes/routes.dart';
import 'package:done_flutter/core/router/routing_error_page.dart';
import 'package:done_flutter/core/router/utils/router_redirect.dart';
import 'package:done_flutter/core/services/firebase_storage_service.dart';
import 'package:done_flutter/core/services/intercom_service.dart'
    show IntercomHelper;
import 'package:done_flutter/core/services/intercom_service.dart'
    show IntercomRouteObserver;
import 'package:done_flutter/core/services/meili_search_service.dart';
import 'package:done_flutter/core/services/messaging_service.dart'
    show Messaging;
import 'package:done_flutter/core/services/remote_config_service.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_models/done_models.dart';
import 'package:done_router/done_router.dart';
import 'package:done_ui/done_ui.dart';
import 'package:firebase_auth/firebase_auth.dart' hide User;
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_dynamic_links/firebase_dynamic_links.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:flutter_web_plugins/url_strategy.dart';
import 'package:get_it/get_it.dart';
import 'package:logger/logger.dart';
import 'package:package_info_plus/package_info_plus.dart' show PackageInfo;
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:universal_platform/universal_platform.dart';

import 'package:firebase_app_check/firebase_app_check.dart';

/// This class is responsible for initializing 3rd party libraries.
/// Firebase AUTH
/// Firebase Messaging
/// CallKit
/// Firebase Analytics
/// Logger
///
/// **Note: We have to set the [flavor] in the main method, otherwise configuration can be wrong**
///
/// Usage inside main method:
///
/// ```dart
/// AppSetup.flavor = FlavorType.dev;
/// AppSetup.start();
/// ```
class AppSetup {
  AppSetup._internal(this._packageInfo);

  static bool isRunningIntegrationTest = false;

  static FlavorType flavor = FlavorType.values.byName(appFlavor!);

  final PackageInfo _packageInfo;

  // Used in backgroundMessageHandler to avoid initializing if run from the main isolate and to avoid background isolates to run multiple times.
  static bool hasBeenRun = false;

  static Future<AppSetup> start({
    bool initializeBackgroundMessaging = true,
  }) async {
    hasBeenRun = true;
    // Needs to be initialized to get path for logger
    WidgetsFlutterBinding.ensureInitialized();
    final logger = await doneLogger();

    usePathUrlStrategy();

    logger.d('[appSetup] Initializing Firebase');
    await Firebase.initializeApp(options: flavor.firebaseOptions);
    logger.d('[appSetup] Firebase initialized');

    if (kDebugMode) {
      await FirebaseAppCheck.instance.activate(
        androidProvider: AndroidProvider.debug,
        appleProvider: AppleProvider.debug,
        webProvider: ReCaptchaEnterpriseProvider(
          '6LchCjErAAAAAMvHIGOK9c085DD6iq85Gym2udwh',
        ),
      );
    } else {
      await FirebaseAppCheck.instance.activate(
        // ignore: avoid_redundant_argument_values
        androidProvider: AndroidProvider.playIntegrity,
        appleProvider: AppleProvider.appAttest,
        webProvider: ReCaptchaEnterpriseProvider(
          '6LfS4jArAAAAANM1yEaAdav5_LgqWCe6r9ByDqK9',
        ),
      );
    }
    logger.d('[appSetup] AppCheck initialized');

    FirebaseFirestore.instance.settings = const Settings(
      persistenceEnabled: true,
    );
    logger.d('[appSetup] Firestore initialized');

    setupDoneCalls(flavor);
    Messaging.setup(
      initializeBackgroundMessaging: initializeBackgroundMessaging,
    );
    if (UniversalPlatform.isIOS) MessagingPermissionsHandler.setupApns();

    await _registerSingletons(logger: logger);

    GetIt.instance<AuthCubit>().stream.listen((state) {
      if (state.hasValidUserDocument) {
        DeviceTokenManager.instance.setDeviceTokenOnUser(
          state.user!.documentReference,
          onTokenUpdated:
              (token) => GetIt.instance<IntercomHelper>().setFCMToken(token),
        );
      }
    });
    // Setup timeago Swedish messages as they aren't available out of the box
    TimeFormatter.setupTimeAgo(SupportedLocales.swedish);

    final packageInfo = await PackageInfo.fromPlatform();
    if (!kIsWeb) {
      await SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
      ]);
      await _initDynamicLinks();
    }

    LabeledValue.onCopySnackbarMessage = (BuildContext context, String label) {
      EventLogger.instance.logEvent('copiedValue', {'label': label});
      return S.of(context).copied(label);
    };

    return AppSetup._internal(packageInfo);
  }

  /// Needs to differ from regular `start` to avoid touching UI code.
  /// See https://firebase.flutter.dev/docs/messaging/usage/#background-messages
  static Future<void> startAndroidBackgroundMessageIsolate() async {
    hasBeenRun = true;

    // Needs to be initialized to get file path for logger
    WidgetsFlutterBinding.ensureInitialized();
    DartPluginRegistrant.ensureInitialized();

    final logger = await doneLogger();
    logger.d('[appSetup] Widgets binding initialized');
    GetIt.instance.registerSingleton<Logger>(logger);

    await Firebase.initializeApp(options: flavor.firebaseOptions);
    logger.d('[appSetup] Firebase initialized');

    setupDoneCalls(flavor);
  }

  static Future<void> _registerSingletons({required Logger logger}) async {
    // TODO: for tests later on - make AppSetup aware when it's running in test mode
    // based on that, swap between FirestoreDB and MockFirestoreDB

    final getIt = GetIt.instance;
    final authCubit = AuthCubit();
    final doneRouter = DoneRouter(
      appRoutes,
      authCubit,
      observers: navigationObservers,
      redirect: routerAuthRedirect,
      errorBuilder: (context, state) {
        ErrorHandler.reportError(
          Exception('Failed to route to a broken link: ${state.uri}'),
          StackTrace.current,
        );
        return const RoutingErrorPage();
      },
    )..initialize();

    getIt
      ..registerSingleton<Logger>(logger)
      ..registerSingleton<Connectivity>(Connectivity())
      ..registerSingleton<DoneRouter>(doneRouter)
      ..registerSingleton<MeiliSearchService>(
        MeiliSearchService(flavor.meiliSearchConfig),
      )
      ..registerSingleton<AuthCubit>(authCubit)
      ..registerSingleton<FirebaseStorageService>(FirebaseStorageService())
      ..registerSingleton<BookingRemoteConfigService>(
        BookingRemoteConfigService(),
      )
      ..registerSingleton<AnalyticsRemoteConfigService>(
        AnalyticsRemoteConfigService(),
      )
      ..registerSingletonAsync<IntercomHelper>(() async {
        await IntercomHelper.initialize(flavor.intercomConfig);
        return IntercomHelper(authCubit, logger);
      })
      ..registerSingletonAsync<RemoteConfigService>(() async {
        final remoteConfigService = RemoteConfigService();
        await remoteConfigService.initialize();
        return remoteConfigService;
      });
    _registerRepositories(getIt);
    await getIt.allReady();
    GetIt.instance.get<Logger>().d('[appSetup] singletons registered');
  }

  // FIXME: This is a temporary function to register our repositories as singletons until we refactor them into viewmodels, maybe?
  static void _registerRepositories(GetIt getIt) {
    getIt
      ..registerSingleton<JobsRepository>(FirestoreJobsRepository())
      ..registerSingleton<UserRepository>(FirestoreUserRepository())
      ..registerSingleton<QuotesRepository>(FirestoreQuotesRepository())
      ..registerSingleton<BookingRepository>(FirestoreBookingRepository())
      ..registerSingleton<CompanyRepository>(FirestoreCompanyRepository())
      ..registerSingleton<InvoicesRepository>(FirestoreInvoiceRepository())
      ..registerSingleton<RawQuotesRepository>(FirestoreRawQuotesRepository())
      ..registerSingleton<JobOffersRepository>(FirestoreJobOffersRepository())
      ..registerSingleton<JobReportsRepository>(FirestoreJobReportsRepository())
      ..registerSingleton<ConversationsRepository>(
        FirestoreConversationsRepository(),
      )
      ..registerSingleton<FixedPriceJobsRepository>(
        FirestoreFixedPriceJobsRepository(),
      )
      ..registerSingleton<InstitutionRequestRepository>(
        FirestoreInstitutionRequestRepository(),
      )
      ..registerSingleton<FinancialGeneratorRepository>(
        FirestoreFinancialGeneratorRepository(),
      )
      ..registerSingleton<PartnershipRepository>(
        FirestorePartnershipRepository(),
      );
  }

  /// Initializes dynamic links
  /// FirebaseDynamicLinks is deprecated but currently needed for email link login and there is no alternative.
  /// Also is is used when captchas are showing.
  /// TODO: Replace Firebase Dynamic Links with a new solution
  static Future<void> _initDynamicLinks() async {
    final router = GetIt.instance<DoneRouter>().router;
    final urlRouter = URLRouter.instance;

    // Attach a listener to the stream
    // ignore: deprecated_member_use
    FirebaseDynamicLinks.instance.onLink.listen(
      (link) async {
        final url = link.link;
        final logger =
            GetIt.instance.get<Logger>()
              ..d('Background dynamic link opened: $url');
        final shouldHandleLinkAsEmailLink = await _shouldHandleLinkAsEmailLink(
          url,
          logger,
        );
        if (shouldHandleLinkAsEmailLink)
          return _handleEmailLinkLogin(url, logger);
        await urlRouter.open(url, router: router);
      },
      onError: (Object error) async {
        GetIt.instance.get<Logger>().e(
          'Error with dynamic link stream',
          error: error,
        );
      },
    );

    // Handle iniital link if it exists
    try {
      // ignore: deprecated_member_use
      final initialLink = await FirebaseDynamicLinks.instance.getInitialLink();
      GetIt.instance.get<Logger>().d('Initial link: ${initialLink?.link}');
      if (initialLink != null) {
        final url = initialLink.link;
        final logger = GetIt.instance.get<Logger>();
        final shouldHandleLinkAsEmailLink = await _shouldHandleLinkAsEmailLink(
          url,
          logger,
        );
        if (shouldHandleLinkAsEmailLink)
          return _handleEmailLinkLogin(url, logger);
        SchedulerBinding.instance.addPostFrameCallback(
          (_) => urlRouter.open(url, router: router),
        );
      }
    } on FormatException {
      GetIt.instance.get<Logger>().e('error with initial link');
    } on MissingPluginException {
      GetIt.instance.get<Logger>().e(
        'Missing plugin on getInitialLink. Probably because started in the background',
      );
    }
  }

  static Future<bool> _shouldHandleLinkAsEmailLink(
    Uri url,
    Logger logger,
  ) async {
    final emailLink = url.toString();
    final isEmailLink = FirebaseAuth.instance.isSignInWithEmailLink(emailLink);
    if (!isEmailLink) return false;
    final prefs = await SharedPreferences.getInstance();
    final email = prefs.getString(magicLinkPreferencesKey);
    if (email == null) {
      await ErrorHandler.reportError(
        'Tried to login with email link but email was null',
        StackTrace.current,
      );
    }
    return email != null;
  }

  /// Handles the email link login
  /// It's called from dynamic link's init and listener callbacks
  static Future<void> _handleEmailLinkLogin(Uri url, Logger logger) async {
    final emailLink = url.toString();
    try {
      // The client SDK will parse the code from the link for you.
      final prefs = await SharedPreferences.getInstance();
      final email = prefs.getString(magicLinkPreferencesKey);
      if (email == null) return;
      await FirebaseAuth.instance.signInWithEmailLink(
        email: email,
        emailLink: emailLink,
      );
      logger.d('Successfully signed in with email link!');
    } catch (error, stackTrace) {
      logger.e(
        'Error signing in with email link.',
        error: error,
        stackTrace: stackTrace,
      );
    }
  }

  /// Configures [Sentry]'s scope with the [SentryUser] used in error reports
  ///
  /// It sets it to `null` if [userId] is `null` otherwise, it uses our user's ID
  static Future<void> configureSentryUser(String? userId) async {
    final sentryUser = userId != null ? SentryUser(id: userId) : null;
    return Sentry.configureScope((scope) => scope.setUser(sentryUser));
  }

  PackageInfo getPackageInfo() {
    return _packageInfo;
  }

  static List<NavigatorObserver> get navigationObservers => [
    firebaseAnalyticsObserver,
    SentryNavigatorObserver(),
    IntercomRouteObserver(),
  ];
}
