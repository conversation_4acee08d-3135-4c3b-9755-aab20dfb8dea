import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';

Future<Logger> doneLogger() async {
  return Logger(
    printer: Pretty<PERSON><PERSON><PERSON>(noBoxingByDefault: true),
    output: MultiOutput([
      ConsoleOutput(),
      if (!kIsWeb)
        AdvancedFileOutput(
          path: (await getApplicationSupportDirectory()).path,
          maxRotatedFilesCount: 5,
        ),
    ]),
    // filter: ProductionFilter(), // Remove comment to enable logs in release mode
  );
}

Future<void> shareLogFiles() async {
  final appSupport = await getApplicationSupportDirectory();
  final logDir = Directory(appSupport.path);
  final logFiles =
      logDir.listSync().where((file) => file.path.endsWith('.log')).toList();

  final logXFiles = logFiles.map((file) => XFile(file.path)).toList();
  await Share.shareXFiles(logXFiles, subject: 'App Logs');
}
