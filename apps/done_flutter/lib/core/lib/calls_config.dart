import 'package:done_calls/done_calls.dart';
import 'package:done_flutter/core/configuration/app_config.dart';
import 'package:done_flutter/core/configuration/webrtc_config.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:flutter/services.dart';

void setupDoneCalls(FlavorType flavor) {
  DoneCallsConfig.init(
    webrtcConfig: WebrtcConfig(iceServers: DoneWebrtcConfig.iceServers),
    agoraAppId: flavor.agoraAppId,
    apnsChannel: const MethodChannel('co.doneservices.app/apns'),
    callKeepBaseConfig: CallKeepBaseConfig(
      appName: 'Done',
      acceptText: S().callAnswer,
      declineText: S().callDecline,
      contentTitle: (argument) => S().callNotificationTitle(argument),
      androidConfig: CallKeepAndroidConfig(
        logo: 'slash_logo',
        showCallBackAction: false,
        showMissedCallNotification: false,
        notificationIcon: 'ic_notification',
        ringtoneFileName: 'ringtone.mp3',
        accentColor: '#6734C7',
      ),
      iosConfig: CallKeepIosConfig(iconName: 'IconMask', maximumCallGroups: 1),
    ),
  );
}
