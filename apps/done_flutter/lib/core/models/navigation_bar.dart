import 'package:done_auth/done_auth.dart';
import 'package:done_flutter/core/router/routes/project_routes.dart';
import 'package:done_flutter/core/router/routes/routes_definitions.dart';
import 'package:done_flutter/utils/extensions/string_extensions.dart';
import 'package:done_image/done_image.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:done_localizations/done_localizations.dart';

enum CompanyHomeTabs { messages, offers, projects, more }

enum CustomerHomeTabs { explore, projects, chat, profile }

class NavigationBarItem {
  NavigationBarItem({
    required this.iconAsset,
    required this.activeIconAsset,
    required this.pageNumber,
    required this.key,
    required this.label,
    required this.location,
  });

  final String iconAsset;
  final String activeIconAsset;
  final int pageNumber;
  final Key key;
  final String label;
  final String location;
}

List<NavigationBarItem> getNavBarItems(BuildContext context) {
  final isCraftsman = context.authState.isUserCraftsman();
  return isCraftsman
      ? _getCraftsmanNavBarItems(context)
      : _getCustomerNavBarItems(context);
}

List<NavigationBarItem> _getCustomerNavBarItems(BuildContext context) {
  return CustomerHomeTabs.values
      .map((tab) => tab.navigationBarItem(context))
      .toList();
}

List<NavigationBarItem> _getCraftsmanNavBarItems(BuildContext context) {
  return CompanyHomeTabs.values
      .map((tab) => tab.navigationBarItem(context))
      .toList();
}

// TODO: Move to theme.dart
class NavigationBarData {
  NavigationBarData({required this.context});
  final BuildContext context;

  double get elevation => 0;

  DoneSideBarType get type => DoneSideBarType.fixed;

  Color get bottomNavBarBackgroundColor => context.doneColors.uiLightNeutral;

  EdgeInsets get iconEdgeInsets =>
      const EdgeInsets.only(left: 12, right: 12, top: 5, bottom: 2);

  Color get sideNavBarBackgroundColor => Colors.transparent;

  TextStyle get selectedLabelStyle => TextStyle(
    fontFamily: 'Inter',
    fontSize: 11,
    fontWeight: FontWeight.w600,
    color: context.doneColors.uiBlack,
  );

  TextStyle get unselectedLabelStyle => TextStyle(
    fontFamily: 'Inter',
    fontSize: 11,
    fontWeight: FontWeight.w600,
    color: context.doneColors.uiNeutral,
  );

  Color get unselectedItemColor => context.doneColors.uiNeutral;

  Color get selectedItemColor => context.doneColors.uiPurple;
}

extension CustomerNavigationBarItems on CustomerHomeTabs {
  NavigationBarItem navigationBarItem(BuildContext context) {
    switch (this) {
      case CustomerHomeTabs.explore:
        return NavigationBarItem(
          iconAsset: ImageAssets.explore,
          activeIconAsset: ImageAssets.exploreInverted,
          pageNumber: index,
          key: const Key("customerExploreTab"),
          label: S.of(context).explore,
          location: const RootRoute().location(context),
        );
      case CustomerHomeTabs.projects:
        return NavigationBarItem(
          iconAsset: ImageAssets.projects,
          activeIconAsset: ImageAssets.projectsInverted,
          pageNumber: index,
          key: const Key("customerProjectsTab"),
          label: S.of(context).projectsTabTitle,
          location: const ProjectsRoute().location(context),
        );
      case CustomerHomeTabs.chat:
        return NavigationBarItem(
          iconAsset: ImageAssets.chat,
          activeIconAsset: ImageAssets.chatInverted,
          pageNumber: index,
          key: const Key("customerChatTab"),
          label: S.of(context).chats,
          location: const CustomerChatRoute().location(context),
        );
      case CustomerHomeTabs.profile:
        return NavigationBarItem(
          iconAsset: ImageAssets.profile,
          activeIconAsset: ImageAssets.profileInverted,
          pageNumber: index,
          key: const Key("customerProfileTab"),
          label: S.of(context).profile,
          location: const ProfileRoute().location(context),
        );
    }
  }
}

extension CompanyNavigationBarItems on CompanyHomeTabs {
  NavigationBarItem navigationBarItem(BuildContext context) {
    switch (this) {
      case CompanyHomeTabs.messages:
        return NavigationBarItem(
          iconAsset: ImageAssets.chat,
          activeIconAsset: ImageAssets.chatInverted,
          pageNumber: index,
          key: const Key("companyMessagesTab"),
          label: S.of(context).messages,
          location: const RootRoute().location(context),
        );
      case CompanyHomeTabs.offers:
        return NavigationBarItem(
          iconAsset: ImageAssets.request,
          activeIconAsset: ImageAssets.requestInverted,
          pageNumber: index,
          key: const Key("companyjobOffersTap"),
          label: S.of(context).jobOffers,
          location: const JobOffersRoute().location(context),
        );
      case CompanyHomeTabs.projects:
        return NavigationBarItem(
          iconAsset: ImageAssets.projects,
          activeIconAsset: ImageAssets.projectsInverted,
          pageNumber: index,
          key: const Key("companyProjectTab"),
          label: S.of(context).projectsTabTitle,
          location: const ProjectsRoute().location(context),
        );
      case CompanyHomeTabs.more:
        return NavigationBarItem(
          iconAsset: ImageAssets.more,
          activeIconAsset: ImageAssets.moreInverted,
          pageNumber: 3,
          key: const Key("companyProfileTab"),
          label: S.of(context).more.capitalize(),
          location: const MoreRoute().location(context),
        );
    }
  }
}
