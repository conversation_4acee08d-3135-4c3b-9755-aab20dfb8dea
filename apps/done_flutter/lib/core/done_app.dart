import 'package:done_analytics/done_analytics.dart';
import 'package:done_booking/done_booking.dart';
import 'package:done_calls/done_calls.dart';
import 'package:done_flutter/core/app_setup.dart';
import 'package:done_flutter/core/blocs/admin_configuration_cubit/admin_configuration_cubit.dart';
import 'package:done_flutter/core/blocs/master_detail_cubit/home_selection_cubit.dart';
import 'package:done_flutter/core/blocs/master_detail_cubit/home_tabs_badge_cubit.dart';
import 'package:done_flutter/core/blocs/session_configuration_cubit/session_configuration_cubit.dart';
import 'package:done_flutter/core/configuration/app_config.dart';
import 'package:done_flutter/core/configuration/call_manager_config.dart';
import 'package:done_auth/done_auth.dart';
import 'package:done_flutter/core/models/navigation_bar.dart';
import 'package:done_flutter/core/router/deep_linking_handler.dart';
import 'package:done_router/done_router.dart';
import 'package:done_flutter/core/services/app_badger_manager.dart';

import 'package:done_flutter/ui/widgets/check_connectivity_banner.dart';
import 'package:done_flutter/ui/widgets/flavor_banner.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:done_ui/done_ui.dart' show LayoutMargins, darkTheme, lightTheme;

// Localisation
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:overlay_support/overlay_support.dart';

class DoneApp extends StatelessWidget {
  const DoneApp({super.key});

  @override
  Widget build(BuildContext context) {
    final router = GetIt.instance<DoneRouter>().router;
    return BlocProvider.value(
      value: GetIt.instance<AuthCubit>(),
      child: MaterialApp.router(
        title: 'Done',
        theme: lightTheme,
        darkTheme: darkTheme,
        debugShowCheckedModeBanner: false,
        builder: (context, child) {
          final config = AppConfig.of(context);
          return LayoutMargins(
            child: CheckConnectivityBanner(
              child: FlavorBanner(
                flavorType: config.flavorType,
                child: BlocBuilder<AuthCubit, AuthState>(
                  buildWhen: _rebuildAuthWhen,
                  builder: (context, state) {
                    final user = state.user;
                    final userId = user?.documentReference.id;
                    if (state.hasValidUserDocument) {
                      AppSetup.configureSentryUser(userId);
                    }
                    if (!kIsWeb) {
                      if (userId == null)
                        EventLogger.instance.disableFacebookTracking();
                    }
                    if (user == null) return child!;
                    final tabsCount =
                        user.company != null
                            ? CompanyHomeTabs.values.length
                            : CustomerHomeTabs.values.length;

                    return CupertinoTheme(
                      data: CupertinoTheme.of(context).copyWith(
                        // FIX-ME: Check why this is needed
                        brightness: Brightness.light,
                      ),
                      child: OverlaySupport.global(
                        child: Builder(
                          builder:
                              (context) => MultiBlocProvider(
                                providers: [
                                  BlocProvider(
                                    create: (_) => AdminConfigurationCubit(),
                                    lazy: false,
                                  ),
                                  BlocProvider(
                                    create: (_) => SessionConfigurationCubit(),
                                  ),
                                  BlocProvider(
                                    create:
                                        (_) => HomeSelectionCubit(
                                          tabsCount: tabsCount,
                                        ),
                                  ),
                                  BlocProvider(
                                    create:
                                        (_) => HomeTabsBadgeCubit(
                                          tabsCount: tabsCount,
                                        ),
                                  ),
                                  if (user.company == null)
                                    BlocProvider(
                                      create: (_) => BookingConfigCubit(),
                                    ),
                                ],
                                child: DeepLinkingHandler(
                                  blackListedDomains: const ['done.link'],
                                  child: AppBadgerManager(
                                    child: CallManager(
                                      user: user,
                                      callService:
                                          CallManagerConfig.callService,
                                      incomingCallBuilder:
                                          CallManagerConfig.incomingCallBuilder,
                                      onCallDisconnected:
                                          CallManagerConfig.onCallDisconnected,
                                      onCallPermissionsDenied:
                                          CallManagerConfig
                                              .onCallPermissionsDenied,
                                      ongoingCallBuilder:
                                          CallManagerConfig.ongoingCallBuilder,
                                      outgoingCallBuilder:
                                          CallManagerConfig.outgoingCallBuilder,
                                      unsupportedCallBuilder:
                                          CallManagerConfig
                                              .unsupportedCallBuilder,
                                      child: child!,
                                    ),
                                  ),
                                ),
                              ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
          );
        },
        routeInformationProvider: router.routeInformationProvider,
        routeInformationParser: router.routeInformationParser,
        routerDelegate: router.routerDelegate,
        localizationsDelegates: const [
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
          S.delegate,
        ],
        supportedLocales: S.delegate.supportedLocales,
      ),
    );
  }

  bool _rebuildAuthWhen(AuthState previous, AuthState current) {
    // Auth status changes
    return previous.status != current.status ||
        // Anonymity changes
        previous.isAnonymous != current.isAnonymous ||
        // User changes.
        previous.user?.documentReference.id !=
            current.user?.documentReference.id ||
        // User changes company
        previous.user?.company?.id != current.user?.company?.id;
  }
}
