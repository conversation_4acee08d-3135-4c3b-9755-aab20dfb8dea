import 'package:path/path.dart' as p;

enum DocumentType { pdf, txt }

DocumentType documentTypeFromPath(String path) {
  final extension = p.extension(path).replaceFirst('.', '');
  return documentTypeFromExtension(extension);
}

DocumentType documentTypeFromExtension(String extension) {
  return DocumentType.values.firstWhere((element) => element.name == extension);
}

extension DocumentTypeText on DocumentType {
  String get mimeType {
    switch (this) {
      case DocumentType.pdf:
        return 'application/pdf';
      case DocumentType.txt:
        return 'text/plain';
    }
  }
}
