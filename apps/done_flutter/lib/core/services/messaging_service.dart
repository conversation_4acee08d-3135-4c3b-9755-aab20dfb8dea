import 'dart:async';

import 'package:done_calls/done_calls.dart';
import 'package:done_flutter/core/enums/notification_ui_type.dart';
import 'package:done_flutter/core/router/utils/uri_to_location_converter.dart';
import 'package:done_flutter/ui/widgets/dialogs/done_notification_dialog.dart';
import 'package:done_router/done_router.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:firebase_messaging/firebase_messaging.dart'
    show FirebaseMessaging, RemoteMessage;
import 'package:done_flutter/core/services/background_message_service.dart'
    show backgroundMessageHandler;
import 'package:get_it/get_it.dart';
import 'package:logger/logger.dart';
import 'package:overlay_support/overlay_support.dart';
import 'package:universal_platform/universal_platform.dart';

class APNSToken {
  const APNSToken({required this.token, required this.environment});

  factory APNSToken.fromMap(Map<dynamic, dynamic> data) {
    return APNSToken(
      environment: data['environment'] as String,
      token: data['token'] as String,
    );
  }

  final String token;
  final String environment;
}

class Messaging {
  static final _firebaseMessaging = FirebaseMessaging.instance;

  static void setup({bool initializeBackgroundMessaging = true}) {
    _firebaseMessaging.getInitialMessage().then((message) async {
      // FIX-ME: Figure out why we need to delay routing onLaunch (maybe router / widgets not initialized?)
      await Future<void>.delayed(const Duration(seconds: 1));
      if (!kIsWeb && message != null) await _routeMessage(message.data);
    });

    if (initializeBackgroundMessaging)
      FirebaseMessaging.onBackgroundMessage(backgroundMessageHandler);

    FirebaseMessaging.onMessage.listen(_onForegroundMessage);

    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) async {
      await Future<void>.delayed(const Duration(seconds: 1));
      await _routeMessage(message.data);
    });
  }

  static Future<void> _routeMessage(Map<String, dynamic> message) async {
    final data =
        message['data'] as Map? ??
        message; // Fix for differences between iOS & Android notification payloads. See https://pub.dev/packages/firebase_messaging#notification-messages-with-additional-data

    if (data.containsKey('url')) {
      final goRouter = GetIt.instance<DoneRouter>().router;
      GetIt.instance.get<Logger>().d(
        'Link through notification: ${data['url']}',
      );
      await URLRouter.instance.open(
        Uri.parse(data['url'] as String),
        router: goRouter,
      );
    }
  }

  static Future<void> _onForegroundMessage(RemoteMessage message) async {
    final log = GetIt.instance<Logger>().i;
    log("Incoming FCM message on foreground: ${message.notification}");

    final data = message.data;
    if (UniversalPlatform.isAndroid &&
        data.containsKey('pushType') &&
        (data['pushType'] as String) == 'voip') {
      GetIt.instance<Logger>().d(
        "Incoming VoIP message on foreground: ${data.toString()}",
      );

      final request = callDataFromMessage(data);
      log('Request: ${request.id}');

      if (await request.exists()) {
        log('Request exists');

        await request.displayIncomingCall();
      }
      return;
    }

    if (message.notification == null) return;

    final uri =
        message.data['url'] != null
            ? Uri.parse(message.data['url'] as String)
            : null;

    await _showForegroundNotification(message, uri);
  }
}

Future<void> _showForegroundNotification(
  RemoteMessage message,
  Uri? uri,
) async {
  // Compare current route with the URL from notification.
  // If same route: don't show.
  final router = GetIt.instance<DoneRouter>().router;
  try {
    final currentLocation =
        router.routerDelegate.currentConfiguration.uri.toString();
    if (convertUriToLocation(uri) == currentLocation) {
      GetIt.instance<Logger>().t("FCM Message is for current view, ignore");
      return;
    }
  } catch (e) {
    GetIt.instance<Logger>().e(
      "Failed to compare current route with notification URL",
      error: e,
    );
  }

  if (message.data['foreground_ui_type'] == NotificationUiType.popup.name) {
    final context = router.routerDelegate.navigatorKey.currentContext;
    if (context == null || !context.mounted) {
      GetIt.instance<Logger>().w("No context found, ignoring notification");
      return;
    }

    return showDialog(
      context: context,
      builder: (context) => DoneNotificationDialog(message: message, uri: uri),
    );
  }

  showOverlayNotification(
    (context) => DoneNotification(
      title:
          message.notification?.title != null
              ? Text(message.notification!.title!)
              : null,
      body:
          message.notification?.body != null
              ? Text(
                message.notification!.body!.replaceAll('\n', ' '),
                maxLines: 2,
              )
              : null,
      onTap:
          uri != null
              ? () {
                URLRouter.instance.open(uri, router: context.router);
                OverlaySupportEntry.of(context)!.dismiss();
              }
              : null,
      onDismissed:
          () => OverlaySupportEntry.of(context)!.dismiss(animate: false),
    ),
    duration: const Duration(seconds: 4),
  );
}
