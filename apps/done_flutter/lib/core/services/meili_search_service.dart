import 'package:done_analytics/done_analytics.dart';
import 'package:done_flutter/core/configuration/meili_search_config.dart';
import 'package:done_models/done_models.dart';

import 'package:meilisearch/meilisearch.dart';

class MeiliSearchService {
  MeiliSearchService(MeiliSearchConfig config)
    : client = MeiliSearchClient(config.serverUrl, config.apiKey);

  late final MeiliSearchClient client;

  Future<List<JobSearchEntry>> searchJobsWithCompanyId(
    String companyId,
    String query,
  ) async {
    final searchResult = await client
        .index('jobs')
        .search(query, SearchQuery(filter: ['companyId = $companyId']));

    await EventLogger.instance.logEvent('searchQueries', {
      'type': 'companyJobs',
      'query': query,
    });

    return searchResult.hits.map(JobSearchEntry.fromMap).toList();
  }

  Future<List<JobChatMessageSearchEntry>> searchMessagesWithCompanyId(
    String companyId,
    String query,
  ) async {
    final searchResult = await client
        .index('jobChatMessages')
        .search(
          query,
          SearchQuery(filter: ['messageJobCompanyId = $companyId']),
        );

    await EventLogger.instance.logEvent('searchQueries', {
      'type': 'companyChatMessage',
      'query': query,
    });

    return searchResult.hits.map(JobChatMessageSearchEntry.fromMap).toList();
  }

  Future<List<UserSearchEntry>> searchUsers(String query) async {
    final searchResult = await client
        .index('users')
        .search(query, const SearchQuery(sort: ['fullName:asc']));
    return searchResult.hits.map(UserSearchEntry.fromMap).toList();
  }

  Future<List<CompanySearchEntry>> searchCompanies(String query) async {
    final searchResult = await client.index('companies').search(query);
    return searchResult.hits.map(CompanySearchEntry.fromMap).toList();
  }
}
