import 'dart:io';

import 'package:done_calls/done_calls.dart';
import 'package:done_flutter/core/app_setup.dart';
import 'package:firebase_messaging/firebase_messaging.dart';

void _log(String line) {
  // ignore: avoid_print
  print('[backgroundMessageHandler] $line');
}

/// This function is called by FirebaseMessaging from the main isolate or a
/// background isolate when an FCM message is received, depending on platform
/// and conditions.
///
/// On iOS, it is only called when the app is running (in background),
/// from the main isolate and only for regular notifications.
///
/// It can also be called for data notifications (`content-available`) but we don't use
/// those.
///
/// PushKit notifications for calls are _not_ handled by this function but in the
/// Swift AppDelegate.
///
/// On Android, this function can be run when the app is backgrounded _or_ terminated.
/// It runs both for regular notifications and voip FCM notifications.
///
/// Right now, the only thing this function handles is voip notifications on Android
/// to show an incoming call notification.
///
/// Read more here: https://firebase.flutter.dev/docs/messaging/usage/#background-messages.
@pragma('vm:entry-point')
Future<void> backgroundMessageHandler(RemoteMessage message) async {
  if (!Platform.isAndroid)
    return; // Background notifications are only handled on Android

  try {
    _log("Starting background message handler");
    _log("Message data: ${message.data}");

    if (!message.data.containsKey('pushType') ||
        (message.data['pushType'] as String) != 'voip') {
      _log('Message is not a voip push. Terminate background handler.');
      return;
    }

    await _handleAndroidCallNotification(message);
  } catch (e) {
    _log("background_msg_service error: $e");
  }
}

/// Handles incoming call notifications on Android.
/// Resposible to display the incoming call notification and handle the call.
/// Should not contain any other code since this might be run on a background isolate.
Future<void> _handleAndroidCallNotification(RemoteMessage message) async {
  _log("Incoming call notification received");

  if (!AppSetup.hasBeenRun) {
    _log("App setup not yet run, starting...");
    await AppSetup.startAndroidBackgroundMessageIsolate();
    _log("App setup finished");
  }

  final request = callDataFromMessage(message.data);

  _log('Call request: $request');
  if (await request.exists()) {
    _log('Request exists');

    await request.displayIncomingCall();

    request.handleBackgroundCancellation();
  }

  _log('Handling of background message done');
}
