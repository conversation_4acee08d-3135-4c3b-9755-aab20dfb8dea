import 'package:cross_file/cross_file.dart';
import 'package:done_database/done_database.dart';
import 'package:done_flutter/core/services/firebase_storage_service.dart';
import 'package:done_localizations/done_localizations.dart';
import 'package:done_models/done_models.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:done_flutter/utils/thumbnail_generator.dart';
import 'package:get_it/get_it.dart';
import 'package:video_compress/video_compress.dart';

import 'package:universal_platform/universal_platform.dart';

class Chat {
  const Chat(this.job);

  final DocumentReference<Job> job;

  Future<void> sendItem(
    ChatItem item, {
    required DocumentReference<User> currentUser,
  }) async {
    final message = job.collection('messages').doc().withChatMessageConverter();

    if (item.type == ChatItemType.image) {
      await _processImageUpload(item, message, currentUser: currentUser);
    } else if (item.type == ChatItemType.video) {
      if (kIsWeb)
        await _processVideoUploadForWeb(
          item,
          message,
          currentUser: currentUser,
        );
      else
        await _processVideoUploadForMobile(
          item,
          message,
          currentUser: currentUser,
        );
    } else {
      await _uploadItemWithMessageUpdate(
        item,
        message,
        currentUser: currentUser,
      );
    }
  }

  Future<void> sendItems(
    List<ChatItem> items, {
    required DocumentReference<User> currentUser,
  }) async {
    for (final item in items) {
      await sendItem(item, currentUser: currentUser);
    }
  }

  Future<void> sendTextMessage(
    String text, {
    required DocumentReference<User> currentUser,
    required bool isUserCraftsman,
  }) async {
    final messageRef =
        job.collection('messages').doc().withChatMessageConverter();

    final message = TextChatMessage(
      type: MessageType.text,
      body: text,
      sender: currentUser,
    );

    await Mutations.instance.messages(messageRef).sendTextChatMessage(message);

    if (isUserCraftsman) {
      await Mutations.instance.job(job.id).addCompanySubscriber(message.sender);
    }
  }

  Future<void> _processImageUpload(
    ChatItem item,
    DocumentReference<ChatMessage> document, {
    required DocumentReference<User> currentUser,
  }) async {
    final thumbnail = await generateChatImageThumbnail(
      imageItem: item,
      maxSize: 420,
    );

    await _uploadItemWithMessageUpdate(
      thumbnail.chatItem,
      document,
      currentUser: currentUser,
      mergingDataOnDocumentUpdate: true,
      aspectRatio: thumbnail.aspectRatio,
    );
    await _uploadItemWithMessageUpdate(
      item,
      document,
      currentUser: currentUser,
      mergingDataOnDocumentUpdate: true,
    );
  }

  Future<void> _uploadItemWithMessageUpdate(
    ChatItem item,
    DocumentReference<ChatMessage> message, {
    required DocumentReference<User> currentUser,
    bool mergingDataOnDocumentUpdate = false,
    double? aspectRatio,
  }) async {
    final filePath = await GetIt.instance<FirebaseStorageService>()
        .uploadChatMessageItem(item, job.id, message);
    return Mutations.instance
        .messages(message)
        .sendChatItem(
          item,
          sender: currentUser,
          filePath: filePath,
          options: SetOptions(merge: mergingDataOnDocumentUpdate),
          aspectRatio: aspectRatio,
        );
  }

  Future<void> _processVideoUploadForWeb(
    ChatItem item,
    DocumentReference<ChatMessage> message, {
    required DocumentReference<User> currentUser,
  }) async {
    assert(item.type == ChatItemType.video);
    final messageMutations = Mutations.instance.messages(message);
    // Create the message to show placeholder before video file upload

    await messageMutations.sendChatItem(
      item,
      sender: currentUser,
      filePath: null,
    );

    // Upload video
    final videoPath = await GetIt.instance<FirebaseStorageService>()
        .uploadChatMessageItem(item, job.id, message);

    // Status : upload Finished
    await messageMutations.updateVideoMessage(
      data: item.toChatMessage(currentUser, videoPath).toMap(),
      status: VideoProgressStatus.uploadFinished,
    );
  }

  Future<void> _processVideoUploadForMobile(
    ChatItem item,
    DocumentReference<ChatMessage> message, {
    required DocumentReference<User> currentUser,
  }) async {
    assert(item.type == ChatItemType.video);
    final messageMutations = Mutations.instance.messages(message);
    final storageService = GetIt.instance<FirebaseStorageService>();
    final thumbnailFile = await VideoCompress.getFileThumbnail(
      item.file.path,
      quality: 50,
    );

    // Upload Thumbnail
    final thumbnail = ChatItem(
      XFile(thumbnailFile.path),
      ChatItemType.videoThumbnail,
    );
    final thumbnailPath = await storageService.uploadChatMessageItem(
      thumbnail,
      job.id,
      message,
    );

    await messageMutations.sendChatItem(
      thumbnail,
      sender: currentUser,
      filePath: thumbnailPath,
    );

    // Compress the video into mp4 format
    final mediaInfo = await VideoCompress.compressVideo(
      item.file.path,
      quality: VideoQuality.MediumQuality,
    );

    if (mediaInfo == null ||
        mediaInfo.width == null ||
        mediaInfo.height == null ||
        mediaInfo.duration == null ||
        mediaInfo.path == null)
      return;

    // Get media information
    final aspectRatio =
        UniversalPlatform.isIOS
            ? mediaInfo.width! / mediaInfo.height!
            : mediaInfo.height! / mediaInfo.width!;
    final duration = mediaInfo.duration!;

    final finalItem = ChatItem(XFile(mediaInfo.path!), ChatItemType.video);

    // Status : encode Finished
    await messageMutations.updateVideoMessage(
      status: VideoProgressStatus.encodeFinished,
    );

    // Upload comressed video
    final videoPath = await storageService.uploadChatMessageItem(
      finalItem,
      job.id,
      message,
    );

    // Status : upload Finished
    await messageMutations.updateVideoMessage(
      data:
          finalItem
              .toChatMessage(
                currentUser,
                videoPath,
                aspectRatio: aspectRatio,
                duration: duration.floor(),
              )
              .toMap(),
      status: VideoProgressStatus.uploadFinished,
    );
  }
}

String getMessageTime(Timestamp? timestamp, BuildContext context) {
  final dateTime = DateTime.fromMicrosecondsSinceEpoch(
    timestamp?.microsecondsSinceEpoch ?? DateTime.now().microsecondsSinceEpoch,
  );
  return TimeFormatter(dateTime, context).toHuman();
}
