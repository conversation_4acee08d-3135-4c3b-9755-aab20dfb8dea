import 'package:done_database/done_database.dart';
import 'package:done_models/done_models.dart';
import 'package:get_it/get_it.dart';
import 'package:rxdart/rxdart.dart';

/// Service to watch for pending reviews and show them to the user
class CustomerReviewService {
  static final _shownReviews = <String>{};

  // Stream of jobs with their reviews
  Stream<List<(Job, CompanyReview)>> watchPendingReviews(String customerId) {
    final jobsRepo = GetIt.instance<JobsRepository>();

    return jobsRepo.getPendingReviewJobs(customerId).switchMap((jobs) {
      if (jobs.isEmpty) return Stream.value(<(Job, CompanyReview)>[]);

      // Create streams for each job's review
      final reviewStreams = jobs.map(
        (job) => jobsRepo
            .rating(job.id, job.company!.id)
            .map((review) => (job, review)),
      );

      // Combine all streams
      return CombineLatestStream.list(reviewStreams);
    });
  }

  bool hasShownReview(String jobId) => _shownReviews.contains(jobId);

  void markReviewAsShown(String jobId) => _shownReviews.add(jobId);
}
