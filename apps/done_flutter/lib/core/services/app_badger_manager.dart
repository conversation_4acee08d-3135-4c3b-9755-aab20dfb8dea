import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:app_badge_plus/app_badge_plus.dart';

/// A manager for [AppBadgePlus] which removes the badge on app resume
class AppBadgerManager extends StatefulWidget {
  const AppBadgerManager({super.key, required this.child});
  final Widget child;
  @override
  _AppBadgerManagerState createState() => _AppBadgerManagerState();
}

class _AppBadgerManagerState extends State<AppBadgerManager>
    with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    if (!kIsWeb) {
      _removeBadge();
    }
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      if (!kIsWeb) {
        _removeBadge();
      }
    }
  }

  void _removeBadge() {
    AppBadgePlus.isSupported().then((isSupported) {
      if (isSupported) {
        AppBadgePlus.updateBadge(0);
      }
    });
  }
}
