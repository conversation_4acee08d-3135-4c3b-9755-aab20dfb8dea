import 'dart:io' as io;

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:cross_file/cross_file.dart';
import 'package:done_models/done_models.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_jsonschema_builder/flutter_jsonschema_builder.dart';
import 'package:mime/mime.dart';

class FirebaseStorageService {
  /// Uploads a PDF file to job's bucket according to [jobRef]
  Future<Reference> uploadPdf(
    XFile pdfFile,
    DocumentReference messageRef,
    DocumentReference jobRef,
  ) async {
    final chatItem = ChatItem(pdfFile, ChatItemType.pdf);

    final fileRef = FirebaseStorage.instance.ref().child(
      '${chatItem.firebaseFolder()}/${jobRef.id}/${messageRef.id}/${chatItem.fileName()}.${chatItem.fileExtension()}',
    );
    await fileRef.putData(
      await chatItem.globalBytes,
      SettableMetadata(contentType: chatItem.contentType()),
    );
    return fileRef;
  }

  /// Uploads chat file to the corresponding chat's images' bucket.
  ///
  /// It removes any unnecessary files when not on web like thumbnails after upload
  ///
  /// Returns the path of the [Reference] of the file
  Future<String> uploadChatMessageItem(
    ChatItem item,
    String jobId,
    DocumentReference message,
  ) async {
    final fileReference = FirebaseStorage.instance.ref().child(
      '${item.firebaseFolder()}/$jobId/${message.id}/${item.fileName()}.${item.fileExtension()}',
    );

    await fileReference.putData(
      await item.globalBytes,
      SettableMetadata(contentType: item.contentType()),
    );

    // Remember to delete temporary thumbnail file after upload
    if (!kIsWeb &&
        (item.type == ChatItemType.thumbnail ||
            item.type == ChatItemType.videoThumbnail)) {
      final thumbnail = io.File(item.file.path);
      await thumbnail.delete();
    }

    return fileReference.fullPath;
  }

  /// Uploads schema form file to the corresponding [bucket].
  ///
  /// Returns the path of the [Reference] of the file
  /// The file.name contains the original filename for display purposes
  /// The file.value is used as the storage filename (should be unique)
  Future<String> uploadSchemaFormFile(
    String bucket,
    SchemaFormFile file,
  ) async {
    final fileReference = FirebaseStorage.instance.ref().child(
      '$bucket/${file.value}',
    );

    // Get content type based on the original filename (stored in value)
    // This ensures proper content type detection even with our unique filenames
    final originalName = file.name;
    final contentType =
        lookupMimeType(originalName, headerBytes: file.bytes) ??
        lookupMimeType(file.value, headerBytes: file.bytes);

    await fileReference.putData(
      file.bytes,
      SettableMetadata(
        contentType: contentType,
        customMetadata: {
          'originalName': originalName,
        }, // Store original filename in metadata
      ),
    );

    return fileReference.fullPath;
  }

  /// Downloads a schema form file from the given [path]
  ///
  /// Returns the [SchemaFormFile]
  Future<SchemaFormFile> downloadSchemaFormFile(String path) async {
    final file = FirebaseStorage.instance.ref().child(path);
    final bytes = await file.getData();
    final metadata = await file.getMetadata();

    final name =
        metadata.customMetadata?['originalName'] ?? path.split('/').last;

    return SchemaFormFile(name: name, value: path, bytes: bytes!);
  }

  /// Returns the bytes of the file in the given [path]
  Future<Uint8List?> getFileBytes(String path) async {
    final file = FirebaseStorage.instance.ref().child(path);
    return file.getData();
  }

  /// Returns the metadata of the file in the given [path]
  Future<FullMetadata> getFileMetadata(String path) async {
    final file = FirebaseStorage.instance.ref().child(path);
    return file.getMetadata();
  }
}
