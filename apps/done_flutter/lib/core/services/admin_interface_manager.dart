import 'package:shared_preferences/shared_preferences.dart';

const _isAdminInterfaceEnabledKey = 'is_admin_interface_enabled';

class AdminInterfaceManager {
  AdminInterfaceManager._internal();

  static final AdminInterfaceManager _instance =
      AdminInterfaceManager._internal();

  static AdminInterfaceManager get instance => _instance;

  /// Sets the admin interface as [enabled] or disabled
  ///
  /// It returns a boolean representing whether the toggle operation succeeded or not
  Future<bool> setInterface({required bool enabled}) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.setBool(_isAdminInterfaceEnabledKey, enabled);
  }

  /// Returns whether admin interface is enabled or not
  Future<bool> get isAdminInterfaceEnabled async {
    final prefs = await SharedPreferences.getInstance();
    // The default value is true if it's not set
    return prefs.getBool(_isAdminInterfaceEnabledKey) ?? true;
  }
}
