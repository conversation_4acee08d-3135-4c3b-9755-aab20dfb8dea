import 'package:cloud_firestore/cloud_firestore.dart'
    show DocumentReference, DocumentSnapshot, FirebaseFirestore;
import 'package:done_calls/done_calls.dart';
import 'package:done_flutter/ui/widgets/call/initiate_call_bottom_sheet.dart';
import 'package:done_models/done_models.dart';
import 'package:done_auth/done_auth.dart';
import 'package:done_ui/done_ui.dart';

import 'package:done_localizations/done_localizations.dart'
    show S, UserLocalizationValues;
import 'package:flutter/cupertino.dart';

final _systemUserReference = FirebaseFirestore.instance
    .collection('users')
    .doc('gthflMdLjCZlIuRkp6ayoKEUVcg1');

/// This method gets the [Callee] or lets the user pick a [Callee] if multiple are found
/// Finally it lets the user choose whether to start the call using audio or video
Future<void> initiateCall(
  Job job,
  BuildContext context, {
  required String source,
}) async {
  final callee = await _getCallee(job, context, source: source);
  if (callee != null) {
    return showCupertinoModalPopup<void>(
      context: context,
      builder:
          (context) => InititateCallBottomSheet(
            job: job,
            callee: callee,
            source: source,
          ),
    );
  }
}

/// This method it gets the [Callee] or lets the user pick a [Callee] if multiple are found
Future<Callee?> _getCallee(
  Job job,
  BuildContext context, {
  required String source,
}) async {
  final authState = context.authState;
  Callee? callee;

  final targets = <DocumentReference<User>>{};
  if (!authState.isAuthenticated || job.company == null) return null;

  if (authState.isUserCustomer()) {
    final company = (await job.company!.withCompanyConverter.get()).data();
    if (company == null) return null;
    final companyAdmins = company.admins.map((e) => e.reference).toList();
    final companyOwners = company.owners.map((e) => e.reference).toList();
    final currentUser = authState.user!.documentReference;

    if (job.companySubscribers != null) {
      targets.addAll(job.companySubscribers!);
    }

    if (targets.isEmpty) {
      targets
        ..addAll(companyOwners)
        ..addAll(companyAdmins);
      // Temp solution waiting for https://github.com/FirebaseExtended/flutterfire/pull/3263
    }

    targets
      ..remove(currentUser)
      ..remove(_systemUserReference);
    callee = await _showCallingTargetsPicker(job, context, targets);
  } else {
    callee = Callee(job.customer, job.customerName ?? '');
  }
  return callee;
}

Future<Callee?> _showCallingTargetsPicker(
  Job job,
  BuildContext context,
  Iterable<DocumentReference<User>> targets,
) async {
  if (targets.length == 1) {
    final target = await targets.first.get();
    final targetName = '${target.data()!.firstName} ${target.data()!.lastName}';
    return Callee(targets.first, targetName);
  }

  return showAdaptivePopup<Callee>(
    context: context,
    builder: (context) {
      return DonePopup(
        content: <Widget>[
          for (final ref in targets)
            StreamBuilder<DocumentSnapshot<User>>(
              stream: ref.snapshots(),
              builder: (context, snapshot) {
                if (!snapshot.hasData || snapshot.data?.data() == null)
                  return DoneButton(
                    title: Text(S.of(context).chatLoading),
                    style: DoneButtonStyle.secondary,
                    onPressed: () {},
                  );

                final name = snapshot.data!.data()!.fullNameWithFallback(
                  context,
                );

                return DoneButton(
                  title: Text(name),
                  style: DoneButtonStyle.secondary,
                  onPressed: () {
                    Navigator.of(context).pop(Callee(ref, name));
                  },
                );
              },
            ),
          DoneButton(
            title: Text(S.of(context).cancel),
            style: DoneButtonStyle.neutral,
            onPressed: () {
              Navigator.of(context).pop();
            },
          ),
        ].separatedBy(() => const VerticalMargin.small()),
      );
    },
  );
}
