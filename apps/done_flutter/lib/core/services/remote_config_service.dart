import 'package:done_models/done_models.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/foundation.dart';

class RemoteConfigService {
  /// Initialize the remote config service, sets up default settings and values, also fetches the latest values.
  Future<void> initialize() async {
    final remoteConfig = FirebaseRemoteConfig.instance;

    await remoteConfig.setConfigSettings(
      RemoteConfigSettings(
        fetchTimeout: const Duration(minutes: 1),
        minimumFetchInterval: const Duration(hours: 1),
      ),
    );
    await remoteConfig.setDefaults(const {
      _videoCallServiceKey: kIsWeb ? "webrtc" : "agora",
    });

    await remoteConfig.fetchAndActivate().catchError((
      dynamic error,
      StackTrace stackTrace,
    ) {
      ErrorHandler.reportError(error, stackTrace);
      return false;
    });
  }

  String get videoCallService =>
      FirebaseRemoteConfig.instance.getString(_videoCallServiceKey);

  static const _videoCallServiceKey = "video_call_service";
}
