import 'package:file_picker/file_picker.dart';
import 'package:flutter/services.dart';
import 'package:get_it/get_it.dart';
import 'package:logger/logger.dart';

class FilePickerWeb {
  String? filename;
  List<PlatformFile>? paths;
  bool loadingPath = false;
  bool multiPick = false;
  FileType pickingType = FileType.custom;

  Future<void> openFileExplorer(List<String> allowedExtensions) async {
    loadingPath = true; // Should be inside setState
    try {
      paths =
          (await FilePicker.platform.pickFiles(
            type: pickingType,
            allowMultiple: multiPick,
            allowedExtensions: allowedExtensions,
          ))?.files;
    } on PlatformException catch (e) {
      GetIt.instance<Logger>().e(e);
    } catch (ex) {
      GetIt.instance<Logger>().e(ex);
    }
    loadingPath = false;

    filename = paths?.first.name;
  }

  PlatformFile? pickedFile() => paths?.first;
}
