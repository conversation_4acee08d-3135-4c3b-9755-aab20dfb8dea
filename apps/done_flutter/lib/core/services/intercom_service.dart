import 'package:done_auth/done_auth.dart';
import 'package:done_flutter/core/configuration/intercom_config.dart';

import 'package:done_models/done_models.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';

import 'package:cloud_firestore/cloud_firestore.dart' show DocumentReference;
import 'package:intercom_flutter/intercom_flutter.dart';
import 'package:logger/logger.dart';
import 'package:rxdart/rxdart.dart';

class IntercomHelper {
  IntercomHelper(AuthCubit authCubit, Logger logger) : _logger = logger {
    Intercom.instance.getUnreadStream().listen(
      (event) =>
          _unreadCountController.value =
              num.tryParse(event.toString())?.toInt() ?? 0,
    );
    authCubit.stream.listen((state) async {
      if (state.isUnauthenticated) return logout();

      // If the user is not authenticated, we don't need to do anything
      // Accounts for initial state and other non authenticated states
      if (!state.isAuthenticated) return;

      if (state.isAnonymous) return loginAnonymously();

      await login(state.user!.documentReference.id);
      await updateUser(state.user!);
    });
  }

  static Future<void> initialize(IntercomConfig config) async {
    await Intercom.instance.initialize(
      config.appId,
      iosApiKey: config.iosApiKey,
      androidApiKey: config.androidApiKey,
    );
    if (!kIsWeb) {
      final latestUnreadCount =
          await Intercom.instance.unreadConversationCount();
      _unreadCountController.value = latestUnreadCount;
    }
  }

  final Logger _logger;

  Future<void> updateUser(User user) async {
    try {
      // Anon user, just set the id for easier debugging if an issue arises
      if (user.firstName == null) {
        return Intercom.instance.updateUser(
          userId: user.documentReference.id,
          // Custom attributes containing ID as updating user id for visitor fails
          customAttributes: {'done_user_id': user.documentReference.id},
        );
      }

      _logger.d('Updating Intercom user');

      final companyRef = user.company;

      await Intercom.instance.updateUser(
        email: user.email,
        name:
            user.firstName != null
                ? '${user.firstName} ${user.lastName}'
                : null,
        phone: user.phoneNumber,
        companyId: companyRef?.id,
        company: await _companyName(companyRef),
        customAttributes: {'done_user_id': user.documentReference.id},
      );
    } catch (e) {
      GetIt.instance<Logger>().e("Error updating intercom user: $e");
    }
  }

  Future<void> loginAnonymously() {
    _logger.d('Intercom: Login Anonymously');
    return Intercom.instance.loginUnidentifiedUser();
  }

  static final BehaviorSubject<int> _unreadCountController =
      BehaviorSubject<int>.seeded(0);
  Stream<int> get unreadCount => _unreadCountController.asBroadcastStream();

  Future<void> login(String uid) {
    _logger.d('Intercom: Login');
    return Intercom.instance.loginIdentifiedUser(userId: uid);
  }

  Future<void> setFCMToken(String token) async {
    _logger.d('Intercom: Sending FCM token');
    if (kIsWeb) return;
    return Intercom.instance.sendTokenToIntercom(token);
  }

  Future<dynamic> logout() {
    _logger.d('Intercom: Log out');
    return Intercom.instance.logout();
  }
}

class IntercomRouteObserver extends RouteObserver<PageRoute<dynamic>> {
  void _sendScreenView(PageRoute<dynamic> route) {
    final screenName = route.settings.name;
    if (screenName != null) {
      Intercom.instance.logEvent('screenView', {'name': screenName});
    }
  }

  @override
  void didPush(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPush(route, previousRoute);
    if (route is PageRoute) {
      _sendScreenView(route);
    }
  }

  @override
  void didReplace({Route<dynamic>? newRoute, Route<dynamic>? oldRoute}) {
    super.didReplace(newRoute: newRoute, oldRoute: oldRoute);
    if (newRoute is PageRoute) {
      _sendScreenView(newRoute);
    }
  }

  @override
  void didPop(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPop(route, previousRoute);
    if (previousRoute is PageRoute && route is PageRoute) {
      _sendScreenView(previousRoute);
    }
  }
}

Future<String?> _companyName(DocumentReference<Company>? companyRef) async {
  if (companyRef == null) return null;

  final company = await companyRef.withCompanyConverter.get();
  return company.data()!.name;
}
