PODS:
  - agora_rtc_engine (6.5.0):
    - AgoraIrisRTC_iOS (= 4.5.0-build.1)
    - AgoraRtcEngine_iOS (= 4.5.0)
    - Flutter
  - AgoraInfra_iOS (1.2.13)
  - AgoraIrisRTC_iOS (4.5.0-build.1)
  - AgoraRtcEngine_iOS (4.5.0):
    - AgoraRtcEngine_iOS/AIAEC (= 4.5.0)
    - AgoraRtcEngine_iOS/AIAECLL (= 4.5.0)
    - AgoraRtcEngine_iOS/AINS (= 4.5.0)
    - AgoraRtcEngine_iOS/AINSLL (= 4.5.0)
    - AgoraRtcEngine_iOS/AudioBeauty (= 4.5.0)
    - AgoraRtcEngine_iOS/ClearVision (= 4.5.0)
    - AgoraRtcEngine_iOS/ContentInspect (= 4.5.0)
    - AgoraRtcEngine_iOS/FaceCapture (= 4.5.0)
    - AgoraRtcEngine_iOS/FaceDetection (= 4.5.0)
    - AgoraRtcEngine_iOS/LipSync (= 4.5.0)
    - AgoraRtcEngine_iOS/ReplayKit (= 4.5.0)
    - AgoraRtcEngine_iOS/RtcBasic (= 4.5.0)
    - AgoraRtcEngine_iOS/SpatialAudio (= 4.5.0)
    - AgoraRtcEngine_iOS/VideoAv1CodecDec (= 4.5.0)
    - AgoraRtcEngine_iOS/VideoAv1CodecEnc (= 4.5.0)
    - AgoraRtcEngine_iOS/VideoCodecDec (= 4.5.0)
    - AgoraRtcEngine_iOS/VideoCodecEnc (= 4.5.0)
    - AgoraRtcEngine_iOS/VirtualBackground (= 4.5.0)
    - AgoraRtcEngine_iOS/VQA (= 4.5.0)
  - AgoraRtcEngine_iOS/AIAEC (4.5.0)
  - AgoraRtcEngine_iOS/AIAECLL (4.5.0)
  - AgoraRtcEngine_iOS/AINS (4.5.0)
  - AgoraRtcEngine_iOS/AINSLL (4.5.0)
  - AgoraRtcEngine_iOS/AudioBeauty (4.5.0)
  - AgoraRtcEngine_iOS/ClearVision (4.5.0)
  - AgoraRtcEngine_iOS/ContentInspect (4.5.0)
  - AgoraRtcEngine_iOS/FaceCapture (4.5.0)
  - AgoraRtcEngine_iOS/FaceDetection (4.5.0)
  - AgoraRtcEngine_iOS/LipSync (4.5.0)
  - AgoraRtcEngine_iOS/ReplayKit (4.5.0)
  - AgoraRtcEngine_iOS/RtcBasic (4.5.0):
    - AgoraInfra_iOS (= 1.2.13)
  - AgoraRtcEngine_iOS/SpatialAudio (4.5.0)
  - AgoraRtcEngine_iOS/VideoAv1CodecDec (4.5.0)
  - AgoraRtcEngine_iOS/VideoAv1CodecEnc (4.5.0)
  - AgoraRtcEngine_iOS/VideoCodecDec (4.5.0)
  - AgoraRtcEngine_iOS/VideoCodecEnc (4.5.0)
  - AgoraRtcEngine_iOS/VirtualBackground (4.5.0)
  - AgoraRtcEngine_iOS/VQA (4.5.0)
  - app_badge_plus (1.1.6):
    - Flutter
  - app_links (0.0.2):
    - Flutter
  - AppAuth (1.7.6):
    - AppAuth/Core (= 1.7.6)
    - AppAuth/ExternalUserAgent (= 1.7.6)
  - AppAuth/Core (1.7.6)
  - AppAuth/ExternalUserAgent (1.7.6):
    - AppAuth/Core
  - AppCheckCore (11.2.0):
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - ApsEnvironment (1.0.2)
  - audio_session (0.0.1):
    - Flutter
  - camera_avfoundation (0.0.1):
    - Flutter
  - cloud_firestore (5.6.3):
    - Firebase/Firestore (= 11.7.0)
    - firebase_core
    - Flutter
  - cloud_functions (5.3.2):
    - Firebase/Functions (= 11.7.0)
    - firebase_core
    - Flutter
  - CocoaAsyncSocket (7.6.5)
  - connectivity_plus (0.0.1):
    - Flutter
  - CryptoSwift (1.8.4)
  - device_calendar (0.0.1):
    - Flutter
  - device_info_plus (0.0.1):
    - Flutter
  - DKImagePickerController/Core (4.3.9):
    - DKImagePickerController/ImageDataManager
    - DKImagePickerController/Resource
  - DKImagePickerController/ImageDataManager (4.3.9)
  - DKImagePickerController/PhotoGallery (4.3.9):
    - DKImagePickerController/Core
    - DKPhotoGallery
  - DKImagePickerController/Resource (4.3.9)
  - DKPhotoGallery (0.0.19):
    - DKPhotoGallery/Core (= 0.0.19)
    - DKPhotoGallery/Model (= 0.0.19)
    - DKPhotoGallery/Preview (= 0.0.19)
    - DKPhotoGallery/Resource (= 0.0.19)
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Core (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Preview
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Model (0.0.19):
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Preview (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Resource
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Resource (0.0.19):
    - SDWebImage
    - SwiftyGif
  - facebook_app_events (0.0.1):
    - FBAudienceNetwork (= 6.16)
    - FBSDKCoreKit (~> 18.0)
    - Flutter
  - FBAEMKit (18.0.0):
    - FBSDKCoreKit_Basics (= 18.0.0)
  - FBAudienceNetwork (6.16.0)
  - FBSDKCoreKit (18.0.0):
    - FBAEMKit (= 18.0.0)
    - FBSDKCoreKit_Basics (= 18.0.0)
  - FBSDKCoreKit_Basics (18.0.0)
  - file_picker (0.0.1):
    - DKImagePickerController/PhotoGallery
    - Flutter
  - Firebase/AnalyticsWithoutAdIdSupport (11.7.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics/WithoutAdIdSupport (~> 11.7.0)
  - Firebase/Auth (11.7.0):
    - Firebase/CoreOnly
    - FirebaseAuth (~> 11.7.0)
  - Firebase/CoreOnly (11.7.0):
    - FirebaseCore (~> 11.7.0)
  - Firebase/Crashlytics (11.7.0):
    - Firebase/CoreOnly
    - FirebaseCrashlytics (~> 11.7.0)
  - Firebase/DynamicLinks (11.7.0):
    - Firebase/CoreOnly
    - FirebaseDynamicLinks (~> 11.7.0)
  - Firebase/Firestore (11.7.0):
    - Firebase/CoreOnly
    - FirebaseFirestore (~> 11.7.0)
  - Firebase/Functions (11.7.0):
    - Firebase/CoreOnly
    - FirebaseFunctions (~> 11.7.0)
  - Firebase/Messaging (11.7.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 11.7.0)
  - Firebase/RemoteConfig (11.7.0):
    - Firebase/CoreOnly
    - FirebaseRemoteConfig (~> 11.7.0)
  - Firebase/Storage (11.7.0):
    - Firebase/CoreOnly
    - FirebaseStorage (~> 11.7.0)
  - firebase_analytics (11.4.2):
    - Firebase/AnalyticsWithoutAdIdSupport (= 11.7.0)
    - firebase_core
    - Flutter
  - firebase_app_check (0.3.2-2):
    - Firebase/CoreOnly (~> 11.7.0)
    - firebase_core
    - FirebaseAppCheck (~> 11.7.0)
    - Flutter
  - firebase_auth (5.4.2):
    - Firebase/Auth (= 11.7.0)
    - firebase_core
    - Flutter
  - firebase_core (3.11.0):
    - Firebase/CoreOnly (= 11.7.0)
    - Flutter
  - firebase_crashlytics (4.3.2):
    - Firebase/Crashlytics (= 11.7.0)
    - firebase_core
    - Flutter
  - firebase_dynamic_links (6.1.2):
    - Firebase/DynamicLinks (= 11.7.0)
    - firebase_core
    - Flutter
  - firebase_messaging (15.2.2):
    - Firebase/Messaging (= 11.7.0)
    - firebase_core
    - Flutter
  - firebase_remote_config (5.4.0):
    - Firebase/RemoteConfig (= 11.7.0)
    - firebase_core
    - Flutter
  - firebase_storage (12.4.2):
    - Firebase/Storage (= 11.7.0)
    - firebase_core
    - Flutter
  - FirebaseABTesting (11.7.0):
    - FirebaseCore (~> 11.7.0)
  - FirebaseAnalytics/WithoutAdIdSupport (11.7.0):
    - FirebaseCore (~> 11.7.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleAppMeasurement/WithoutAdIdSupport (= 11.7.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseAppCheck (11.7.0):
    - AppCheckCore (~> 11.0)
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseCore (~> 11.7.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
  - FirebaseAppCheckInterop (11.12.0)
  - FirebaseAuth (11.7.0):
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseAuthInterop (~> 11.0)
    - FirebaseCore (~> 11.7.0)
    - FirebaseCoreExtension (~> 11.7.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GTMSessionFetcher/Core (< 5.0, >= 3.4)
    - RecaptchaInterop (~> 100.0)
  - FirebaseAuthInterop (11.12.0)
  - FirebaseCore (11.7.0):
    - FirebaseCoreInternal (~> 11.7.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Logger (~> 8.0)
  - FirebaseCoreExtension (11.7.0):
    - FirebaseCore (~> 11.7.0)
  - FirebaseCoreInternal (11.7.0):
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - FirebaseCrashlytics (11.7.0):
    - FirebaseCore (~> 11.7.0)
    - FirebaseInstallations (~> 11.0)
    - FirebaseRemoteConfigInterop (~> 11.0)
    - FirebaseSessions (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - FirebaseDynamicLinks (11.7.0):
    - FirebaseCore (~> 11.7.0)
  - FirebaseFirestore (11.7.0):
    - FirebaseFirestoreBinary (= 11.7.0)
  - FirebaseFirestoreAbseilBinary (1.2024011602.0)
  - FirebaseFirestoreBinary (11.7.0):
    - FirebaseCore (= 11.7.0)
    - FirebaseCoreExtension (= 11.7.0)
    - FirebaseFirestoreInternalBinary (= 11.7.0)
    - FirebaseSharedSwift (= 11.7.0)
  - FirebaseFirestoreGRPCBoringSSLBinary (1.65.1)
  - FirebaseFirestoreGRPCCoreBinary (1.65.1):
    - FirebaseFirestoreAbseilBinary (= 1.2024011602.0)
    - FirebaseFirestoreGRPCBoringSSLBinary (= 1.65.1)
  - FirebaseFirestoreGRPCCPPBinary (1.65.1):
    - FirebaseFirestoreAbseilBinary (= 1.2024011602.0)
    - FirebaseFirestoreGRPCCoreBinary (= 1.65.1)
  - FirebaseFirestoreInternalBinary (11.7.0):
    - FirebaseCore (= 11.7.0)
    - FirebaseFirestoreAbseilBinary (= 1.2024011602.0)
    - FirebaseFirestoreGRPCCPPBinary (= 1.65.1)
    - leveldb-library (~> 1.22)
    - nanopb (~> 3.30910.0)
  - FirebaseFunctions (11.7.0):
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseAuthInterop (~> 11.0)
    - FirebaseCore (~> 11.7.0)
    - FirebaseCoreExtension (~> 11.7.0)
    - FirebaseMessagingInterop (~> 11.0)
    - FirebaseSharedSwift (~> 11.0)
    - GTMSessionFetcher/Core (< 5.0, >= 3.4)
  - FirebaseInstallations (11.7.0):
    - FirebaseCore (~> 11.7.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - FirebaseMessaging (11.7.0):
    - FirebaseCore (~> 11.7.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Reachability (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
  - FirebaseMessagingInterop (11.12.0)
  - FirebaseRemoteConfig (11.7.0):
    - FirebaseABTesting (~> 11.0)
    - FirebaseCore (~> 11.7.0)
    - FirebaseInstallations (~> 11.0)
    - FirebaseRemoteConfigInterop (~> 11.0)
    - FirebaseSharedSwift (~> 11.0)
    - GoogleUtilities/Environment (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - FirebaseRemoteConfigInterop (11.12.0)
  - FirebaseSessions (11.7.0):
    - FirebaseCore (~> 11.7.0)
    - FirebaseCoreExtension (~> 11.7.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
    - PromisesSwift (~> 2.1)
  - FirebaseSharedSwift (11.7.0)
  - FirebaseStorage (11.7.0):
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseAuthInterop (~> 11.0)
    - FirebaseCore (~> 11.7.0)
    - FirebaseCoreExtension (~> 11.7.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GTMSessionFetcher/Core (< 5.0, >= 3.4)
  - Flutter (1.0.0)
  - flutter_callkeep (0.3.0):
    - CryptoSwift
    - Flutter
  - flutter_google_places_sdk_ios (0.0.3):
    - Flutter
    - GooglePlaces (~> 8.5.0)
  - flutter_keyboard_visibility (0.0.1):
    - Flutter
  - flutter_pdfview (1.0.2):
    - Flutter
  - flutter_timezone (0.0.1):
    - Flutter
  - flutter_webrtc (0.12.6):
    - Flutter
    - WebRTC-SDK (= 125.6422.06)
  - geolocator_apple (1.2.0):
    - Flutter
  - google_sign_in_ios (0.0.1):
    - AppAuth (>= 1.7.4)
    - Flutter
    - FlutterMacOS
    - GoogleSignIn (~> 7.1)
    - GTMSessionFetcher (>= 3.4.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (11.7.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GooglePlaces (8.5.0)
  - GoogleSignIn (7.1.0):
    - AppAuth (< 2.0, >= 1.7.3)
    - GTMAppAuth (< 5.0, >= 4.1.1)
    - GTMSessionFetcher/Core (~> 3.3)
  - GoogleUtilities/AppDelegateSwizzler (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.1.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.1.0)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/Reachability (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMAppAuth (4.1.1):
    - AppAuth/Core (~> 1.7)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3)
  - GTMSessionFetcher (3.5.0):
    - GTMSessionFetcher/Full (= 3.5.0)
  - GTMSessionFetcher/Core (3.5.0)
  - GTMSessionFetcher/Full (3.5.0):
    - GTMSessionFetcher/Core
  - image_gallery_saver_plus (0.0.1):
    - Flutter
  - image_picker_ios (0.0.1):
    - Flutter
  - in_app_review (2.0.0):
    - Flutter
  - Intercom (18.4.0)
  - intercom_flutter (9.0.0):
    - Flutter
    - Intercom (= 18.4.0)
  - iris_method_channel (0.0.1):
    - Flutter
  - just_audio (0.0.1):
    - Flutter
    - FlutterMacOS
  - leveldb-library (1.22.6)
  - map_launcher (0.0.1):
    - Flutter
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - patrol (0.0.1):
    - CocoaAsyncSocket (~> 7.6)
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - pointer_interceptor_ios (0.0.1):
    - Flutter
  - PromisesObjC (2.4.0)
  - PromisesSwift (2.4.0):
    - PromisesObjC (= 2.4.0)
  - proximity_screen_lock_ios (0.0.1):
    - Flutter
  - RecaptchaInterop (100.0.0)
  - SDWebImage (5.21.0):
    - SDWebImage/Core (= 5.21.0)
  - SDWebImage/Core (5.21.0)
  - Sentry/HybridSDK (8.46.0)
  - sentry_flutter (8.14.2):
    - Flutter
    - FlutterMacOS
    - Sentry/HybridSDK (= 8.46.0)
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sign_in_with_apple (0.0.1):
    - Flutter
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - SwiftyGif (5.4.5)
  - url_launcher_ios (0.0.1):
    - Flutter
  - UUIDNamespaces (1.0.0)
  - video_compress (0.3.0):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - wakelock_plus (0.0.1):
    - Flutter
  - WebRTC-SDK (125.6422.06)

DEPENDENCIES:
  - agora_rtc_engine (from `.symlinks/plugins/agora_rtc_engine/ios`)
  - app_badge_plus (from `.symlinks/plugins/app_badge_plus/ios`)
  - app_links (from `.symlinks/plugins/app_links/ios`)
  - ApsEnvironment (~> 1.0)
  - audio_session (from `.symlinks/plugins/audio_session/ios`)
  - camera_avfoundation (from `.symlinks/plugins/camera_avfoundation/ios`)
  - cloud_firestore (from `.symlinks/plugins/cloud_firestore/ios`)
  - cloud_functions (from `.symlinks/plugins/cloud_functions/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - device_calendar (from `.symlinks/plugins/device_calendar/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - facebook_app_events (from `.symlinks/plugins/facebook_app_events/ios`)
  - file_picker (from `.symlinks/plugins/file_picker/ios`)
  - firebase_analytics (from `.symlinks/plugins/firebase_analytics/ios`)
  - firebase_app_check (from `.symlinks/plugins/firebase_app_check/ios`)
  - firebase_auth (from `.symlinks/plugins/firebase_auth/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_crashlytics (from `.symlinks/plugins/firebase_crashlytics/ios`)
  - firebase_dynamic_links (from `.symlinks/plugins/firebase_dynamic_links/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - firebase_remote_config (from `.symlinks/plugins/firebase_remote_config/ios`)
  - firebase_storage (from `.symlinks/plugins/firebase_storage/ios`)
  - FirebaseFirestore (from `https://github.com/invertase/firestore-ios-sdk-frameworks.git`, tag `11.7.0`)
  - Flutter (from `Flutter`)
  - flutter_callkeep (from `.symlinks/plugins/flutter_callkeep/ios`)
  - flutter_google_places_sdk_ios (from `.symlinks/plugins/flutter_google_places_sdk_ios/ios`)
  - flutter_keyboard_visibility (from `.symlinks/plugins/flutter_keyboard_visibility/ios`)
  - flutter_pdfview (from `.symlinks/plugins/flutter_pdfview/ios`)
  - flutter_timezone (from `.symlinks/plugins/flutter_timezone/ios`)
  - flutter_webrtc (from `.symlinks/plugins/flutter_webrtc/ios`)
  - geolocator_apple (from `.symlinks/plugins/geolocator_apple/ios`)
  - google_sign_in_ios (from `.symlinks/plugins/google_sign_in_ios/darwin`)
  - image_gallery_saver_plus (from `.symlinks/plugins/image_gallery_saver_plus/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - in_app_review (from `.symlinks/plugins/in_app_review/ios`)
  - intercom_flutter (from `.symlinks/plugins/intercom_flutter/ios`)
  - iris_method_channel (from `.symlinks/plugins/iris_method_channel/ios`)
  - just_audio (from `.symlinks/plugins/just_audio/darwin`)
  - map_launcher (from `.symlinks/plugins/map_launcher/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - patrol (from `.symlinks/plugins/patrol/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - pointer_interceptor_ios (from `.symlinks/plugins/pointer_interceptor_ios/ios`)
  - proximity_screen_lock_ios (from `.symlinks/plugins/proximity_screen_lock_ios/ios`)
  - sentry_flutter (from `.symlinks/plugins/sentry_flutter/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sign_in_with_apple (from `.symlinks/plugins/sign_in_with_apple/ios`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - UUIDNamespaces (~> 1.0)
  - video_compress (from `.symlinks/plugins/video_compress/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)

SPEC REPOS:
  trunk:
    - AgoraInfra_iOS
    - AgoraIrisRTC_iOS
    - AgoraRtcEngine_iOS
    - AppAuth
    - AppCheckCore
    - ApsEnvironment
    - CocoaAsyncSocket
    - CryptoSwift
    - DKImagePickerController
    - DKPhotoGallery
    - FBAEMKit
    - FBAudienceNetwork
    - FBSDKCoreKit
    - FBSDKCoreKit_Basics
    - Firebase
    - FirebaseABTesting
    - FirebaseAnalytics
    - FirebaseAppCheck
    - FirebaseAppCheckInterop
    - FirebaseAuth
    - FirebaseAuthInterop
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseCrashlytics
    - FirebaseDynamicLinks
    - FirebaseFirestoreAbseilBinary
    - FirebaseFirestoreBinary
    - FirebaseFirestoreGRPCBoringSSLBinary
    - FirebaseFirestoreGRPCCoreBinary
    - FirebaseFirestoreGRPCCPPBinary
    - FirebaseFirestoreInternalBinary
    - FirebaseFunctions
    - FirebaseInstallations
    - FirebaseMessaging
    - FirebaseMessagingInterop
    - FirebaseRemoteConfig
    - FirebaseRemoteConfigInterop
    - FirebaseSessions
    - FirebaseSharedSwift
    - FirebaseStorage
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GooglePlaces
    - GoogleSignIn
    - GoogleUtilities
    - GTMAppAuth
    - GTMSessionFetcher
    - Intercom
    - leveldb-library
    - nanopb
    - PromisesObjC
    - PromisesSwift
    - RecaptchaInterop
    - SDWebImage
    - Sentry
    - SwiftyGif
    - UUIDNamespaces
    - WebRTC-SDK

EXTERNAL SOURCES:
  agora_rtc_engine:
    :path: ".symlinks/plugins/agora_rtc_engine/ios"
  app_badge_plus:
    :path: ".symlinks/plugins/app_badge_plus/ios"
  app_links:
    :path: ".symlinks/plugins/app_links/ios"
  audio_session:
    :path: ".symlinks/plugins/audio_session/ios"
  camera_avfoundation:
    :path: ".symlinks/plugins/camera_avfoundation/ios"
  cloud_firestore:
    :path: ".symlinks/plugins/cloud_firestore/ios"
  cloud_functions:
    :path: ".symlinks/plugins/cloud_functions/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  device_calendar:
    :path: ".symlinks/plugins/device_calendar/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  facebook_app_events:
    :path: ".symlinks/plugins/facebook_app_events/ios"
  file_picker:
    :path: ".symlinks/plugins/file_picker/ios"
  firebase_analytics:
    :path: ".symlinks/plugins/firebase_analytics/ios"
  firebase_app_check:
    :path: ".symlinks/plugins/firebase_app_check/ios"
  firebase_auth:
    :path: ".symlinks/plugins/firebase_auth/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_crashlytics:
    :path: ".symlinks/plugins/firebase_crashlytics/ios"
  firebase_dynamic_links:
    :path: ".symlinks/plugins/firebase_dynamic_links/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  firebase_remote_config:
    :path: ".symlinks/plugins/firebase_remote_config/ios"
  firebase_storage:
    :path: ".symlinks/plugins/firebase_storage/ios"
  FirebaseFirestore:
    :git: https://github.com/invertase/firestore-ios-sdk-frameworks.git
    :tag: 11.7.0
  Flutter:
    :path: Flutter
  flutter_callkeep:
    :path: ".symlinks/plugins/flutter_callkeep/ios"
  flutter_google_places_sdk_ios:
    :path: ".symlinks/plugins/flutter_google_places_sdk_ios/ios"
  flutter_keyboard_visibility:
    :path: ".symlinks/plugins/flutter_keyboard_visibility/ios"
  flutter_pdfview:
    :path: ".symlinks/plugins/flutter_pdfview/ios"
  flutter_timezone:
    :path: ".symlinks/plugins/flutter_timezone/ios"
  flutter_webrtc:
    :path: ".symlinks/plugins/flutter_webrtc/ios"
  geolocator_apple:
    :path: ".symlinks/plugins/geolocator_apple/ios"
  google_sign_in_ios:
    :path: ".symlinks/plugins/google_sign_in_ios/darwin"
  image_gallery_saver_plus:
    :path: ".symlinks/plugins/image_gallery_saver_plus/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  in_app_review:
    :path: ".symlinks/plugins/in_app_review/ios"
  intercom_flutter:
    :path: ".symlinks/plugins/intercom_flutter/ios"
  iris_method_channel:
    :path: ".symlinks/plugins/iris_method_channel/ios"
  just_audio:
    :path: ".symlinks/plugins/just_audio/darwin"
  map_launcher:
    :path: ".symlinks/plugins/map_launcher/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  patrol:
    :path: ".symlinks/plugins/patrol/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  pointer_interceptor_ios:
    :path: ".symlinks/plugins/pointer_interceptor_ios/ios"
  proximity_screen_lock_ios:
    :path: ".symlinks/plugins/proximity_screen_lock_ios/ios"
  sentry_flutter:
    :path: ".symlinks/plugins/sentry_flutter/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sign_in_with_apple:
    :path: ".symlinks/plugins/sign_in_with_apple/ios"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_compress:
    :path: ".symlinks/plugins/video_compress/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"

CHECKOUT OPTIONS:
  FirebaseFirestore:
    :git: https://github.com/invertase/firestore-ios-sdk-frameworks.git
    :tag: 11.7.0

SPEC CHECKSUMS:
  agora_rtc_engine: bc056a728ed1be8261074a4baf3487e99a6c9a9a
  AgoraInfra_iOS: 65e11a2183ab7836258768868d06058c22701b13
  AgoraIrisRTC_iOS: 8207edf56ceaae6b8ee773d8e2137c6b7cb062be
  AgoraRtcEngine_iOS: 38b5e0a49bbad5616f284e6a4f9e4f5bb3fb3b2e
  app_badge_plus: 6a335976213896ae5d35e588f2ecbe89c9f3c845
  app_links: 3da4c36b46cac3bf24eb897f1a6ce80bda109874
  AppAuth: d4f13a8fe0baf391b2108511793e4b479691fb73
  AppCheckCore: cc8fd0a3a230ddd401f326489c99990b013f0c4f
  ApsEnvironment: 4dc1d8331494b5878cd1f9dd1f540304767602e8
  audio_session: 9bb7f6c970f21241b19f5a3658097ae459681ba0
  camera_avfoundation: 04b44aeb14070126c6529e5ab82cc7c9fca107cf
  cloud_firestore: 176d7a2e629c6473b1f99c413e27efe08715e7af
  cloud_functions: 0812cad3db81427fab054daef70f439ec6625603
  CocoaAsyncSocket: 065fd1e645c7abab64f7a6a2007a48038fdc6a99
  connectivity_plus: cb623214f4e1f6ef8fe7403d580fdad517d2f7dd
  CryptoSwift: e64e11850ede528a02a0f3e768cec8e9d92ecb90
  device_calendar: b55b2c5406cfba45c95a59f9059156daee1f74ed
  device_info_plus: 21fcca2080fbcd348be798aa36c3e5ed849eefbe
  DKImagePickerController: 946cec48c7873164274ecc4624d19e3da4c1ef3c
  DKPhotoGallery: b3834fecb755ee09a593d7c9e389d8b5d6deed60
  facebook_app_events: 015d0181fcb1bf667ada2b8f02d0f53b9ea296dc
  FBAEMKit: e34530df538b8eb8aeb53c35867715ba6c63ef0c
  FBAudienceNetwork: d1670939884e3a2e0ad98dca98d7e0c841417228
  FBSDKCoreKit: d3f479a69127acebb1c6aad91c1a33907bcf6c2f
  FBSDKCoreKit_Basics: 017b6dc2a1862024815a8229e75661e627ac1e29
  file_picker: a0560bc09d61de87f12d246fc47d2119e6ef37be
  Firebase: a64bf6a8546e6eab54f1c715cd6151f39d2329f4
  firebase_analytics: adc07e6a7e80e0c97ce4af06da0ba34e195ed64b
  firebase_app_check: 0fe3abd6014c23d8cb1c865f7e9d34d5e44ca983
  firebase_auth: a1aa0a20e8de111be5b9a5a2ba73201877f9f53d
  firebase_core: 3d36094af9b47c46bfb965943413a39eebaca4db
  firebase_crashlytics: 787a6a63edce2452bc3f088b62099d70e19a3998
  firebase_dynamic_links: 4e94a204c410eab25d44a2e9999b352300fbefa9
  firebase_messaging: 5881ea6744fe41f0759bc52844c530d64f3c37e3
  firebase_remote_config: ffae2e1845f2773589bb0c5eaf16a9ad82c78567
  firebase_storage: 8f65b231a5ff6e98d63f736420e278673f08d7bb
  FirebaseABTesting: 08b3e19b28504632a9cd03e7a796b355c5d39b27
  FirebaseAnalytics: bc9e565af9044ba8d6c6e4157e4edca8e5fdf7ec
  FirebaseAppCheck: 2bd832b48faa38f7d86f902c57f78af93eae4cdc
  FirebaseAppCheckInterop: 73b173e5ec45192e2d522ad43f526a82ad10b852
  FirebaseAuth: 77e25aa24f3e1c626c5babd3338551fc1669ee0e
  FirebaseAuthInterop: b583210c039a60ed3f1e48865e1f3da44a796595
  FirebaseCore: 3227e35f4197a924206fbcdc0349325baf4f5de4
  FirebaseCoreExtension: 206c1b399f0d103055207c16f299b28e3dbd1949
  FirebaseCoreInternal: d6c17dafc8dc33614733a8b52df78fcb4394c881
  FirebaseCrashlytics: 785a73b624715bbc09a40bb56cdc3829a801cc98
  FirebaseDynamicLinks: e81e03f6076bd02081ae6e06631797e134380a76
  FirebaseFirestore: 61305c5ac196ec1526dde68ac132543a7749a081
  FirebaseFirestoreAbseilBinary: fa2ebd2ed02cadef5382e4f7c93f1b265c812c85
  FirebaseFirestoreBinary: 86eaad2ff00b789242734496029a3d08d4d86a89
  FirebaseFirestoreGRPCBoringSSLBinary: d86ebbe2adc8d15d7ebf305fff7d6358385327f8
  FirebaseFirestoreGRPCCoreBinary: 472bd808e1886a5efb2fd03dd09b98d34641a335
  FirebaseFirestoreGRPCCPPBinary: db76d83d2b7517623f8426ed7f7a17bad2478084
  FirebaseFirestoreInternalBinary: 1850c8c72f3d7933a00a4d0bae88021df87c9e10
  FirebaseFunctions: b7f3122ca91ced5253f16aa0418339f19434d0c7
  FirebaseInstallations: 9347e719c3d52d8d7b9074b2c32407dd027305e9
  FirebaseMessaging: 00ece041b71ddb52a2862ffdee73fb6e9824bd0c
  FirebaseMessagingInterop: f4ed7b2d66b89b53fd7982b9317e403691dbd978
  FirebaseRemoteConfig: aa1d4cb05ef4caad203448dfc87842de12f1ea8d
  FirebaseRemoteConfigInterop: 82b81fd06ee550cbeff40004e2c106daedf73e38
  FirebaseSessions: 32ed7a9387ae71efe3a35a7f20f3a7292950957b
  FirebaseSharedSwift: a45efd84d60ebbfdcdbaebc66948af3630459e62
  FirebaseStorage: d35da127dd49edcbd07b8c07cf651a70161558b2
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_callkeep: 753bef54580a21ed91e53593954c92ced854ae41
  flutter_google_places_sdk_ios: c2236643eea32395f32d1667651d796f4377996a
  flutter_keyboard_visibility: 4625131e43015dbbe759d9b20daaf77e0e3f6619
  flutter_pdfview: 32bf27bda6fd85b9dd2c09628a824df5081246cf
  flutter_timezone: ee50ce7786b5fde27e2fe5375bbc8c9661ffc13f
  flutter_webrtc: 57f32415b8744e806f9c2a96ccdb60c6a627ba33
  geolocator_apple: 1560c3c875af2a412242c7a923e15d0d401966ff
  google_sign_in_ios: 19297361f2c51d7d8ac0201b866ef1fa5d1f94a8
  GoogleAppMeasurement: 0471a5b5bff51f3a91b1e76df22c952d04c63967
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GooglePlaces: 426efb69051e7b460e16300ba63598687d10fa1a
  GoogleSignIn: d4281ab6cf21542b1cfaff85c191f230b399d2db
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  GTMAppAuth: f69bd07d68cd3b766125f7e072c45d7340dea0de
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  image_gallery_saver_plus: e597bf65a7846979417a3eae0763b71b6dfec6c3
  image_picker_ios: 7fe1ff8e34c1790d6fff70a32484959f563a928a
  in_app_review: 5596fe56fab799e8edb3561c03d053363ab13457
  Intercom: fe050e77de3419c6a202b616997f566535fca2f8
  intercom_flutter: c290938662c2809a1cb157bcbe0d43183ec6be27
  iris_method_channel: b9db2053dac3dc84e256c8792eff6f11323a53bd
  just_audio: 4e391f57b79cad2b0674030a00453ca5ce817eed
  leveldb-library: cc8b8f8e013647a295ad3f8cd2ddf49a6f19be19
  map_launcher: fe43bda6720bb73c12fcc1bdd86123ff49a4d4d6
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  package_info_plus: af8e2ca6888548050f16fa2f1938db7b5a5df499
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  patrol: dd82ffedfee3aba87c1d0ed2daad0b77bfb8ee1f
  permission_handler_apple: 4ed2196e43d0651e8ff7ca3483a069d469701f2d
  pointer_interceptor_ios: ec847ef8b0915778bed2b2cef636f4d177fa8eed
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  PromisesSwift: 9d77319bbe72ebf6d872900551f7eeba9bce2851
  proximity_screen_lock_ios: 1823ea79f192d8e136140ad2c633216c817430cc
  RecaptchaInterop: 7d1a4a01a6b2cb1610a47ef3f85f0c411434cb21
  SDWebImage: f84b0feeb08d2d11e6a9b843cb06d75ebf5b8868
  Sentry: da60d980b197a46db0b35ea12cb8f39af48d8854
  sentry_flutter: 27892878729f42701297c628eb90e7c6529f3684
  share_plus: 50da8cb520a8f0f65671c6c6a99b3617ed10a58a
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sign_in_with_apple: c5dcc141574c8c54d5ac99dd2163c0c72ad22418
  sqflite_darwin: 20b2a3a3b70e43edae938624ce550a3cbf66a3d0
  SwiftyGif: 706c60cf65fa2bc5ee0313beece843c8eb8194d4
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d
  UUIDNamespaces: bb9d4af8dc3e5ff90068f2d8d42ad5b5c942d380
  video_compress: f2133a07762889d67f0711ac831faa26f956980e
  video_player_avfoundation: 2cef49524dd1f16c5300b9cd6efd9611ce03639b
  wakelock_plus: 04623e3f525556020ebd4034310f20fe7fda8b49
  WebRTC-SDK: 79942c006ea64f6fb48d7da8a4786dfc820bc1db

PODFILE CHECKSUM: 7c2a1df569b3b21ce0dd602d2996d1f521041397

COCOAPODS: 1.16.2
