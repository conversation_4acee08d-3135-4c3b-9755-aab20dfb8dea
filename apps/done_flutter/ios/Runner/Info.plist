<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>$(APP_NAME)</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>Done</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>FlutterDeepLinkingEnabled</key>
	<false/>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>co.doneservices.app</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>doneservices</string>
				<string>com.googleusercontent.apps.865533511519-6elpfdhrsaaag2q1sikvmdu68dmce5pl</string>
				<string>done-services</string>
				<string>com.googleusercontent.apps.704360355803-n2bloi5eb4i8cb5gr2blvaa5svkrrj1o</string>
				<string>fb2444176342512863</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>firebase_dynamic_links</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>co.doneservices.app</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>FacebookAppID</key>
	<string>2444176342512863</string>
	<key>FacebookClientToken</key>
	<string>********************************</string>
	<key>FacebookAutoLogAppEventsEnabled</key>
	<false/>
	<key>FacebookDisplayName</key>
	<string>Done</string>
	<key>FirebaseDynamicLinksCustomDomains</key>
	<array>
		<string>https://done.link</string>
	</array>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsLocalNetworking</key>
		<true/>
	</dict>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>Done använder Bluetooth så att du kan använda trådlösa hörlurar i videosamtal.</string>
	<key>NSCalendarsUsageDescription</key>
	<string>Done använder kalendern för att påminna dig om dina projekt.</string>
	<key>NSCalendarsFullAccessUsageDescription</key>
	<string>Done använder kalendern för att påminna dig om dina projekt.</string>
	<key>NSCameraUsageDescription</key>
	<string>Done använder kameran under dina videosamtal och för att ta bild på saker du vill skicka i ett meddelande.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>Done använder mikrofonen under dina videosamtal.</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>Done behöver detta för att spara bilden till ditt bildbibliotek.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Done behöver tillgång till dina bilder när du vill skicka dem i ett meddelande.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Done använder din position för att välja rätt område för våra tjänster</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>Done använder din position för att välja rätt område för våra tjänster i bakgrunden.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>Done använder din position för att välja rätt område för våra tjänster i bakgrunden.</string>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
		<string>voip</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>Launch Screen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>io.flutter.embedded_views_preview</key>
	<true/>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>googlechromes</string>
		<string>comgooglemaps</string>
		<string>bankid</string>
	</array>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIStatusBarHidden</key>
	<false/>
</dict>
</plist>
