import UIKit
import Flutter
import Intercom
import ApsEnvironment
import firebase_messaging

@main
@objc class AppDelegate: FlutterAppDelegate {
    private var apnsChannel: FlutterMethodChannel?
    private var voipManager: VoIPManager?

    override func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
        ) -> Bool {
        let controller: FlutterViewController = window?.rootViewController as! FlutterViewController
        apnsChannel = FlutterMethodChannel(name: "co.doneservices.app/apns", binaryMessenger: controller as! FlutterBinaryMessenger)
        
        voipManager = VoIPManager()
        voipManager?.configure();

        GeneratedPluginRegistrant.register(with: self)

        return super.application(application, didFinishLaunchingWithOptions: launchOptions)
    }

    override func application(_ application: UIApplication, didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data) {
        apnsChannel!.invokeMethod("token", arguments: ["token": deviceToken.hexEncodedString(), "environment": ApsEnvironment.get()])
        Intercom.setDeviceToken(deviceToken)

        super.application(application, didRegisterForRemoteNotificationsWithDeviceToken: deviceToken)
    }
    
    override func application(_ application: UIApplication, didFailToRegisterForRemoteNotificationsWithError error: Error){
        apnsChannel!.invokeMethod("error", arguments: ["error": error.localizedDescription])
        
        super.application(application, didFailToRegisterForRemoteNotificationsWithError: error)
    }
}
