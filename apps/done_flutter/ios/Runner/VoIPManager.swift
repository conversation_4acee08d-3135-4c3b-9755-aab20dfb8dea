import AVFoundation
import CallKit
import Foundation
import PushKit
import os.log
import UUIDNamespaces
import flutter_callkeep

fileprivate let voipLogger = OSLog(subsystem: Bundle.main.bundleIdentifier!, category: "VOIP")
fileprivate let malformedArguments = FlutterError(code: "voipmanager.malformedArguments", message: "Malformed call arguments", details: nil)

fileprivate let namespace = UUID(uuidString: "28171db0-22c0-47fa-a95b-f39836bfcb5f")!

fileprivate extension Error {
    var flutterError: FlutterError { return .init(code: "SwiftError", message: self.localizedDescription, details: nil) }
}

class VoIPManager: NSObject {
    private var registry: PKPushRegistry!

    func configure() {
        self.registry = PKPushRegistry(queue: .main)
        self.registry.delegate = self
        self.registry.desiredPushTypes = [.voIP]
    }
}

extension VoIPManager: PKPushRegistryDelegate {
    func pushRegistry(_ registry: PKPushRegistry, didUpdate pushCredentials: PKPushCredentials, for type: PKPushType) {
        let token = pushCredentials.token.map { String(format: "%02x", $0) }.joined();
        SwiftCallKeepPlugin.sharedInstance?.setDevicePushTokenVoIP(token)
    }

    func pushRegistry(_ registry: PKPushRegistry, didReceiveIncomingPushWith payload: PKPushPayload, for type: PKPushType, completion: @escaping () -> Void) {
        os_log("Receiving PKPush", log: voipLogger)
        guard
            let callUuid = payload.dictionaryPayload["callUuid"] as? String,
            let callId = payload.dictionaryPayload["callId"] as? String,
            let callType = payload.dictionaryPayload["callType"] as? String,
            let callerHandle = payload.dictionaryPayload["callerHandle"] as? String else {
                fatalError("[DONE] Missing required voip payload data")
        }

        let payloadUuid = payload.dictionaryPayload["callUuid"] as? String
        let uuid = payloadUuid ?? UUID(name: callUuid, namespace: namespace).uuidString
        let data = flutter_callkeep.Data(id: uuid, callerName: callerHandle, handle: callerHandle, hasVideo: callType == "video")
        data.iconName = "IconMask"
        data.maximumCallGroups = 1
        data.appName = "Done"
        data.extra = ["CALL_ID": callId]

        SwiftCallKeepPlugin.sharedInstance?.displayIncomingCall(data, fromPushKit: true)
    }

    func pushRegistry(_ registry: PKPushRegistry, didInvalidatePushTokenFor type: PKPushType) {
        os_log("Invalidating PKPush token", log: voipLogger)
        SwiftCallKeepPlugin.sharedInstance?.setDevicePushTokenVoIP("")
    }
}
