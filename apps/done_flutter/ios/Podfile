# Uncomment this line to define a global platform for your project
platform :ios, '15.0'

# CocoaPods analytics sends network stats synchronously affecting flutter build latency.
ENV['COCOAPODS_DISABLE_STATS'] = 'true'

# Disable IDFA collection in Firebase Analytics.
$FirebaseAnalyticsWithoutAdIdSupport = true

project 'Runner', {
  'Debug' => :debug,
  'Debug-dev' => :debug,
  'Debug-prod' => :debug,
  'Profile' => :release,
  'Release' => :release,
  'Release-dev' => :release,
  'Release-prod' => :release,
}

def flutter_root
  generated_xcode_build_settings_path = File.expand_path(File.join('..', 'Flutter', 'Generated.xcconfig'), __FILE__)
  unless File.exist?(generated_xcode_build_settings_path)
    raise "#{generated_xcode_build_settings_path} must exist. If you're running pod install manually, make sure flutter pub get is executed first"
  end

  File.foreach(generated_xcode_build_settings_path) do |line|
    matches = line.match(/FLUTTER_ROOT\=(.*)/)
    return matches[1].strip if matches
  end
  raise "FLUTTER_ROOT not found in #{generated_xcode_build_settings_path}. Try deleting Generated.xcconfig, then run flutter pub get"
end

require File.expand_path(File.join('packages', 'flutter_tools', 'bin', 'podhelper'), flutter_root)

flutter_ios_podfile_setup
target 'Runner' do
  use_frameworks!
  use_modular_headers!

  target 'RunnerUITests' do
    inherit! :complete
  end
  
  flutter_install_all_ios_pods File.dirname(File.realpath(__FILE__))

  # Use static framework for firestore to increase build times. See https://github.com/invertase/firestore-ios-sdk-frameworks
  firebase_core_script = File.join(File.dirname(__FILE__), '.symlinks/plugins/firebase_core/ios/firebase_sdk_version.rb')
  if File.exist?(firebase_core_script)
    require firebase_core_script
    firebase_sdk_version = firebase_sdk_version!
    Pod::UI.puts "Using Firebase SDK version '#{firebase_sdk_version}' defined in 'firebase_core for FirebaseFirestore framework'"
  else
    raise "Error - unable to locate firebase_ios_sdk.rb script in firebase_core, and no FirebaseSDKVersion specified #{firebase_core_script}"
  end
  pod 'FirebaseFirestore', :git => 'https://github.com/invertase/firestore-ios-sdk-frameworks.git', :tag => firebase_sdk_version

  pod 'ApsEnvironment', '~> 1.0'
  pod 'UUIDNamespaces', '~> 1.0'

end

post_install do |installer|
  installer.pods_project.targets.each do |target|
    flutter_additional_ios_build_settings(target)
    target.build_configurations.each do |config|
      config.build_settings['ENABLE_BITCODE'] = 'NO'
      # To get simulator working with new WebRTC versions
      # https://github.com/flutter-webrtc/flutter-webrtc/issues/713#issuecomment-1001058073
      config.build_settings['EXCLUDED_ARCHS[sdk=iphonesimulator*]'] = 'arm64'
      config.build_settings['ONLY_ACTIVE_ARCH'] = 'YES'

      # Remove unused permissions here (because of permission_handler plugin)
      # See https://pub.dev/packages/permission_handler iOS setup
      config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] ||= [
        '$(inherited)',
        'PERMISSION_CAMERA=1',
        'PERMISSION_MICROPHONE=1',
      ]


      # A workaround that should be removed when this cocoapods fix lands
      # https://github.com/CocoaPods/CocoaPods/pull/12009  
      xcconfig_path = config.base_configuration_reference.real_path
      xcconfig = File.read(xcconfig_path)
      xcconfig_mod = xcconfig.gsub(/DT_TOOLCHAIN_DIR/, "TOOLCHAIN_DIR")
      File.open(xcconfig_path, "w") { |file| file << xcconfig_mod }

      # A workaround that shod be removed when this issue is resolved
      # https://github.com/fluttercommunity/plus_plugins/issues/2155
      config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '13.0'
    end
  end

end
