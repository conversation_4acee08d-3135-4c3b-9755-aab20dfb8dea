# Routing

[go_router](https://pub.dev/packages/go_router) is used for routing.

We use `go_router` in _declarative mode_ by navigating with `.push` instead of `.go` and with `GoRouter.optionURLReflectsImperativeAPIs = true`. This allows us to use imperative navigation from chat -> project and vice versa and still update the URL.

Example:

- /chat/123 -> /projects/123 when pressing "view project".

Downside is that if a web user refreshes the browser or deep links, they go to /projects when pressing the in-app back button. Upside is that we can use query parameters in intermediary pages since the entire URL is rewritten on every push.

## File structure

All the files you need are in `done_flutter/lib/core/router/routes/`

- `routes.dart` defines all the routes set on the GoRouter instance.
- `routes_definitions.dart` defines all the routes as classes that extend `TypedRoute` or `TypedRouteData`.
- `routes_params.dart` defines the parameters for each route.
- `project_routes.dart` contains the routes that are specific to the project feature.
- `project_routes_params.dart` contains the parameters for the routes in `project_routes.dart`.

## Navigating

Most of the routes are currently setup as "named routes" (not using URL-based routing) and uses class constructors:

```dart
final params = ProjectDetailsRouteParams(projectId: id);
ProjectDetailsRoute(params: params).navigate(context);
```

Going forward, we are encouraged to use URL-based routing to move away from the bloat that having a bunch of extra classes need. Using URL-based routing we would use:

```
context.push('/projects/$id');
```

The drawback is that we don't get typesafe URL parameters. That is a known, deliberate choice.

## Adding new routes

1. Define your route in `routes.dart`.
2. Create a route builder function in the page widget that returns the page widget for the given URL.

```dart
static Widget routeBuilder(BuildContext context, GoRouterState state) =>
    ProjectPage(
      showChatButton: true,
      projectId: state.pathParameters['projectId']!,
    );
```

# Push notifications

To deep-link a push-notification, add the `url` field to the `data` part of the notification. For Android, it's also required to set the `click_action` key to `FLUTTER_NOTIFICATION_CLICK`.
Example:

```
const data = {
  url: `doneservices://jobs/$jobId/chat`,
  click_action: 'FLUTTER_NOTIFICATION_CLICK', // Needed for Android
}
```

You can test URL routing in a few ways on iOS.

1. Running `simctl openurl` to open a URL in the iOS simulator

```
xcrun simctl openurl booted "doneservices://jobs/$jobId/chat"
```

2. Drag 'n' dropping an `.apns` file to the iOS simulator to trigger a notification and then opening it. See the `test/apns-payloads` folder for examples.
