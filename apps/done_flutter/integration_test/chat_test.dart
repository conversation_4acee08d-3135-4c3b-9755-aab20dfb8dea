import 'package:done_flutter/core/app_setup.dart';
import 'package:done_flutter/core/configuration/app_config.dart';
import 'package:done_flutter/core/done_app.dart';
import 'package:done_flutter/ui/widgets/conversation_preview_item.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:logger/logger.dart';
import 'package:patrol/patrol.dart';
import 'package:get_it/get_it.dart';
import 'package:universal_platform/universal_platform.dart';

import 'test_utils/handle_custom_tracking_dialog.dart';
import 'test_utils/test_login.dart';

void main() {
  patrolTest('Login as customer and test the chat flow', ($) async {
    await AppSetup.start();
    final configuredApp = AppConfig(
      flavorType: AppSetup.flavor,
      child: const DoneApp(),
    );
    await $.pumpWidgetAndSettle(configuredApp);
    GetIt.instance<Logger>().d("Opened app!");

    //There is cache in between testing sessions on iOS only
    if (!UniversalPlatform.isIOS) await customerLoginFromOnboarding($);
    await handleCustomTrackingDialog($);

    await $('Chats').tap();
    //Tap on the first chat with Sören that pops up
    await $(ListView)
        .$(ConversationPreviewItem)
        .$(RichText)
        .which<RichText>((textw) => textw.text.toPlainText().contains("Sören"))
        .first
        .scrollTo()
        .tap();

    //TODO: Can't test sending pictures and videos atm, FIX or leave it for irl person testing

    // await $(#CameraChatButton).tap();
    // await $('Photo').tap();
    // if (await $.native.isPermissionDialogVisible(timeout: const Duration(seconds: 15))) {
    //   await $.native.grantPermissionWhenInUse();
    // }
    // wait(5);
    // await $.native.getNativeViews(Selector());
    // await wait(20);

    final randomString = "INTEGRATION TESTING ${generateRandomString(10)}";
    await $(#ChatTextField).enterText(randomString);
    await $(#SendChatButton).tap();
    await wait(5);

    //Look if the chat contains the sent message
    await $(#ChatTextMessage).$(randomString).waitUntilVisible();

    FocusManager.instance.primaryFocus?.unfocus();

    //Test call button
    await $('Call').tap();
    if ($('Sören Snickare').visible) {
      await $('Sören Snickare').tap();
    }
    expect($('Video call'), findsOneWidget);
    expect($('Audio call'), findsOneWidget);

    // TODO(any): Figure out a way to test calls without failing
    // testCall($);

    //TODO : Scheduled call/work time messages show correctly
    //- [ ]  Cancelled scheduled call/work time messages show correctly
    // - [ ]  Missed calls messages show correctly
    // - [ ]  Rate call in chat after a successful call
    //     - [ ]  Sending a rating shows in `bots-call-feedback`
    //     - [ ]  Provide reason if 3 stars or below
  });
}
