import 'package:done_flutter/core/app_setup.dart';
import 'package:done_flutter/core/configuration/app_config.dart';
import 'package:done_flutter/core/done_app.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:logger/logger.dart';
import 'package:patrol/patrol.dart';
import 'package:get_it/get_it.dart';
import 'test_utils/handle_custom_tracking_dialog.dart';
import 'test_utils/ignore_exceptions.dart';
import 'test_utils/toggle_cellular.dart';
import 'test_utils/toggle_wifi.dart';
import 'test_utils/test_login.dart';

void main() {
  patrolTest('Anonymous customer Testing Session', ($) async {
    await AppSetup.start();
    final configuredApp = AppConfig(
      flavorType: AppSetup.flavor,
      child: const DoneApp(),
    );
    await $.pumpWidgetAndSettle(configuredApp);
    GetIt.instance<Logger>().d("Opened app!");
    await toggleWifi($, enable: true);
    await toggleCellular($, enable: true);

    //Anonymous Login
    await $('Next').tap();
    await $('Next').tap();
    await $('Next').tap();
    await $('Next').tap();
    await $('Explore').tap(settlePolicy: SettlePolicy.trySettle);
    expect($('Cancel'), findsOneWidget);
    await $(
      'Accept Terms of Service',
    ).tap(settlePolicy: SettlePolicy.trySettle);

    await wait(10);

    if (await $.native.isPermissionDialogVisible(
      timeout: const Duration(seconds: 10),
    )) {
      await $.native.grantPermissionOnlyThisTime();
    }

    await handleCustomTrackingDialog($);

    //Sign in button is shown in Profile tab
    await $('Profile').tap();
    await $('Sign in').waitUntilVisible();

    await $('Explore').tap();

    //Try to grant gps access one more time
    if (await $.native.isPermissionDialogVisible(
      timeout: const Duration(seconds: 10),
    )) {
      await $.native.grantPermissionOnlyThisTime();
    }

    //Sign up as craftsman button at the bottom
    await $(
      SingleChildScrollView,
    ).$('Craftsman? Read more').scrollTo(settlePolicy: SettlePolicy.settle);

    //Cities and categories show correctly
    await $('Stockholm').scrollTo(
      settlePolicy: SettlePolicy.settle,
      scrollDirection: AxisDirection.up,
    );
    await $('Stockholm').waitUntilVisible();
    expect($('Göteborg'), findsOneWidget);
    expect($('Örebro'), findsOneWidget);
    expect($('Umeå'), findsOneWidget);
    expect($('Södertälje'), findsOneWidget);
    expect($('Uppsala'), findsOneWidget);
    expect($('Malmö'), findsOneWidget);
    //Etc......
    await $('Electricity').scrollTo();
    await $('Painting').scrollTo();
    await $('Plumbing').scrollTo();
    await $('Carpenting').scrollTo();
    await $('Kitchen & Bathroom').scrollTo();
    await $('Flooring').scrollTo();
    await $('Small home projects').scrollTo();
    await $('Other').scrollTo();

    //Booking flow works correctly and asks for login at final step.
    await $('Plumbing').scrollTo(scrollDirection: AxisDirection.up).tap();

    //Empty in dev flavor? Prod works
    await $(#BookingStartCtaButton).tap();
    await $(TextField).enterText("I want to fix my roof lamp");
    await $('Continue').tap();
    await $('Within 1-2 weeks').tap();
    await $('Continue').tap();

    await $('No video call needed').tap();

    await $(TextField).enterText("Storgatan 15 Stockholm");
    //Wait some time
    await wait(5);
    await $('Storgatan 15, 114 51 Stockholm, Sweden').tap();
    await $('Continue').tap();

    //Should prompt the user to log in by now
    expect($('Verify phone number'), findsOneWidget);
    expect($('Verify'), findsOneWidget);
    expect($('Craftsman? Read more'), findsOneWidget);
    ignoreExceptions($);
  });
}
