import 'dart:io';

import 'package:done_flutter/core/app_setup.dart';
import 'package:done_flutter/core/configuration/app_config.dart';
import 'package:done_flutter/core/done_app.dart';
import 'package:done_flutter/ui/pages/customer/calculators/floor_sanding_calculator.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:patrol/patrol.dart';
import 'package:universal_platform/universal_platform.dart';

import 'test_utils/handle_custom_tracking_dialog.dart';
import 'test_utils/toggle_cellular.dart';
import 'test_utils/toggle_wifi.dart';
import 'test_utils/test_login.dart';

void main() {
  patrolTest('Login as customer and test the Customer Flow', ($) async {
    await AppSetup.start();
    final configuredApp = AppConfig(
      flavorType: AppSetup.flavor,
      child: const DoneApp(),
    );
    await $.pumpWidgetAndSettle(configuredApp);

    if (!UniversalPlatform.isIOS) await customerLoginFromOnboarding($);
    //####################### CUSTOMER HOME ###############################
    await handleCustomTrackingDialog($);

    //WARNING : THIS TEST ONLY WORKS IN PROD FLAVOR

    //Show & navigate to projects
    await $('Projects').tap();

    //Previous projects exists
    await $('Previous projects').scrollTo();

    //Asks for location and updates city accordingly
    if (await $.native.isPermissionDialogVisible(
      timeout: const Duration(seconds: 10),
    )) {
      await $.native.grantPermissionWhenInUse();
      //TODO : Check if city is updated here
    }

    await $('Explore').tap();

    // await $('Your projects').waitUntilVisible();

    await $('Flooring').scrollTo().tap();
    await $(#ContentCardText).scrollTo().tap();

    await $('Furu').tap();
    await $('Ek').tap();
    await $('Olja').tap();
    await $('Lack').tap();
    await $('Hårdvaxolja').tap();
    await $('Yes').tap();
    await $('No').tap();
    //Todo : maybe more robust verification that the calculator works, parse the text into nums and compare if there is a change.
    await $('Svårt att välja? Läs mer på vår blogg').waitUntilVisible();
    await $(#ExpandablePanelCalculator).tap();

    await $(
      'Priset är en uppskattning och gäller vid ROT-avdrag. Eventuella kostnader för trösklar & reparation tillkommer. Boka videosamtal för exakt offert.',
    ).scrollTo();
    expect($(CalculatorLabelValue), findsNWidgets(4));

    await $(BackButton).tap();
    await $(BackButton).tap();

    await $('Profile').tap();

    // Only test help center on Android as we can't dismiss on iOS yet
    // TODO(ayman) : look into how do to this in IOS
    if (Platform.isAndroid) {
      await $('Help center & contact').tap();

      await pressBackOnAndroid($);
    }

    //"Share app with friend" button
    await $('Explore').tap();
    await $('Friend in need of pro help? Share Done!').scrollTo();

    // Only test sharingon Android as we can't dismiss on iOS yet
    // TODO(ayman) : look into how do to this in IOS
    if (Platform.isAndroid) {
      await $(#GiveAwayButton).scrollTo().tap();
      await pressBackOnAndroid($);
    }

    //Connectivity check banner works (when no connection)
    final wifiDisabled = await toggleWifi($, enable: false);
    final cellularDisabled = await toggleCellular($, enable: false);
    if (wifiDisabled && cellularDisabled) {
      await $(
        'You are not connected to the Internet.',
      ).waitUntilVisible(timeout: const Duration(seconds: 60));
    }
    await toggleWifi($, enable: true);
    await toggleCellular($, enable: true);
  });
}
