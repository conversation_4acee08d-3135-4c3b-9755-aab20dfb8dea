import 'package:done_flutter/core/app_setup.dart';
import 'package:done_flutter/core/configuration/app_config.dart';
import 'package:done_flutter/core/done_app.dart';
import 'package:done_flutter/ui/pages/chat/chat_page.dart';
import 'package:done_flutter/ui/widgets/jobs/job_offer_map.dart';
import 'package:done_flutter/ui/widgets/jobs/job_offer_row.dart';
import 'package:flutter/material.dart';
import 'package:patrol/patrol.dart';
import 'package:universal_platform/universal_platform.dart';

import 'test_utils/test_login.dart';

void main() {
  patrolTest('Login as customer and test the General Flow', ($) async {
    await AppSetup.start();
    final configuredApp = AppConfig(
      flavorType: AppSetup.flavor,
      child: const DoneApp(),
    );
    await $.pumpWidgetAndSettle(configuredApp);
    // iOS user is cached from previous tests, let's sign customer out
    if (UniversalPlatform.isIOS) await logoutCustomer($);

    await companyLogin($);

    //Tap on the first chat that pops up with <PERSON> Testsson
    // Testing Waiting for quote one
    await $('Job offers').tap(visibleTimeout: const Duration(seconds: 120));

    //If we don't have a job offer, just return from the test
    await wait(5);
    // if (!$(ListView).$(JobOfferRow).visible) {
    //   return;
    // }
    await $(JobOfferRow).tap(); //This creates exception
    await $('Submitted').waitUntilVisible();
    await $('Area').waitUntilVisible();
    await $('Reference number').waitUntilVisible();
    await $(JobOfferMap).waitUntilVisible();

    //Decline show a prompt with reasons
    await $('Decline').tap();
    await $('Job offer declined').waitUntilVisible();
    await $("Can't take call at that time").waitUntilVisible();
    await $('Too close project start').waitUntilVisible();
    await $('Fully booked').waitUntilVisible();
    await $('Bad fit for job').waitUntilVisible();
    await $('Bad customer request').waitUntilVisible();
    //Choosing other prompts asking for other reason
    await $('Other').tap();
    await $(TextFormField).enterText("My life be like oooaaa");
    await $('Back').tap();
    await $('Cancel').tap();
    //Conditionally accept shows a prompt with reasons
    await $('Accept conditionally').tap();
    await $('Different start date').waitUntilVisible();
    await $('Increased budget').waitUntilVisible();
    //Choosing other prompts asking for other reason
    await $('Other').tap();
    await $(TextFormField).enterText("I want hugs");
    await $('Back').tap();
    await $('Cancel').tap();
    //Accepting shows confirmation and works correctly
    await $('Accept').tap();
    await $('Accept').waitUntilVisible();
    await $('Accept & open').tap();
    await $("Job offer accepted").waitUntilVisible();

    await $(ChatPage).waitUntilVisible();
  });
}
