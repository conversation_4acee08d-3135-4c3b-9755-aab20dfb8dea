//Todo : Uncomment when cloud function is accepted
// import 'package:cloud_functions/cloud_functions.dart';
import 'package:done_booking/done_booking.dart';
import 'package:done_flutter/core/app_setup.dart';
import 'package:done_flutter/core/configuration/app_config.dart';
import 'package:done_flutter/core/done_app.dart';
import 'package:done_flutter/ui/pages/customer/fixed_price/fixed_price_list_item.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:logger/logger.dart';
import 'package:patrol/patrol.dart';
import 'package:get_it/get_it.dart';
import 'package:universal_platform/universal_platform.dart';

import 'test_utils/handle_custom_tracking_dialog.dart';
import 'test_utils/ignore_exceptions.dart';
import 'test_utils/test_login.dart';

void main() {
  patrolTest('Login as customer and test booking flow', ($) async {
    await AppSetup.start();
    final configuredApp = AppConfig(
      flavorType: AppSetup.flavor,
      child: const DoneApp(),
    );
    await $.pumpWidgetAndSettle(configuredApp);
    GetIt.instance<Logger>().d("Opened app!");
    await handleCustomTrackingDialog($);
    // Anon user is cached on iOS from previous test
    if (UniversalPlatform.isIOS) {
      await customerLoginFromAnon($);
    } else {
      await customerLoginFromOnboarding($);
    }
    //#################### BOOKING FLOW ###########################
    //TODO : Express functionality (only in prod flavor currently),
    //Go to a plumbing fixed pricing job
    await $('Projects').tap();
    await $('Explore').tap();
    //We should have internet
    expect($(#NotConnectedBanner), findsNothing);
    await $('Plumbing').scrollTo().tap();
    await $('Book with fixed price').waitUntilVisible();
    //Sometimes permission dialog pops up
    if (await $.native.isPermissionDialogVisible(
      timeout: const Duration(seconds: 10),
    )) {
      await $.native.grantPermissionWhenInUse();
    }
    await fixedPricePlumbingBookingFlow($);
    await $(Icons.close).tap();
    await $('Continue').tap();
    await $('Service type').waitUntilVisible();
    await $('Plumbing').waitUntilVisible();
    await $('TEST').waitUntilVisible();
    await $(Icons.close).tap();
    //Should discard the whole booking, redo it
    await $('Discard booking').tap();
    await $(BackButton).tap();
    await $(BackButton).tap();

    await $('Painting').scrollTo().tap();
    await normalBookingFlow($);

    await $(#SubmitBookingButton).tap();
    await $('Accept & book').tap();
    //Wait some time to finalize the Firebase updates
    await wait(5);
    await $('⏱ Waiting for company match').waitUntilVisible();
  });
}

Future<void> fixedPricePlumbingBookingFlow(PatrolIntegrationTester $) async {
  // Press on the first fixed price job, should be toilet chair thing
  await $(Column).$(FixedPriceListItem).at(0).tap();
  // Details shows
  //Todo : Maybe do more extensive parsing of ints to see we get reasonable values?
  await $('Labor').waitUntilVisible();
  await $('50% ROT deduction').waitUntilVisible();
  await $('Installation material').waitUntilVisible();
  await $('Start & travel costs').waitUntilVisible();
  //Parse to see if there's a price
  final price =
      num.tryParse($(#PriceText).text!.replaceAll(RegExp('[^0-9]'), '')) ?? -1;

  //Price should be something high
  expect(price > 100, equals(true));

  // - Booking with checklist
  //     - Refers to video call if not checked all
  // - Booking without checklist
  await $(FixedPriceCheckBoxListItem).scrollTo();
  for (var i = 0; i < 8; i++) {
    await $(ListView).$(FixedPriceCheckBoxListItem).at(i).scrollTo().tap();
    if (i == 7) break;
    await $(#BookFixedPriceJobButton).scrollTo().tap();
    await $('Book video call').waitUntilVisible();
    await $('Cancel').tap();
  }

  await $(#BookFixedPriceJobButton).tap();

  //Enter a description
  //Todo: Image uploading
  await $(DoneButton)
      .which<DoneButton>((btn) => btn.onPressed == null)
      .containing('Continue')
      .waitUntilVisible(); //button should not be clickable
  await $(#ProjectDescriptionTextField).enterText("TEST");
  await $('Continue').tap();
  //Booking modal
  await $('Requested start date').waitUntilVisible();
  await $(DoneButton)
      .which<DoneButton>((btn) => btn.onPressed == null)
      .containing('Continue')
      .waitUntilVisible(); //button should not be clickable
  //Express doesn't exist in dev flavor
  await $('Within 1-2 weeks').tap();
  await $('Continue').tap();
  await wait(5);
  //Location should be preselected since we have an account
  // Dismiss keyboard if visible
  FocusManager.instance.primaryFocus?.unfocus();
  await $('Continue').tap();
  //Access the jobID
  //Summary scene
  await $('Discount code').waitUntilVisible();
  await $('Service type').waitUntilVisible();
  await $('Plumbing').waitUntilVisible();
  await $('Within 1-2 weeks').waitUntilVisible();
  await $('TEST').waitUntilVisible();
}

Future<void> normalBookingFlow(PatrolIntegrationTester $) async {
  await $(#BookingStartCtaButton).tap();
  // TODO(any): test image uploading
  //Enter a description
  await $(#ProjectDescriptionTextField).enterText("TEST");
  await $('Continue').scrollTo().tap();
  //Booking modal
  await $('Requested start date').waitUntilVisible();
  await $(DoneButton)
      .which<DoneButton>((btn) => btn.onPressed == null)
      .containing('Continue')
      .waitUntilVisible(); //button should not be clickable
  //Express doesn't exist in dev flavor
  await $('Within 1-2 weeks').tap();
  await $('Continue').tap();
  await $('Preferred call date').waitUntilVisible();
  await $('Continue').tap();
  await $('Preferred call time').waitUntilVisible();
  await $('Continue').tap();
  await wait(5);
  //Location should be preselected since we have an account
  // Dismiss keyboard if visible
  FocusManager.instance.primaryFocus?.unfocus();
  await $('Continue').tap();
  //Summary scene
  await $('Discount code').waitUntilVisible();
  await $('Service type').waitUntilVisible();
  await $('Painting').waitUntilVisible();
  await $('Within 1-2 weeks').waitUntilVisible();
  await $('TEST').waitUntilVisible();

  ignoreExceptions($);
}
