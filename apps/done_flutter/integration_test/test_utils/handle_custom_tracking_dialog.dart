import 'dart:developer';

import 'package:patrol/patrol.dart';
import 'package:universal_platform/universal_platform.dart';

/// Handles the Custom Tracking Dialog on iOS devices, for some reason patrol fails to accept the permission
/// But for now it's not needed to approve the permission
Future<void> handleCustomTrackingDialog(PatrolIntegrationTester $) async {
  if (UniversalPlatform.isIOS) {
    try {
      // Accept custom tracking dialog on iOS
      await $(#CustomTrackingDialogButton).tap();

      if (await $.native.isPermissionDialogVisible(
        timeout: const Duration(seconds: 10),
      )) {
        await $.native.grantPermissionWhenInUse();
      }
    } catch (e) {
      log('Could not accept custom tracking dialog');
    }
  }
}
