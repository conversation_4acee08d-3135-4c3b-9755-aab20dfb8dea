import 'dart:developer';

import 'package:patrol/patrol.dart';

/// To be called by the end of tests where we would like to ingore uncaught exceptions
void ignoreExceptions(PatrolIntegrationTester $) {
  var exceptionCount = 0;
  dynamic exception = $.tester.takeException();
  while (exception != null) {
    exceptionCount++;
    exception = $.tester.takeException();
  }
  if (exceptionCount != 0) {
    log('Warning: $exceptionCount exceptions were ignored');
  }
}
