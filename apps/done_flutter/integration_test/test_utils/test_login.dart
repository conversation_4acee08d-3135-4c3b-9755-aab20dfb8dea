import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:logger/logger.dart';
import 'package:patrol/patrol.dart';
import 'package:flutter_test/flutter_test.dart';
import 'dart:math';

import 'package:universal_platform/universal_platform.dart';

import 'toggle_cellular.dart';
import 'toggle_wifi.dart';

String generateRandomString(int length) {
  final random = Random();
  const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';

  final buffer = StringBuffer();
  for (var i = 0; i < length; i++) {
    final randomIndex = random.nextInt(chars.length);
    buffer.write(chars[randomIndex]);
  }

  return buffer.toString();
}

Future<void> pressBackOnAndroid(PatrolIntegrationTester $) async {
  if (UniversalPlatform.isAndroid) {
    await $.native.pressBack();
  }
}

Future<void> wait(int seconds) async {
  await Future.delayed(const Duration(seconds: 5), () {});
}

/// Login to the app as customer 'John Appleseed' after logining in as anonymous
Future<void> customerLoginFromAnon(PatrolIntegrationTester $) async {
  // no need to login as anonymous again since firebase auth is cached
  if (!UniversalPlatform.isIOS) {
    await toggleWifi($, enable: true);
    await toggleCellular($, enable: true);
    await $('Next').tap();
    await $('Next').tap();
    await $('Next').tap();
    await $('Next').tap();
    await $('Explore').tap(settlePolicy: SettlePolicy.trySettle);
    expect($('Cancel'), findsOneWidget);
    await $(
      'Accept Terms of Service',
    ).tap(settlePolicy: SettlePolicy.trySettle);
  }
  await $('Profile').tap(
    settlePolicy: SettlePolicy.trySettle,
    visibleTimeout: const Duration(seconds: 20),
  );

  await $('Sign in').tap(settlePolicy: SettlePolicy.trySettle);
  if (await $.native.isPermissionDialogVisible(
    timeout: const Duration(seconds: 10),
  )) {
    await $.native.grantPermissionWhenInUse();
  }
  await $(#PhoneNumberTextField).waitUntilVisible().enterText(
    '0701234567',
    visibleTimeout: const Duration(seconds: 5),
  );

  await $('Verify').tap();

  await $(EditableText).waitUntilVisible();

  await $(
    EditableText,
  ).enterText('321654', settlePolicy: SettlePolicy.trySettle);

  if (await $.native.isPermissionDialogVisible(
    timeout: const Duration(seconds: 5),
  )) {
    await $.native.grantPermissionWhenInUse();
  }
  // await $('Sign in').tap();
}

Future<void> customerLoginFromOnboarding(PatrolIntegrationTester $) async {
  try {
    await toggleWifi($, enable: true);
    await toggleCellular($, enable: true);
    await $('Sign in').tap();

    await $(#PhoneNumberTextField).waitUntilVisible().enterText(
      '0701234567',
      visibleTimeout: const Duration(seconds: 15),
    );
    await $('Verify').tap();
    // We use `EditableText` here since `Pinput` use it down the widget tree, we can't target `Pinput` directly,
    // see: https://github.com/Tkko/Flutter_Pinput/issues/140 and https://github.com/leancodepl/patrol/issues/1409
    await $(EditableText).enterText('321654');

    await wait(5);

    if (await $.native.isPermissionDialogVisible(
      timeout: const Duration(seconds: 10),
    )) {
      await $.native.grantPermissionWhenInUse();
    }
    await $('Sign in').tap();
    await Future.delayed(const Duration(seconds: 5), () {});

    await $('Explore').tap();

    if (await $.native.isPermissionDialogVisible(
      timeout: const Duration(seconds: 10),
    )) {
      await $.native.grantPermissionWhenInUse();
    }
  } catch (err) {
    GetIt.instance<Logger>().d(err.toString());
    //Log something
  }
}

Future<void> logoutCustomer(PatrolIntegrationTester $) async {
  await $('Profile').tap(visibleTimeout: const Duration(seconds: 10));
  await $(#SignOutButton).tap();
  await $(#SignOutDialogButton).tap();
}

/// Login to the app as Company Sören Snickare
Future<void> companyLogin(PatrolIntegrationTester $) async {
  await toggleWifi($, enable: true);
  await toggleCellular($, enable: true);
  await $('Sign in').tap();

  await $(#PhoneNumberTextField).waitUntilVisible().enterText(
    '0707654321',
    visibleTimeout: const Duration(seconds: 15),
  );
  await $('Verify').tap();
  // We use `EditableText` here since `Pinput` use it down the widget tree, we can't target `Pinput` directly,
  // see: https://github.com/Tkko/Flutter_Pinput/issues/140 and https://github.com/leancodepl/patrol/issues/1409
  await $(EditableText).enterText('321654');

  await wait(5);

  if (await $.native.isPermissionDialogVisible(
    timeout: const Duration(seconds: 10),
  )) {
    await $.native.grantPermissionWhenInUse();
  }
}
