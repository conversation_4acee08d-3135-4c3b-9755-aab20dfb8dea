import 'package:device_info_plus/device_info_plus.dart';
import 'package:patrol/patrol.dart';
import 'package:universal_platform/universal_platform.dart';

Future<bool> toggleWifi(
  PatrolIntegrationTester $, {
  required bool enable,
}) async {
  if (!UniversalPlatform.isIOS) {
    enable ? await $.native.enableWifi() : await $.native.disableWifi();
    return true;
  }

  final isSimulator = !(await DeviceInfoPlugin().iosInfo).isPhysicalDevice;
  if (isSimulator) return false;

  enable ? await $.native.enableWifi() : await $.native.disableWifi();
  return true;
}
