import 'package:done_flutter/ui/widgets/buttons/call_action_button.dart';
import 'package:done_flutter/ui/widgets/chat/message_types/call_missed_or_declined.dart';
import 'package:done_ui/done_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:patrol/patrol.dart';
import 'package:universal_platform/universal_platform.dart';

Future<void> testCall(PatrolIntegrationTester $) async {
  await $('Audio call').tap();
  if (await $.native.isPermissionDialogVisible(
    timeout: const Duration(seconds: 5),
  )) {
    await $.native.grantPermissionWhenInUse();
  }
  if (await $.native.isPermissionDialogVisible(
    timeout: const Duration(seconds: 5),
  )) {
    await $.native.grantPermissionWhenInUse();
  }

  // The bluetooth permission is only on Android
  if (UniversalPlatform.isAndroid) {
    if (await $.native.isPermissionDialogVisible(
      timeout: const Duration(seconds: 5),
    )) {
      await $.native.grantPermissionWhenInUse();
    }
  }

  //Close keyboard if open
  FocusManager.instance.primaryFocus?.unfocus();
  await $('take photo').waitUntilVisible();
  //If End Call button not visible, probably because keyboard is up

  await $(EndCallButton).$(Material).tap(settlePolicy: SettlePolicy.settle);

  expect($(PageContent).containing(CallMissedOrDeclined), findsWidgets);
}
