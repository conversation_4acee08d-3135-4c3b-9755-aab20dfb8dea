allprojects {
    repositories {
        google()
        mavenCentral()
    }
    // Remove screen sharing module as we don't use it and causes permission issues
    // https://github.com/AgoraIO-Extensions/Agora-Flutter-SDK/issues/1575
    configurations.all {
        exclude group: "io.agora.rtc", module: "full-screen-sharing"
    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(':app')
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
