{"project_info": {"project_number": "865533511519", "firebase_url": "https://done-50549.firebaseio.com", "project_id": "done-50549", "storage_bucket": "done-50549.appspot.com"}, "client": [{"client_info": {"mobilesdk_app_id": "1:865533511519:android:f8d8a205555646d1e8d8ea", "android_client_info": {"package_name": "co.doneservices.callsDemo"}}, "oauth_client": [{"client_id": "865533511519-iirdlsb551kkq7gkmkse30buuuc14g49.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyB1PNq7nmA0J8cupYmstael3q5CSGHBkGM"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "865533511519-0dqa4aaj7jdkp767k3su7ont2fckh92n.apps.googleusercontent.com", "client_type": 3}, {"client_id": "865533511519-6elpfdhrsaaag2q1sikvmdu68dmce5pl.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "co.doneservices.app", "app_store_id": "1459408976"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:865533511519:android:2a9c67327e976253", "android_client_info": {"package_name": "co.doneservices.doneFlutter"}}, "oauth_client": [{"client_id": "865533511519-2jrq5o7me9crg41546ejsjpgpc8e6r4v.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "co.doneservices.doneFlutter", "certificate_hash": "ffefabfe06c94d4d0961f5f2e02d839ca33644ed"}}, {"client_id": "865533511519-6ce5f18mmu7osrd530c7akksuj9um6j3.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "co.doneservices.doneFlutter", "certificate_hash": "64aa8bd9b6b45a2c4f18fc501fb8a75105e7a6c5"}}, {"client_id": "865533511519-6ur5ameevssufl5jtfcs862ibjd0oa7o.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "co.doneservices.doneFlutter", "certificate_hash": "f37485fbebada874714dcfaa069c699d79909004"}}, {"client_id": "865533511519-7vf9sq0mk2vogq8tqpatnp8e1f3sqhqn.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "co.doneservices.doneFlutter", "certificate_hash": "eeffa1a6146360917fa183398cb7fe9e46574958"}}, {"client_id": "865533511519-9g6ni96ghc72jaop79gf4oso9v54am72.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "co.doneservices.doneFlutter", "certificate_hash": "fe8d87e44bb17df430f3ea40db40372f681012e6"}}, {"client_id": "865533511519-fm8naph15ajo9sujd7l2funf9fe3oa53.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "co.doneservices.doneFlutter", "certificate_hash": "2585e1d2b52f3996866da9a4cbfd641654be5184"}}, {"client_id": "865533511519-h77a88hct0elm6jgjtvmlli96b974kih.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "co.doneservices.doneFlutter", "certificate_hash": "1269ee079286463d001099d7f6fdb5962a1b4e6b"}}, {"client_id": "865533511519-k5nvdm3g9ten6vj0bda9ku8pg941r2uv.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "co.doneservices.doneFlutter", "certificate_hash": "2143dabd0acc59dbb1ef6fbfb94ccd5c14c56fe3"}}, {"client_id": "865533511519-kll9q38id1kf1hj9sl12ld8j0dq5erjr.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "co.doneservices.doneFlutter", "certificate_hash": "75f749ab8332afb27e4d476a6b9bf92b36ebe7fc"}}, {"client_id": "865533511519-o4sqiiikd1v8qg3arpgosu9kr4fhd4jm.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "co.doneservices.doneFlutter", "certificate_hash": "68c738648c1192718186273b4bfb0ebdeca435ea"}}, {"client_id": "865533511519-slu6407nlkri8edj8cj7slha8fjs3rch.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "co.doneservices.doneFlutter", "certificate_hash": "124697dfa4fe94e88dffbb5da96a9761cc4584e8"}}, {"client_id": "865533511519-u41dihhqc1os89pl0df8h738i7t986vv.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "co.doneservices.doneFlutter", "certificate_hash": "710fe10004cb796cd26bea69cbe5e11a9545978a"}}, {"client_id": "865533511519-iirdlsb551kkq7gkmkse30buuuc14g49.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyB1PNq7nmA0J8cupYmstael3q5CSGHBkGM"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "865533511519-0dqa4aaj7jdkp767k3su7ont2fckh92n.apps.googleusercontent.com", "client_type": 3}, {"client_id": "865533511519-6elpfdhrsaaag2q1sikvmdu68dmce5pl.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "co.doneservices.app", "app_store_id": "1459408976"}}]}}}], "configuration_version": "1"}