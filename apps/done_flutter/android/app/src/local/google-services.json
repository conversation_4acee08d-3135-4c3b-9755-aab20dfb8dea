{"project_info": {"project_number": "704360355803", "firebase_url": "https://done-dev-f0434.firebaseio.com", "project_id": "done-dev-f0434", "storage_bucket": "done-dev-f0434.appspot.com"}, "client": [{"client_info": {"mobilesdk_app_id": "1:704360355803:android:bd5dce6cd828958cebb9fe", "android_client_info": {"package_name": "co.doneservices.callsDemo"}}, "oauth_client": [{"client_id": "704360355803-r01q6so69hiv7j3j68itj1oadg85ttm1.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyBsax8oVFpr6UHN8-cyzClygHYx0JpS97s"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "704360355803-r01q6so69hiv7j3j68itj1oadg85ttm1.apps.googleusercontent.com", "client_type": 3}, {"client_id": "704360355803-24ubtkeqsen0c41432tag7ehtodhs9rb.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "co.doneservices.doneDemoCallsApp"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:704360355803:android:25bf6c8612273f69", "android_client_info": {"package_name": "co.doneservices.doneFlutter.dev"}}, "oauth_client": [{"client_id": "704360355803-7obv10gkk72sa17l6vp0gfmve182o68d.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "co.doneservices.doneFlutter.dev", "certificate_hash": "75f749ab8332afb27e4d476a6b9bf92b36ebe7fc"}}, {"client_id": "704360355803-7sjoogo5jqahfipe6q7jq59hj5dobhs1.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "co.doneservices.doneFlutter.dev", "certificate_hash": "fe8d87e44bb17df430f3ea40db40372f681012e6"}}, {"client_id": "704360355803-9kgba4gi95prj2suhjj27jvj9tpgti1d.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "co.doneservices.doneFlutter.dev", "certificate_hash": "1269ee079286463d001099d7f6fdb5962a1b4e6b"}}, {"client_id": "704360355803-in6qbol21n5s4bc27r05ce3chgcefmji.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "co.doneservices.doneFlutter.dev", "certificate_hash": "2143dabd0acc59dbb1ef6fbfb94ccd5c14c56fe3"}}, {"client_id": "704360355803-khg97fim5nr3f0nm3pls276ffidnj8sq.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "co.doneservices.doneFlutter.dev", "certificate_hash": "ffefabfe06c94d4d0961f5f2e02d839ca33644ed"}}, {"client_id": "704360355803-qv7127bv2nmdbofgd49shho1lq5tj64q.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "co.doneservices.doneFlutter.dev", "certificate_hash": "124697dfa4fe94e88dffbb5da96a9761cc4584e8"}}, {"client_id": "704360355803-uknbtfgvg322oqsqg21vptftkk16e8ts.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "co.doneservices.doneFlutter.dev", "certificate_hash": "64aa8bd9b6b45a2c4f18fc501fb8a75105e7a6c5"}}, {"client_id": "704360355803-r01q6so69hiv7j3j68itj1oadg85ttm1.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyBsax8oVFpr6UHN8-cyzClygHYx0JpS97s"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "704360355803-r01q6so69hiv7j3j68itj1oadg85ttm1.apps.googleusercontent.com", "client_type": 3}, {"client_id": "704360355803-24ubtkeqsen0c41432tag7ehtodhs9rb.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "co.doneservices.doneDemoCallsApp"}}]}}}], "configuration_version": "1"}