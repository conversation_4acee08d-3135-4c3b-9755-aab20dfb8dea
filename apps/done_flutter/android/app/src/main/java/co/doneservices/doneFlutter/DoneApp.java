package co.doneservices.doneFlutter;

import android.app.Application;
import io.maido.intercom.IntercomFlutterPlugin;

public class DoneApp extends Application {
    @Override
    public void onCreate() {
    super.onCreate();

    // Initialize the Intercom SDK here also as Android requires to initialize it in the onCreate of
    // the application.
    final boolean isProduction = BuildConfig.FLAVOR.equals("prod");
    final String appId = isProduction ? "gn8o0bfp" : "lfwvpegs";
    final String androidApiKey = isProduction ? "android_sdk-eb800e26d8cb8a63ccc68e29c06dc59953857bdb" : "android_sdk-5e330793bd6ca3339391cb7b9ed9a358999b4c74";
    IntercomFlutterPlugin.initSdk(this, appId, androidApiKey);
  }
}