PODS:
  - agora_rtc_engine (6.3.2):
    - AgoraIrisRTC_iOS (= 4.3.2-build.1)
    - AgoraRtcEngine_iOS (= 4.3.2)
    - Flutter
  - AgoraIrisRTC_iOS (4.3.2-build.1)
  - AgoraRtcEngine_iOS (4.3.2):
    - AgoraRtcEngine_iOS/AIAEC (= 4.3.2)
    - AgoraRtcEngine_iOS/AINS (= 4.3.2)
    - AgoraRtcEngine_iOS/AudioBeauty (= 4.3.2)
    - AgoraRtcEngine_iOS/ClearVision (= 4.3.2)
    - AgoraRtcEngine_iOS/ContentInspect (= 4.3.2)
    - AgoraRtcEngine_iOS/FaceCapture (= 4.3.2)
    - AgoraRtcEngine_iOS/FaceDetection (= 4.3.2)
    - AgoraRtcEngine_iOS/LipSync (= 4.3.2)
    - AgoraRtcEngine_iOS/ReplayKit (= 4.3.2)
    - AgoraRtcEngine_iOS/RtcBasic (= 4.3.2)
    - AgoraRtcEngine_iOS/SpatialAudio (= 4.3.2)
    - AgoraRtcEngine_iOS/VideoAv1CodecDec (= 4.3.2)
    - AgoraRtcEngine_iOS/VideoAv1CodecEnc (= 4.3.2)
    - AgoraRtcEngine_iOS/VideoCodecDec (= 4.3.2)
    - AgoraRtcEngine_iOS/VideoCodecEnc (= 4.3.2)
    - AgoraRtcEngine_iOS/VirtualBackground (= 4.3.2)
    - AgoraRtcEngine_iOS/VQA (= 4.3.2)
  - AgoraRtcEngine_iOS/AIAEC (4.3.2)
  - AgoraRtcEngine_iOS/AINS (4.3.2)
  - AgoraRtcEngine_iOS/AudioBeauty (4.3.2)
  - AgoraRtcEngine_iOS/ClearVision (4.3.2)
  - AgoraRtcEngine_iOS/ContentInspect (4.3.2)
  - AgoraRtcEngine_iOS/FaceCapture (4.3.2)
  - AgoraRtcEngine_iOS/FaceDetection (4.3.2)
  - AgoraRtcEngine_iOS/LipSync (4.3.2)
  - AgoraRtcEngine_iOS/ReplayKit (4.3.2)
  - AgoraRtcEngine_iOS/RtcBasic (4.3.2)
  - AgoraRtcEngine_iOS/SpatialAudio (4.3.2)
  - AgoraRtcEngine_iOS/VideoAv1CodecDec (4.3.2)
  - AgoraRtcEngine_iOS/VideoAv1CodecEnc (4.3.2)
  - AgoraRtcEngine_iOS/VideoCodecDec (4.3.2)
  - AgoraRtcEngine_iOS/VideoCodecEnc (4.3.2)
  - AgoraRtcEngine_iOS/VirtualBackground (4.3.2)
  - AgoraRtcEngine_iOS/VQA (4.3.2)
  - app_tracking_transparency (0.0.1):
    - Flutter
  - AppAuth (1.7.5):
    - AppAuth/Core (= 1.7.5)
    - AppAuth/ExternalUserAgent (= 1.7.5)
  - AppAuth/Core (1.7.5)
  - AppAuth/ExternalUserAgent (1.7.5):
    - AppAuth/Core
  - ApsEnvironment (1.0.2)
  - audio_session (0.0.1):
    - Flutter
  - cloud_firestore (5.0.0):
    - Firebase/Firestore (= 10.27.0)
    - firebase_core
    - Flutter
  - cloud_functions (5.0.0):
    - Firebase/Functions (= 10.27.0)
    - firebase_core
    - Flutter
  - CryptoSwift (1.8.2)
  - device_info_plus (0.0.1):
    - Flutter
  - facebook_app_events (0.0.1):
    - FBAudienceNetwork (~> 6.15)
    - FBSDKCoreKit (~> 17.0)
    - Flutter
  - FBAEMKit (17.0.2):
    - FBSDKCoreKit_Basics (= 17.0.2)
  - FBAudienceNetwork (6.15.1)
  - FBSDKCoreKit (17.0.2):
    - FBAEMKit (= 17.0.2)
    - FBSDKCoreKit_Basics (= 17.0.2)
  - FBSDKCoreKit_Basics (17.0.2)
  - Firebase/Analytics (10.27.0):
    - Firebase/Core
  - Firebase/Auth (10.27.0):
    - Firebase/CoreOnly
    - FirebaseAuth (~> 10.27.0)
  - Firebase/Core (10.27.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 10.27.0)
  - Firebase/CoreOnly (10.27.0):
    - FirebaseCore (= 10.27.0)
  - Firebase/Firestore (10.27.0):
    - Firebase/CoreOnly
    - FirebaseFirestore (~> 10.27.0)
  - Firebase/Functions (10.27.0):
    - Firebase/CoreOnly
    - FirebaseFunctions (~> 10.27.0)
  - Firebase/Messaging (10.27.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 10.27.0)
  - Firebase/RemoteConfig (10.27.0):
    - Firebase/CoreOnly
    - FirebaseRemoteConfig (~> 10.27.0)
  - Firebase/Storage (10.27.0):
    - Firebase/CoreOnly
    - FirebaseStorage (~> 10.27.0)
  - firebase_analytics (11.0.0):
    - Firebase/Analytics (= 10.27.0)
    - firebase_core
    - Flutter
  - firebase_auth (5.0.0):
    - Firebase/Auth (= 10.27.0)
    - firebase_core
    - Flutter
  - firebase_core (3.0.0):
    - Firebase/CoreOnly (= 10.27.0)
    - Flutter
  - firebase_messaging (15.0.0):
    - Firebase/Messaging (= 10.27.0)
    - firebase_core
    - Flutter
  - firebase_remote_config (5.0.0):
    - Firebase/RemoteConfig (= 10.27.0)
    - firebase_core
    - Flutter
  - firebase_storage (12.0.0):
    - Firebase/Storage (= 10.27.0)
    - firebase_core
    - Flutter
  - FirebaseABTesting (10.29.0):
    - FirebaseCore (~> 10.0)
  - FirebaseAnalytics (10.27.0):
    - FirebaseAnalytics/AdIdSupport (= 10.27.0)
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseAnalytics/AdIdSupport (10.27.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleAppMeasurement (= 10.27.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseAppCheckInterop (10.29.0)
  - FirebaseAuth (10.27.0):
    - FirebaseAppCheckInterop (~> 10.17)
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GTMSessionFetcher/Core (< 4.0, >= 2.1)
    - RecaptchaInterop (~> 100.0)
  - FirebaseAuthInterop (10.29.0)
  - FirebaseCore (10.27.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.12)
    - GoogleUtilities/Logger (~> 7.12)
  - FirebaseCoreExtension (10.27.0):
    - FirebaseCore (~> 10.0)
  - FirebaseCoreInternal (10.29.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseFirestore (10.27.0):
    - FirebaseFirestoreBinary (= 10.27.0)
  - FirebaseFirestoreAbseilBinary (1.2024011601.0)
  - FirebaseFirestoreBinary (10.27.0):
    - FirebaseCore (= 10.27.0)
    - FirebaseCoreExtension (= 10.27.0)
    - FirebaseFirestoreInternalBinary (= 10.27.0)
    - FirebaseSharedSwift (= 10.27.0)
  - FirebaseFirestoreGRPCBoringSSLBinary (1.62.1)
  - FirebaseFirestoreGRPCCoreBinary (1.62.1):
    - FirebaseFirestoreAbseilBinary (= 1.2024011601.0)
    - FirebaseFirestoreGRPCBoringSSLBinary (= 1.62.1)
  - FirebaseFirestoreGRPCCPPBinary (1.62.1):
    - FirebaseFirestoreAbseilBinary (= 1.2024011601.0)
    - FirebaseFirestoreGRPCCoreBinary (= 1.62.1)
  - FirebaseFirestoreInternalBinary (10.27.0):
    - FirebaseCore (= 10.27.0)
    - FirebaseFirestoreAbseilBinary (= 1.2024011601.0)
    - FirebaseFirestoreGRPCCPPBinary (= 1.62.1)
    - leveldb-library (~> 1.22)
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseFunctions (10.27.0):
    - FirebaseAppCheckInterop (~> 10.10)
    - FirebaseAuthInterop (~> 10.25)
    - FirebaseCore (~> 10.0)
    - FirebaseCoreExtension (~> 10.0)
    - FirebaseMessagingInterop (~> 10.0)
    - FirebaseSharedSwift (~> 10.0)
    - GTMSessionFetcher/Core (< 4.0, >= 2.1)
  - FirebaseInstallations (10.29.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseMessaging (10.27.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.3)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Reachability (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseMessagingInterop (10.29.0)
  - FirebaseRemoteConfig (10.27.0):
    - FirebaseABTesting (~> 10.0)
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - FirebaseRemoteConfigInterop (~> 10.23)
    - FirebaseSharedSwift (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseRemoteConfigInterop (10.29.0)
  - FirebaseSharedSwift (10.27.0)
  - FirebaseStorage (10.27.0):
    - FirebaseAppCheckInterop (~> 10.0)
    - FirebaseAuthInterop (~> 10.25)
    - FirebaseCore (~> 10.0)
    - FirebaseCoreExtension (~> 10.0)
    - GoogleUtilities/Environment (~> 7.12)
    - GTMSessionFetcher/Core (< 4.0, >= 2.1)
  - Flutter (1.0.0)
  - flutter_callkeep (0.3.0):
    - CryptoSwift
    - Flutter
  - flutter_webrtc (0.9.36):
    - Flutter
    - WebRTC-SDK (= 114.5735.10)
  - google_sign_in_ios (0.0.1):
    - AppAuth (>= 1.7.4)
    - Flutter
    - FlutterMacOS
    - GoogleSignIn (~> 7.1)
    - GTMSessionFetcher (>= 3.4.0)
  - GoogleAppMeasurement (10.27.0):
    - GoogleAppMeasurement/AdIdSupport (= 10.27.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleAppMeasurement/AdIdSupport (10.27.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 10.27.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (10.27.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleDataTransport (9.4.1):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleSignIn (7.1.0):
    - AppAuth (< 2.0, >= 1.7.3)
    - GTMAppAuth (< 5.0, >= 4.1.1)
    - GTMSessionFetcher/Core (~> 3.3)
  - GoogleUtilities/AppDelegateSwizzler (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (7.13.3):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.13.3)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/Reachability (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMAppAuth (4.1.1):
    - AppAuth/Core (~> 1.7)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3)
  - GTMSessionFetcher (3.5.0):
    - GTMSessionFetcher/Full (= 3.5.0)
  - GTMSessionFetcher/Core (3.5.0)
  - GTMSessionFetcher/Full (3.5.0):
    - GTMSessionFetcher/Core
  - image_downloader (0.0.1):
    - Flutter
  - image_picker_ios (0.0.1):
    - Flutter
  - Intercom (17.0.4)
  - intercom_flutter (9.0.0):
    - Flutter
    - Intercom (= 17.0.4)
  - iris_method_channel (0.0.1):
    - Flutter
  - just_audio (0.0.1):
    - Flutter
  - leveldb-library (1.22.5)
  - nanopb (2.30910.0):
    - nanopb/decode (= 2.30910.0)
    - nanopb/encode (= 2.30910.0)
  - nanopb/decode (2.30910.0)
  - nanopb/encode (2.30910.0)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - pointer_interceptor_ios (0.0.1):
    - Flutter
  - PromisesObjC (2.4.0)
  - proximity_screen_lock_ios (0.0.1):
    - Flutter
  - RecaptchaInterop (100.0.0)
  - Sentry/HybridSDK (8.25.2)
  - sentry_flutter (8.2.0):
    - Flutter
    - FlutterMacOS
    - Sentry/HybridSDK (= 8.25.2)
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sign_in_with_apple (0.0.1):
    - Flutter
  - sqflite (0.0.3):
    - Flutter
    - FlutterMacOS
  - url_launcher_ios (0.0.1):
    - Flutter
  - UUIDNamespaces (1.0.0)
  - wakelock_plus (0.0.1):
    - Flutter
  - WebRTC-SDK (114.5735.10)

DEPENDENCIES:
  - agora_rtc_engine (from `.symlinks/plugins/agora_rtc_engine/ios`)
  - app_tracking_transparency (from `.symlinks/plugins/app_tracking_transparency/ios`)
  - ApsEnvironment (~> 1.0)
  - audio_session (from `.symlinks/plugins/audio_session/ios`)
  - cloud_firestore (from `.symlinks/plugins/cloud_firestore/ios`)
  - cloud_functions (from `.symlinks/plugins/cloud_functions/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - facebook_app_events (from `.symlinks/plugins/facebook_app_events/ios`)
  - firebase_analytics (from `.symlinks/plugins/firebase_analytics/ios`)
  - firebase_auth (from `.symlinks/plugins/firebase_auth/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - firebase_remote_config (from `.symlinks/plugins/firebase_remote_config/ios`)
  - firebase_storage (from `.symlinks/plugins/firebase_storage/ios`)
  - FirebaseFirestore (from `https://github.com/invertase/firestore-ios-sdk-frameworks.git`, tag `10.27.0`)
  - Flutter (from `Flutter`)
  - flutter_callkeep (from `.symlinks/plugins/flutter_callkeep/ios`)
  - flutter_webrtc (from `.symlinks/plugins/flutter_webrtc/ios`)
  - google_sign_in_ios (from `.symlinks/plugins/google_sign_in_ios/darwin`)
  - image_downloader (from `.symlinks/plugins/image_downloader/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - intercom_flutter (from `.symlinks/plugins/intercom_flutter/ios`)
  - iris_method_channel (from `.symlinks/plugins/iris_method_channel/ios`)
  - just_audio (from `.symlinks/plugins/just_audio/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - pointer_interceptor_ios (from `.symlinks/plugins/pointer_interceptor_ios/ios`)
  - proximity_screen_lock_ios (from `.symlinks/plugins/proximity_screen_lock_ios/ios`)
  - sentry_flutter (from `.symlinks/plugins/sentry_flutter/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sign_in_with_apple (from `.symlinks/plugins/sign_in_with_apple/ios`)
  - sqflite (from `.symlinks/plugins/sqflite/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - UUIDNamespaces (~> 1.0)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)

SPEC REPOS:
  trunk:
    - AgoraIrisRTC_iOS
    - AgoraRtcEngine_iOS
    - AppAuth
    - ApsEnvironment
    - CryptoSwift
    - FBAEMKit
    - FBAudienceNetwork
    - FBSDKCoreKit
    - FBSDKCoreKit_Basics
    - Firebase
    - FirebaseABTesting
    - FirebaseAnalytics
    - FirebaseAppCheckInterop
    - FirebaseAuth
    - FirebaseAuthInterop
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseFirestoreAbseilBinary
    - FirebaseFirestoreBinary
    - FirebaseFirestoreGRPCBoringSSLBinary
    - FirebaseFirestoreGRPCCoreBinary
    - FirebaseFirestoreGRPCCPPBinary
    - FirebaseFirestoreInternalBinary
    - FirebaseFunctions
    - FirebaseInstallations
    - FirebaseMessaging
    - FirebaseMessagingInterop
    - FirebaseRemoteConfig
    - FirebaseRemoteConfigInterop
    - FirebaseSharedSwift
    - FirebaseStorage
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleSignIn
    - GoogleUtilities
    - GTMAppAuth
    - GTMSessionFetcher
    - Intercom
    - leveldb-library
    - nanopb
    - PromisesObjC
    - RecaptchaInterop
    - Sentry
    - UUIDNamespaces
    - WebRTC-SDK

EXTERNAL SOURCES:
  agora_rtc_engine:
    :path: ".symlinks/plugins/agora_rtc_engine/ios"
  app_tracking_transparency:
    :path: ".symlinks/plugins/app_tracking_transparency/ios"
  audio_session:
    :path: ".symlinks/plugins/audio_session/ios"
  cloud_firestore:
    :path: ".symlinks/plugins/cloud_firestore/ios"
  cloud_functions:
    :path: ".symlinks/plugins/cloud_functions/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  facebook_app_events:
    :path: ".symlinks/plugins/facebook_app_events/ios"
  firebase_analytics:
    :path: ".symlinks/plugins/firebase_analytics/ios"
  firebase_auth:
    :path: ".symlinks/plugins/firebase_auth/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  firebase_remote_config:
    :path: ".symlinks/plugins/firebase_remote_config/ios"
  firebase_storage:
    :path: ".symlinks/plugins/firebase_storage/ios"
  FirebaseFirestore:
    :git: https://github.com/invertase/firestore-ios-sdk-frameworks.git
    :tag: 10.27.0
  Flutter:
    :path: Flutter
  flutter_callkeep:
    :path: ".symlinks/plugins/flutter_callkeep/ios"
  flutter_webrtc:
    :path: ".symlinks/plugins/flutter_webrtc/ios"
  google_sign_in_ios:
    :path: ".symlinks/plugins/google_sign_in_ios/darwin"
  image_downloader:
    :path: ".symlinks/plugins/image_downloader/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  intercom_flutter:
    :path: ".symlinks/plugins/intercom_flutter/ios"
  iris_method_channel:
    :path: ".symlinks/plugins/iris_method_channel/ios"
  just_audio:
    :path: ".symlinks/plugins/just_audio/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  pointer_interceptor_ios:
    :path: ".symlinks/plugins/pointer_interceptor_ios/ios"
  proximity_screen_lock_ios:
    :path: ".symlinks/plugins/proximity_screen_lock_ios/ios"
  sentry_flutter:
    :path: ".symlinks/plugins/sentry_flutter/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sign_in_with_apple:
    :path: ".symlinks/plugins/sign_in_with_apple/ios"
  sqflite:
    :path: ".symlinks/plugins/sqflite/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"

CHECKOUT OPTIONS:
  FirebaseFirestore:
    :git: https://github.com/invertase/firestore-ios-sdk-frameworks.git
    :tag: 10.27.0

SPEC CHECKSUMS:
  agora_rtc_engine: 14229f6b2fb40ab33cabba2b9f9c5b1c454ecb8e
  AgoraIrisRTC_iOS: 3cb15344a503a1a35ab6d60481732a8beeda98fe
  AgoraRtcEngine_iOS: eaa97751fcfe1b50d9b067e0df92752a6a5f899f
  app_tracking_transparency: e169b653478da7bb15a6c61209015378ca73e375
  AppAuth: 501c04eda8a8d11f179dbe8637b7a91bb7e5d2fa
  ApsEnvironment: 4dc1d8331494b5878cd1f9dd1f540304767602e8
  audio_session: 088d2483ebd1dc43f51d253d4a1c517d9a2e7207
  cloud_firestore: 8cbdffc6eec66f2c2e67f19e4c6f9a872c222d19
  cloud_functions: 800466fd033f2e623a27c006add7be953df28227
  CryptoSwift: c63a805d8bb5e5538e88af4e44bb537776af11ea
  device_info_plus: 97af1d7e84681a90d0693e63169a5d50e0839a0d
  facebook_app_events: acb7c266406d3e3592bcf846d7184172ecfd6492
  FBAEMKit: 619f96ea65427e8afca240d5b0f4703738dfdf5c
  FBAudienceNetwork: 80871c3b3057e89ab36d9eb79344be20377e3994
  FBSDKCoreKit: a5f384db2e9ee84e98494fed8f983d2bd79accff
  FBSDKCoreKit_Basics: d35c775aaf243a2d731dfae7be3a74b1987285ab
  Firebase: 26b040b20866a55f55eb3611b9fcf3ae64816b86
  firebase_analytics: 921262f343360193f8376eb89c01fb5f15571ca2
  firebase_auth: 17ce83d25a062b2a594e5dfce91a4ad36e3739d5
  firebase_core: 5926464bbb028fef87d2443369b73ada2a8a3608
  firebase_messaging: 17bc029302b3342daa1c5905a1ee4258bcf47572
  firebase_remote_config: 57d29f82e7e4666349c1634a32568c877e3a079d
  firebase_storage: ff66671828d524fc9e36f30cd7ed1909036324c7
  FirebaseABTesting: d87f56707159bae64e269757a6e963d490f2eebe
  FirebaseAnalytics: f9211b719db260cc91aebee8bb539cb367d0dfd1
  FirebaseAppCheckInterop: 6a1757cfd4067d8e00fccd14fcc1b8fd78cfac07
  FirebaseAuth: 77a012b7e08042bf44d0db835ca2e86e6ca7bbd3
  FirebaseAuthInterop: 17db81e9b198afb0f95ce48c133825727eed55d3
  FirebaseCore: a2b95ae4ce7c83ceecfbbbe3b6f1cddc7415a808
  FirebaseCoreExtension: 4ec89dd0c6de93d6becde32122d68b7c35f6bf5d
  FirebaseCoreInternal: df84dd300b561c27d5571684f389bf60b0a5c934
  FirebaseFirestore: 6bc950c7b9f4aec24ad11c583b28201567a80977
  FirebaseFirestoreAbseilBinary: bd0451946879e0e01327c8efc35896bb273009c4
  FirebaseFirestoreBinary: c9850d2f59c0e0284300366d7b6cf27705bbbc17
  FirebaseFirestoreGRPCBoringSSLBinary: c3007e61593e4583ec3bb8b34ce654663663b690
  FirebaseFirestoreGRPCCoreBinary: 1243fc6604a52912694635f891916976924931ed
  FirebaseFirestoreGRPCCPPBinary: e447b3a7c060c0abfc4e2c9f6fb53bf7635ded66
  FirebaseFirestoreInternalBinary: a6fee1ed2c3d51c750c34a6ad879fc7308484f1a
  FirebaseFunctions: d95bacf9a201ebabd2df7d2412281538016a07dd
  FirebaseInstallations: 913cf60d0400ebd5d6b63a28b290372ab44590dd
  FirebaseMessaging: 585984d0a1df120617eb10b44cad8968b859815e
  FirebaseMessagingInterop: afa5a4da1d615d109cf9fcf37479af96f6c3623a
  FirebaseRemoteConfig: 37a2ba3c8c454be8553a41ba1a2f4a4f0b845670
  FirebaseRemoteConfigInterop: 6efda51fb5e2f15b16585197e26eaa09574e8a4d
  FirebaseSharedSwift: a03fe7a59ee646fef71099a887f774fe25d745b8
  FirebaseStorage: 255526c3d04c49874d7a5e3886964a79f77d6f33
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_callkeep: e94ee31d8797776cee91bf288c62ca4aa89fb5a8
  flutter_webrtc: b33475c3a57d59ff05bf87b4f5d3feceac63f291
  google_sign_in_ios: 07375bfbf2620bc93a602c0e27160d6afc6ead38
  GoogleAppMeasurement: f65fc137531af9ad647f1c0a42f3b6a4d3a98049
  GoogleDataTransport: 6c09b596d841063d76d4288cc2d2f42cc36e1e2a
  GoogleSignIn: d4281ab6cf21542b1cfaff85c191f230b399d2db
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  GTMAppAuth: f69bd07d68cd3b766125f7e072c45d7340dea0de
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  image_downloader: 73e190d6c9f286f2649554051348d9cb319cd4b3
  image_picker_ios: c560581cceedb403a6ff17f2f816d7fea1421fc1
  Intercom: 373e65416ba9864a797695551d4366cb734083c6
  intercom_flutter: 647c479b732f99b4788f702debe8a352b31b7d05
  iris_method_channel: 0617c689164d8154c020c50f63ca79f92b8a7b9d
  just_audio: baa7252489dbcf47a4c7cc9ca663e9661c99aafa
  leveldb-library: e8eadf9008a61f9e1dde3978c086d2b6d9b9dc28
  nanopb: 438bc412db1928dac798aa6fd75726007be04262
  package_info_plus: 58f0028419748fad15bf008b270aaa8e54380b1c
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  permission_handler_apple: 9878588469a2b0d0fc1e048d9f43605f92e6cec2
  pointer_interceptor_ios: 508241697ff0947f853c061945a8b822463947c1
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  proximity_screen_lock_ios: 4f6ea51910f155dac8f2dd97a243742cb7002cb4
  RecaptchaInterop: 7d1a4a01a6b2cb1610a47ef3f85f0c411434cb21
  Sentry: 51b056d96914a741f63eca774d118678b1eb05a1
  sentry_flutter: e8397d13e297a5d4b6be8a752e33140b21c5cc97
  share_plus: 8875f4f2500512ea181eef553c3e27dba5135aad
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  sign_in_with_apple: f3bf75217ea4c2c8b91823f225d70230119b8440
  sqflite: 673a0e54cc04b7d6dba8d24fb8095b31c3a99eec
  url_launcher_ios: 5334b05cef931de560670eeae103fd3e431ac3fe
  UUIDNamespaces: bb9d4af8dc3e5ff90068f2d8d42ad5b5c942d380
  wakelock_plus: 78ec7c5b202cab7761af8e2b2b3d0671be6c4ae1
  WebRTC-SDK: 8c0edd05b880a39648118192c252667ea06dea51

PODFILE CHECKSUM: a02d3af161cb3a0354d9eac0987b38db4ab8d545

COCOAPODS: 1.15.2
